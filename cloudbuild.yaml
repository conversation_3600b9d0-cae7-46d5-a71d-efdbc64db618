# Google Cloud Build configuration for Smart Customer Support Orchestrator
steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build', 
      '-t', 'gcr.io/$PROJECT_ID/customer-support-orchestrator:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/customer-support-orchestrator:latest',
      '.'
    ]

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/customer-support-orchestrator:$COMMIT_SHA']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/customer-support-orchestrator:latest']

  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args: [
      'run', 'deploy', 'customer-support-orchestrator',
      '--image', 'gcr.io/$PROJECT_ID/customer-support-orchestrator:$COMMIT_SHA',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--memory', '2Gi',
      '--cpu', '2',
      '--min-instances', '0',
      '--max-instances', '10',
      '--concurrency', '100',
      '--set-env-vars', 'GOOGLE_CLOUD_PROJECT=$PROJECT_ID,BIGQUERY_DATASET=customer_support,GOOGLE_CLOUD_REGION=us-central1,PORT=8080,PYTHONPATH=/app',
      '--timeout', '300',
      '--port', '8080'
    ]

# Store images in Container Registry
images:
  - 'gcr.io/$PROJECT_ID/customer-support-orchestrator:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/customer-support-orchestrator:latest'

# Build options
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

# Timeout for the entire build
timeout: '1200s'
