@echo off

REM Smart Customer Support Orchestrator Startup Script (Windows)
REM Resolves protobuf compatibility issues with Streamlit

echo 🚀 Starting Smart Customer Support Orchestrator...
echo 🔧 Setting protobuf compatibility mode...

REM Set environment variable to resolve protobuf compatibility issues
set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python

REM Start the application
python start_application.py

pause
