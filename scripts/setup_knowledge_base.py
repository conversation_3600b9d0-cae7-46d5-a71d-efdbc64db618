"""Setup script for creating BigQuery knowledge base and sample data."""

import os
import sys
from google.cloud import bigquery
from google.cloud import storage
import json
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.config import settings


def create_bigquery_dataset():
    """Create BigQuery dataset and tables for knowledge base."""
    client = bigquery.Client(project=settings.google_cloud_project)
    
    # Create dataset
    dataset_id = f"{settings.google_cloud_project}.{settings.bigquery_dataset}"
    dataset = bigquery.Dataset(dataset_id)
    dataset.location = settings.google_cloud_region
    dataset.description = "Customer support knowledge base and ticket storage"
    
    try:
        dataset = client.create_dataset(dataset, exists_ok=True)
        print(f"✅ Created dataset {dataset.dataset_id}")
    except Exception as e:
        print(f"❌ Error creating dataset: {e}")
        return False
    
    # Create knowledge base table
    knowledge_table_id = f"{dataset_id}.{settings.bigquery_table_knowledge}"
    knowledge_schema = [
        bigquery.SchemaField("document_id", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("title", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("content", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("category", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("tags", "STRING", mode="REPEATED"),
        bigquery.SchemaField("last_updated", "TIMESTAMP", mode="REQUIRED"),
        bigquery.SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
    ]
    
    knowledge_table = bigquery.Table(knowledge_table_id, schema=knowledge_schema)
    knowledge_table.description = "Knowledge base documents and FAQs"
    
    try:
        knowledge_table = client.create_table(knowledge_table, exists_ok=True)
        print(f"✅ Created knowledge table {knowledge_table.table_id}")
    except Exception as e:
        print(f"❌ Error creating knowledge table: {e}")
        return False
    
    # Create support tickets table
    tickets_table_id = f"{dataset_id}.{settings.bigquery_table_tickets}"
    tickets_schema = [
        bigquery.SchemaField("ticket_id", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("customer_id", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("customer_message", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("intent", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("sentiment", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("priority", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("final_response", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("status", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("escalated", "BOOLEAN", mode="REQUIRED"),
        bigquery.SchemaField("resolution_type", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("customer_satisfaction_score", "FLOAT", mode="NULLABLE"),
        bigquery.SchemaField("total_processing_time", "FLOAT", mode="REQUIRED"),
        bigquery.SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
        bigquery.SchemaField("completed_at", "TIMESTAMP", mode="NULLABLE"),
    ]
    
    tickets_table = bigquery.Table(tickets_table_id, schema=tickets_schema)
    tickets_table.description = "Historical support tickets for learning"
    
    try:
        tickets_table = client.create_table(tickets_table, exists_ok=True)
        print(f"✅ Created tickets table {tickets_table.table_id}")
    except Exception as e:
        print(f"❌ Error creating tickets table: {e}")
        return False
    
    # Create agent responses table
    responses_table_id = f"{dataset_id}.{settings.bigquery_table_responses}"
    responses_schema = [
        bigquery.SchemaField("response_id", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("ticket_id", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("agent_name", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("agent_result", "JSON", mode="REQUIRED"),
        bigquery.SchemaField("processing_time", "FLOAT", mode="REQUIRED"),
        bigquery.SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
    ]
    
    responses_table = bigquery.Table(responses_table_id, schema=responses_schema)
    responses_table.description = "Individual agent responses for analysis"
    
    try:
        responses_table = client.create_table(responses_table, exists_ok=True)
        print(f"✅ Created responses table {responses_table.table_id}")
    except Exception as e:
        print(f"❌ Error creating responses table: {e}")
        return False
    
    return True


def insert_sample_knowledge_data():
    """Insert sample knowledge base data."""
    client = bigquery.Client(project=settings.google_cloud_project)
    
    # Sample knowledge base documents
    sample_documents = [
        {
            "document_id": "doc_001",
            "title": "Return Policy",
            "content": "Our return policy allows customers to return items within 30 days of purchase for a full refund. Items must be in original condition with tags attached. To initiate a return, customers can contact support or use our online return portal. Refunds are processed within 5-7 business days to the original payment method.",
            "category": "policies",
            "tags": ["returns", "refunds", "policy"],
            "last_updated": datetime.utcnow(),
            "created_at": datetime.utcnow(),
        },
        {
            "document_id": "doc_002",
            "title": "Shipping Information",
            "content": "We offer free standard shipping on orders over $50. Standard shipping takes 3-5 business days. Express shipping (1-2 business days) is available for $9.99. International shipping is available to most countries with delivery times of 7-14 business days. Tracking information is provided via email once orders ship.",
            "category": "shipping",
            "tags": ["shipping", "delivery", "tracking"],
            "last_updated": datetime.utcnow(),
            "created_at": datetime.utcnow(),
        },
        {
            "document_id": "doc_003",
            "title": "Account Access Issues",
            "content": "If you're having trouble accessing your account, try resetting your password using the 'Forgot Password' link. If that doesn't work, clear your browser cache and cookies. For persistent issues, contact our technical support team. We can also help you recover accounts using your email address or phone number.",
            "category": "technical",
            "tags": ["account", "login", "password", "technical"],
            "last_updated": datetime.utcnow(),
            "created_at": datetime.utcnow(),
        },
        {
            "document_id": "doc_004",
            "title": "Product Defect Handling",
            "content": "If you receive a defective product, we sincerely apologize. Please contact us immediately with photos of the defect and your order number. We will arrange for a replacement or full refund, plus expedited shipping at no cost. For manufacturing defects, we also provide a 20% discount on your next order as compensation.",
            "category": "quality",
            "tags": ["defects", "quality", "replacement", "compensation"],
            "last_updated": datetime.utcnow(),
            "created_at": datetime.utcnow(),
        },
        {
            "document_id": "doc_005",
            "title": "Billing and Payment Issues",
            "content": "We accept all major credit cards, PayPal, and digital wallets. If you see an unrecognized charge, please check your order history first. Charges may appear under our parent company name. For billing disputes, we can provide detailed transaction records. Refunds for cancelled orders are processed within 3-5 business days.",
            "category": "billing",
            "tags": ["billing", "payment", "charges", "refunds"],
            "last_updated": datetime.utcnow(),
            "created_at": datetime.utcnow(),
        }
    ]
    
    # Insert documents
    table_id = f"{settings.google_cloud_project}.{settings.bigquery_dataset}.{settings.bigquery_table_knowledge}"
    
    try:
        errors = client.insert_rows_json(client.get_table(table_id), sample_documents)
        if errors:
            print(f"❌ Errors inserting knowledge data: {errors}")
            return False
        else:
            print(f"✅ Inserted {len(sample_documents)} knowledge documents")
            return True
    except Exception as e:
        print(f"❌ Error inserting knowledge data: {e}")
        return False


def insert_sample_ticket_data():
    """Insert sample historical ticket data."""
    client = bigquery.Client(project=settings.google_cloud_project)
    
    # Sample historical tickets
    sample_tickets = [
        {
            "ticket_id": "ticket_hist_001",
            "customer_id": "customer_001",
            "customer_message": "My order arrived damaged, need a replacement",
            "intent": "product_defect",
            "sentiment": "negative",
            "priority": "high",
            "final_response": "I sincerely apologize for the damaged item. I've arranged for an immediate replacement with expedited shipping at no cost, plus a 20% discount on your next order. You should receive the replacement within 2 business days.",
            "status": "completed",
            "escalated": False,
            "resolution_type": "replacement_with_compensation",
            "customer_satisfaction_score": 4.5,
            "total_processing_time": 2.3,
            "created_at": datetime.utcnow(),
            "completed_at": datetime.utcnow(),
        },
        {
            "ticket_id": "ticket_hist_002",
            "customer_id": "customer_002",
            "customer_message": "Can't log into my account, password reset not working",
            "intent": "account_access",
            "sentiment": "neutral",
            "priority": "medium",
            "final_response": "I can help you regain access to your account. I've manually reset your password and sent new login credentials to your registered email. Please also try clearing your browser cache. If you continue having issues, our technical team is standing by to assist.",
            "status": "completed",
            "escalated": False,
            "resolution_type": "technical_assistance",
            "customer_satisfaction_score": 4.0,
            "total_processing_time": 1.8,
            "created_at": datetime.utcnow(),
            "completed_at": datetime.utcnow(),
        },
        {
            "ticket_id": "ticket_hist_003",
            "customer_id": "customer_003",
            "customer_message": "Excellent service! Product exceeded expectations",
            "intent": "compliment",
            "sentiment": "very_positive",
            "priority": "low",
            "final_response": "Thank you so much for your wonderful feedback! We're thrilled that the product exceeded your expectations. Your kind words mean a lot to our team. We look forward to serving you again in the future!",
            "status": "completed",
            "escalated": False,
            "resolution_type": "appreciation_response",
            "customer_satisfaction_score": 5.0,
            "total_processing_time": 0.9,
            "created_at": datetime.utcnow(),
            "completed_at": datetime.utcnow(),
        }
    ]
    
    # Insert tickets
    table_id = f"{settings.google_cloud_project}.{settings.bigquery_dataset}.{settings.bigquery_table_tickets}"
    
    try:
        errors = client.insert_rows_json(client.get_table(table_id), sample_tickets)
        if errors:
            print(f"❌ Errors inserting ticket data: {errors}")
            return False
        else:
            print(f"✅ Inserted {len(sample_tickets)} historical tickets")
            return True
    except Exception as e:
        print(f"❌ Error inserting ticket data: {e}")
        return False


def create_storage_buckets():
    """Create Cloud Storage buckets for documents."""
    client = storage.Client(project=settings.google_cloud_project)
    
    buckets_to_create = [
        settings.storage_bucket,
        settings.storage_bucket_documents
    ]
    
    for bucket_name in buckets_to_create:
        try:
            bucket = client.bucket(bucket_name)
            if not bucket.exists():
                bucket = client.create_bucket(bucket_name, location=settings.google_cloud_region)
                print(f"✅ Created storage bucket: {bucket_name}")
            else:
                print(f"✅ Storage bucket already exists: {bucket_name}")
        except Exception as e:
            print(f"❌ Error with storage bucket {bucket_name}: {e}")
            return False
    
    return True


def main():
    """Main setup function."""
    print("🚀 Setting up Smart Customer Support Knowledge Base")
    print("=" * 60)
    
    print(f"Project: {settings.google_cloud_project}")
    print(f"Region: {settings.google_cloud_region}")
    print(f"Dataset: {settings.bigquery_dataset}")
    
    success = True
    
    # Create BigQuery dataset and tables
    print(f"\n📊 Creating BigQuery dataset and tables...")
    if not create_bigquery_dataset():
        success = False
    
    # Insert sample knowledge data
    print(f"\n📚 Inserting sample knowledge base data...")
    if not insert_sample_knowledge_data():
        success = False
    
    # Insert sample ticket data
    print(f"\n🎫 Inserting sample historical tickets...")
    if not insert_sample_ticket_data():
        success = False
    
    # Create storage buckets
    print(f"\n🪣 Creating Cloud Storage buckets...")
    if not create_storage_buckets():
        success = False
    
    if success:
        print(f"\n✅ Setup completed successfully!")
        print(f"\nNext steps:")
        print(f"1. Copy .env.example to .env and configure your settings")
        print(f"2. Run: python main.py --mode demo")
        print(f"3. Or start the API: python main.py --mode api")
    else:
        print(f"\n❌ Setup completed with errors. Please check the logs above.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
