#!/bin/bash

# Smart Customer Support Orchestrator - Local Docker Runner
echo "🚀 Building and running Smart Customer Support Orchestrator locally..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found. Please create it with your configuration."
    exit 1
fi

print_status "Checking Google Cloud credentials..."

# Check for Google Cloud credentials
if [ ! -f "src/vertexai.json" ]; then
    print_warning "Google Cloud service account key not found at src/vertexai.json"
    print_warning "Voice features may not work without proper credentials"
fi

# Stop any existing container
print_status "Stopping any existing containers..."
docker-compose down 2>/dev/null || true

# Build the Docker image
print_status "Building Docker image..."
if docker build -t smart-customer-support:local .; then
    print_success "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Run the container
print_status "Starting container on port 8080..."
if docker run -d \
    --name smart-customer-support-local \
    --env-file .env \
    -p 8080:8080 \
    -v "$(pwd)/src/vertexai.json:/app/src/vertexai.json:ro" \
    smart-customer-support:local; then
    
    print_success "Container started successfully!"
    
    # Wait a moment for the container to start
    print_status "Waiting for application to start..."
    sleep 5
    
    # Check if the application is responding
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        print_success "Application is healthy and ready!"
        echo ""
        echo "🌐 Access your application at:"
        echo "   • Main Interface: http://localhost:8080"
        echo "   • Voice Interface: http://localhost:8080/voice.html"
        echo "   • API Documentation: http://localhost:8080/docs"
        echo "   • Health Check: http://localhost:8080/health"
        echo ""
        echo "📋 Useful commands:"
        echo "   • View logs: docker logs smart-customer-support-local -f"
        echo "   • Stop container: docker stop smart-customer-support-local"
        echo "   • Remove container: docker rm smart-customer-support-local"
        echo ""
        print_status "Container is running in the background. Use 'docker logs smart-customer-support-local -f' to view logs."
    else
        print_warning "Application may still be starting up. Check logs with:"
        echo "docker logs smart-customer-support-local -f"
    fi
else
    print_error "Failed to start container"
    exit 1
fi
