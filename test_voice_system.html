<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🎤 Voice System Test</h1>
    
    <div class="test-section">
        <h2>1. API Health Check</h2>
        <button class="test-button" onclick="testHealth()">Test Health</button>
        <div id="healthResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>2. Customer Lookup Test</h2>
        <button class="test-button" onclick="testCustomerLookup('<EMAIL>')">Test Max (Found)</button>
        <button class="test-button" onclick="testCustomerLookup('<EMAIL>')">Test Not Found</button>
        <div id="lookupResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>3. Text-to-Speech Test</h2>
        <button class="test-button" onclick="testTTS()">Test TTS</button>
        <div id="ttsResult" class="result" style="display: none;"></div>
        <audio id="ttsAudio" controls style="display: none; margin-top: 10px;"></audio>
    </div>

    <div class="test-section">
        <h2>4. Voice Introduction Test</h2>
        <button class="test-button" onclick="testIntroduction()">Test Introduction</button>
        <div id="introResult" class="result" style="display: none;"></div>
        <audio id="introAudio" controls style="display: none; margin-top: 10px;"></audio>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';

        async function testHealth() {
            const resultDiv = document.getElementById('healthResult');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const result = await response.json();
                
                resultDiv.textContent = `✅ Health Check Passed\n${JSON.stringify(result, null, 2)}`;
                resultDiv.className = 'result success';
                resultDiv.style.display = 'block';
            } catch (error) {
                resultDiv.textContent = `❌ Health Check Failed: ${error.message}`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        async function testCustomerLookup(email) {
            const resultDiv = document.getElementById('lookupResult');
            try {
                const response = await fetch(`${API_BASE}/voice/customer-lookup`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ customer_email: email })
                });
                
                const result = await response.json();
                
                if (result.found) {
                    resultDiv.textContent = `✅ Customer Found: ${result.customer.name}\n${JSON.stringify(result, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `ℹ️ Customer Not Found\n${JSON.stringify(result, null, 2)}`;
                    resultDiv.className = 'result success';
                }
                resultDiv.style.display = 'block';
            } catch (error) {
                resultDiv.textContent = `❌ Lookup Failed: ${error.message}`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        async function testTTS() {
            const resultDiv = document.getElementById('ttsResult');
            const audioElement = document.getElementById('ttsAudio');
            
            try {
                const response = await fetch(`${API_BASE}/voice/text-to-speech`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text: "Hello! This is Angela, your AI customer support assistant. How can I help you today?" })
                });
                
                if (response.ok) {
                    const audioBlob = await response.blob();
                    const audioUrl = URL.createObjectURL(audioBlob);
                    audioElement.src = audioUrl;
                    audioElement.style.display = 'block';
                    
                    resultDiv.textContent = '✅ Text-to-Speech Generated Successfully';
                    resultDiv.className = 'result success';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                resultDiv.style.display = 'block';
            } catch (error) {
                resultDiv.textContent = `❌ TTS Failed: ${error.message}`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        async function testIntroduction() {
            const resultDiv = document.getElementById('introResult');
            const audioElement = document.getElementById('introAudio');
            
            try {
                const response = await fetch(`${API_BASE}/voice/introduction`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        customer_email: "<EMAIL>",
                        customer_name: "Max"
                    })
                });
                
                if (response.ok) {
                    const audioBlob = await response.blob();
                    const audioUrl = URL.createObjectURL(audioBlob);
                    audioElement.src = audioUrl;
                    audioElement.style.display = 'block';
                    
                    const angelaMessage = response.headers.get('X-Angela-Message');
                    
                    resultDiv.textContent = `✅ Introduction Generated Successfully\nAngela's Message: ${angelaMessage}`;
                    resultDiv.className = 'result success';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                resultDiv.style.display = 'block';
            } catch (error) {
                resultDiv.textContent = `❌ Introduction Failed: ${error.message}`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }
    </script>
</body>
</html>
