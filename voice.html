<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Angela - Voice Customer Support</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        .avatar {
            font-size: 4rem;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .avatar.talking {
            transform: scale(1.1);
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1.1); }
            50% { transform: scale(1.2); }
        }

        h1 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .subtitle {
            color: #718096;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }

        .customer-info {
            margin-bottom: 30px;
        }

        .customer-info input {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .customer-info input:focus {
            outline: none;
            border-color: #667eea;
        }

        .controls {
            margin: 30px 0;
        }

        .btn {
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            font-size: 14px;
            padding: 12px 20px;
        }

        .btn.secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }

        .btn.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .btn.danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .status {
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .status.listening {
            background: #e6fffa;
            color: #234e52;
            border: 2px solid #81e6d9;
        }

        .status.speaking {
            background: #fef5e7;
            color: #744210;
            border: 2px solid #f6e05e;
        }

        .status.processing {
            background: #e6f3ff;
            color: #2a4365;
            border: 2px solid #63b3ed;
        }

        .conversation {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            text-align: left;
        }

        .message {
            margin: 15px 0;
            padding: 12px 16px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: #667eea;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }

        .message.angela {
            background: #f7fafc;
            color: #2d3748;
            border: 1px solid #e2e8f0;
            border-bottom-left-radius: 5px;
        }

        .intro-screen {
            display: block;
        }

        .main-screen {
            display: none;
        }

        .customer-identification {
            background: #f0fff4;
            border: 2px solid #9ae6b4;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            color: #22543d;
        }

        .customer-identification.not-found {
            background: #fff5f5;
            border-color: #feb2b2;
            color: #742a2a;
        }

        .help-text {
            font-size: 0.9rem;
            color: #718096;
            margin-top: 15px;
            line-height: 1.5;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
                margin: 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .avatar {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Introduction Screen -->
        <div class="intro-screen" id="introScreen">
            <div class="avatar">👩‍💼</div>
            <h1>Meet Angela</h1>
            <p class="subtitle">Your AI Customer Support Assistant</p>
            
            <div class="customer-info">
                <input type="email" id="customerEmail" placeholder="Enter your email address (recommended)" />
                <input type="text" id="customerName" placeholder="Or enter your name" />
                <input type="text" id="customerId" placeholder="Customer ID (if known)" />
            </div>

            <div id="customerPreview" class="customer-identification" style="display: none; margin: 15px 0;">
                <div id="customerPreviewContent"></div>
            </div>

            <div class="help-text" style="margin-bottom: 20px;">
                <strong>💡 Tip:</strong> Enter your email address for the best experience. Angela can instantly access your account details and order history.
            </div>
            
            <div class="controls">
                <button class="btn primary" id="startCallBtn">
                    📞 Start Voice Call
                </button>
            </div>
            
            <p class="help-text">
                Angela will introduce herself and verify your identity using your name or email address. 
                She can access your account information and help with orders, refunds, and support requests.
            </p>
        </div>

        <!-- Main Voice Interface -->
        <div class="main-screen" id="mainScreen">
            <div class="avatar" id="avatar">👩‍💼</div>
            <h1>Angela</h1>
            <p class="subtitle">Customer Support Assistant</p>
            
            <div id="customerIdentification" class="customer-identification" style="display: none;">
                <strong>Customer Identified:</strong> <span id="identifiedCustomer"></span>
            </div>
            
            <div class="status" id="status">Ready to help you</div>
            
            <div class="controls">
                <button class="btn primary" id="recordBtn">
                    🎤 Hold to Speak
                </button>
                <button class="btn secondary" id="autoListenToggle">
                    🔄 Auto-Listen: ON
                </button>
                <button class="btn danger" id="endCallBtn">
                    📞 End Call
                </button>
            </div>
            
            <div class="conversation" id="conversation"></div>
        </div>
    </div>

    <script>
        // Add global error handler
        window.addEventListener('error', (e) => {
            console.error('JavaScript Error:', e.error);
            console.error('Error details:', {
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno
            });
        });

        // Dynamically determine API base URL
        const API_BASE = window.location.origin;
        let mediaRecorder;
        let audioChunks = [];
        let isRecording = false;
        let conversationHistory = [];
        let currentCustomerId = '';
        let currentCustomerName = '';
        let currentConversationId = '';
        let isListeningForFollowUp = false;
        let followUpTimeout = null;
        let autoListenEnabled = true;
        let conversationActive = false;

        // Start the call and play Angela's introduction
        async function startCall() {
            const customerName = document.getElementById('customerName').value.trim();
            const customerEmail = document.getElementById('customerEmail').value.trim();
            const customerId = document.getElementById('customerId').value.trim();
            
            // Store customer info
            currentCustomerName = customerName;
            currentCustomerId = customerId;

            // Enable conversation mode
            conversationActive = true;

            // Switch to main screen
            document.getElementById('introScreen').style.display = 'none';
            document.getElementById('mainScreen').style.display = 'block';
            
            // Update status
            updateStatus('Angela is introducing herself...', 'speaking');
            
            try {
                // Get Angela's introduction
                const response = await fetch(`${API_BASE}/voice/introduction`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_name: customerName,
                        customer_email: customerEmail,
                        customer_id: customerId
                    })
                });
                
                if (response.ok) {
                    const audioBlob = await response.blob();
                    const audioUrl = URL.createObjectURL(audioBlob);
                    const audio = new Audio(audioUrl);
                    
                    // Get introduction details from headers
                    const introText = response.headers.get('X-Angela-Message') || 'Hello! I\'m Angela from customer support.';
                    const customerIdentified = response.headers.get('X-Customer-Identified') === 'true';
                    const identifiedCustomerId = response.headers.get('X-Customer-ID');
                    const identifiedCustomerName = response.headers.get('X-Customer-Name');
                    const identifiedCustomerEmail = response.headers.get('X-Customer-Email');
                    const customerDetailsStr = response.headers.get('X-Customer-Details');

                    // Parse customer details
                    let customerDetails = {};
                    try {
                        if (customerDetailsStr && customerDetailsStr !== '{}') {
                            customerDetails = JSON.parse(customerDetailsStr.replace(/'/g, '"'));
                        }
                    } catch (e) {
                        console.warn('Could not parse customer details:', e);
                    }

                    // Update customer ID if identified
                    if (identifiedCustomerId && identifiedCustomerId !== 'unknown') {
                        currentCustomerId = identifiedCustomerId;
                    }

                    // Update customer name if identified
                    if (identifiedCustomerName) {
                        currentCustomerName = identifiedCustomerName;
                    }

                    // Show customer identification status
                    const identificationDiv = document.getElementById('customerIdentification');
                    const identifiedSpan = document.getElementById('identifiedCustomer');

                    if (customerIdentified && customerDetails.name) {
                        // Customer found in database
                        let displayText = customerDetails.name;
                        if (customerDetails.customer_type) {
                            displayText += ` (${customerDetails.customer_type})`;
                        }
                        if (customerDetails.total_orders > 0) {
                            displayText += ` • ${customerDetails.total_orders} orders`;
                        }

                        identifiedSpan.innerHTML = `<strong>${displayText}</strong>`;
                        identificationDiv.style.display = 'block';
                        identificationDiv.classList.remove('not-found');

                        console.log('Customer identified from database:', customerDetails);
                    } else if (customerName || customerEmail) {
                        // Customer provided info but not found in database
                        identifiedSpan.innerHTML = `<strong>${customerName || customerEmail}</strong> (verification needed)`;
                        identificationDiv.style.display = 'block';
                        identificationDiv.classList.add('not-found');

                        console.log('Customer not found in database, verification needed');
                    }
                    
                    // Show Angela is speaking
                    document.getElementById('avatar').classList.add('talking');
                    
                    // Play introduction
                    audio.play();
                    
                    // Add to conversation
                    addMessage('angela', introText);
                    
                    audio.onended = () => {
                        document.getElementById('avatar').classList.remove('talking');
                        updateStatus('Listening... Hold the button and speak', 'listening');
                    };
                } else {
                    throw new Error('Failed to get introduction');
                }
            } catch (error) {
                console.error('Error playing introduction:', error);
                updateStatus('Ready to help you', 'listening');
                const fallbackText = customerName ? 
                    `Hello ${customerName}! I'm Angela from customer support. How can I help you today?` :
                    `Hello! I'm Angela from customer support. How can I assist you today?`;
                addMessage('angela', fallbackText);
            }
        }

        // Initialize microphone access
        async function initializeMicrophone() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    audioChunks = [];
                    processVoiceMessage(audioBlob);
                };

                return true;
            } catch (error) {
                console.error('Microphone access denied:', error);
                updateStatus('Microphone access required for voice chat', 'error');
                return false;
            }
        }

        // Start recording
        async function startRecording() {
            if (isRecording) return;

            if (!mediaRecorder) {
                const micInitialized = await initializeMicrophone();
                if (!micInitialized) return;
            }

            isRecording = true;
            audioChunks = [];

            updateStatus('Listening... Speak now', 'listening');
            document.getElementById('recordBtn').textContent = '🔴 Recording...';
            document.getElementById('recordBtn').style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)';

            mediaRecorder.start();
        }

        // Stop recording
        function stopRecording() {
            if (!isRecording || !mediaRecorder) return;

            isRecording = false;
            updateStatus('Processing your message...', 'processing');
            document.getElementById('recordBtn').textContent = '🎤 Hold to Speak';
            document.getElementById('recordBtn').style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

            mediaRecorder.stop();
        }

        // Process voice message
        async function processVoiceMessage(audioBlob) {
            try {
                const formData = new FormData();
                formData.append('audio', audioBlob, 'voice_message.wav');
                formData.append('customer_id', currentCustomerId || 'CUST_001');

                // Include conversation ID if we have one
                if (currentConversationId) {
                    formData.append('conversation_id', currentConversationId);
                }

                const response = await fetch(`${API_BASE}/voice/process-voice-message`, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();

                    // Store conversation ID for future messages
                    if (result.conversation_id && !currentConversationId) {
                        currentConversationId = result.conversation_id;
                    }

                    // Add user message to conversation
                    addMessage('user', result.transcript);

                    // Add AI response to conversation
                    addMessage('angela', result.ai_response);

                    // Play AI response
                    if (result.audio_response) {
                        const audioData = new Uint8Array(result.audio_response.match(/.{1,2}/g).map(byte => parseInt(byte, 16)));
                        const audioBlob = new Blob([audioData], { type: 'audio/mpeg' });
                        const audioUrl = URL.createObjectURL(audioBlob);
                        const audio = new Audio(audioUrl);

                        updateStatus('Angela is responding...', 'speaking');
                        document.getElementById('avatar').classList.add('talking');

                        audio.play();
                        audio.onended = () => {
                            document.getElementById('avatar').classList.remove('talking');

                            // Enable automatic listening for follow-up after AI response
                            if (autoListenEnabled && conversationActive) {
                                startAutoListening();
                            } else {
                                updateStatus('Listening... Hold the button and speak', 'listening');
                            }
                        };
                    } else {
                        updateStatus('Listening... Hold the button and speak', 'listening');
                    }
                } else {
                    throw new Error('Failed to process voice message');
                }
            } catch (error) {
                console.error('Error processing voice message:', error);
                updateStatus('Error processing message. Please try again.', 'error');
                setTimeout(() => {
                    updateStatus('Listening... Hold the button and speak', 'listening');
                }, 3000);
            }
        }

        // Update status display
        function updateStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        // Add message to conversation
        function addMessage(sender, message) {
            const conversation = document.getElementById('conversation');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.textContent = message;
            conversation.appendChild(messageDiv);
            conversation.scrollTop = conversation.scrollHeight;

            // Store in conversation history
            conversationHistory.push({ sender, message, timestamp: new Date() });
        }

        // Auto-listening functions for continuous conversation
        function startAutoListening() {
            if (isListeningForFollowUp) return;

            isListeningForFollowUp = true;
            updateStatus('Waiting for your response... (Auto-listening in 3 seconds)', 'listening');

            // Show countdown
            let countdown = 3;
            const countdownInterval = setInterval(() => {
                countdown--;
                if (countdown > 0) {
                    updateStatus(`Waiting for your response... (Auto-listening in ${countdown} seconds)`, 'listening');
                } else {
                    clearInterval(countdownInterval);
                    if (isListeningForFollowUp) {
                        startAutoRecording();
                    }
                }
            }, 1000);

            // Set timeout to stop auto-listening after 15 seconds
            followUpTimeout = setTimeout(() => {
                stopAutoListening();
            }, 18000); // 3 seconds countdown + 15 seconds listening
        }

        async function startAutoRecording() {
            if (isRecording || !isListeningForFollowUp) return;

            if (!mediaRecorder) {
                const micInitialized = await initializeMicrophone();
                if (!micInitialized) {
                    stopAutoListening();
                    return;
                }
            }

            isRecording = true;
            audioChunks = [];

            updateStatus('🔴 Auto-listening... Speak now (15 seconds)', 'listening');
            document.getElementById('recordBtn').textContent = '🔴 Auto-Recording...';
            document.getElementById('recordBtn').style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)';

            mediaRecorder.start();

            // Auto-stop after 15 seconds
            setTimeout(() => {
                if (isRecording && isListeningForFollowUp) {
                    stopAutoRecording();
                }
            }, 15000);
        }

        function stopAutoRecording() {
            if (!isRecording || !mediaRecorder) return;

            isRecording = false;
            isListeningForFollowUp = false;

            updateStatus('Processing your message...', 'processing');
            document.getElementById('recordBtn').textContent = '🎤 Hold to Speak';
            document.getElementById('recordBtn').style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

            mediaRecorder.stop();

            if (followUpTimeout) {
                clearTimeout(followUpTimeout);
                followUpTimeout = null;
            }
        }

        function stopAutoListening() {
            isListeningForFollowUp = false;

            if (followUpTimeout) {
                clearTimeout(followUpTimeout);
                followUpTimeout = null;
            }

            updateStatus('Listening... Hold the button and speak', 'listening');
        }

        function toggleAutoListen() {
            autoListenEnabled = !autoListenEnabled;
            const button = document.getElementById('autoListenToggle');
            if (autoListenEnabled) {
                button.textContent = '🔄 Auto-Listen: ON';
                button.style.background = 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)';
            } else {
                button.textContent = '🔄 Auto-Listen: OFF';
                button.style.background = 'linear-gradient(135deg, #f44336 0%, #da190b 100%)';
                stopAutoListening();
            }
        }

        // End call
        function endCall() {
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop();
            }

            // Stop auto-listening
            stopAutoListening();
            conversationActive = false;

            // Reset to intro screen
            document.getElementById('mainScreen').style.display = 'none';
            document.getElementById('introScreen').style.display = 'block';

            // Clear conversation
            document.getElementById('conversation').innerHTML = '';
            conversationHistory = [];

            // Reset customer info
            currentCustomerId = '';
            currentCustomerName = '';
            currentConversationId = '';

            // Hide customer identification
            document.getElementById('customerIdentification').style.display = 'none';

            // Reset form
            document.getElementById('customerName').value = '';
            document.getElementById('customerEmail').value = '';
            document.getElementById('customerId').value = '';

            updateStatus('Call ended', 'listening');
        }

        // Look up customer in database
        async function lookupCustomer() {
            const customerEmail = document.getElementById('customerEmail').value.trim();
            const customerName = document.getElementById('customerName').value.trim();
            const customerId = document.getElementById('customerId').value.trim();

            // Only lookup if we have some identifying information
            if (!customerEmail && !customerName && !customerId) {
                document.getElementById('customerPreview').style.display = 'none';
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/voice/customer-lookup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_email: customerEmail,
                        customer_name: customerName,
                        customer_id: customerId
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    const previewDiv = document.getElementById('customerPreview');
                    const contentDiv = document.getElementById('customerPreviewContent');

                    if (result.found) {
                        const customer = result.customer;
                        contentDiv.innerHTML = `
                            <strong>✅ Customer Found:</strong> ${customer.name}<br>
                            <small>
                                📧 ${customer.email} •
                                🏷️ ${customer.customer_type} •
                                📦 ${customer.total_orders} orders •
                                💰 R${customer.total_spent.toFixed(2)}
                            </small>
                        `;
                        previewDiv.classList.remove('not-found');
                        previewDiv.style.display = 'block';

                        // Auto-fill customer ID if found
                        if (customer.customer_id && !customerId) {
                            document.getElementById('customerId').value = customer.customer_id;
                        }
                    } else {
                        contentDiv.innerHTML = `
                            <strong>⚠️ Customer Not Found</strong><br>
                            <small>Angela will ask for verification during the call</small>
                        `;
                        previewDiv.classList.add('not-found');
                        previewDiv.style.display = 'block';
                    }
                } else {
                    console.error('Customer lookup failed');
                }
            } catch (error) {
                console.error('Error looking up customer:', error);
            }
        }

        // Functions are now properly scoped and will be attached via event listeners

        // Test API connection
        async function testApiConnection() {
            try {
                console.log('Testing API connection to:', API_BASE);
                const response = await fetch(`${API_BASE}/api`);
                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ API connection successful:', data);
                    return true;
                } else {
                    console.error('❌ API connection failed:', response.status, response.statusText);
                    return false;
                }
            } catch (error) {
                console.error('❌ API connection error:', error);
                return false;
            }
        }

        // Initialize on page load
        window.addEventListener('load', async () => {
            console.log('Angela Voice Interface loaded');
            console.log('API Base URL:', API_BASE);
            console.log('Functions available:', {
                lookupCustomer: typeof window.lookupCustomer,
                startCall: typeof window.startCall
            });

            // Test API connection
            await testApiConnection();

            // Set up event listeners for customer input fields
            let lookupTimeout;
            ['customerEmail', 'customerName', 'customerId'].forEach(id => {
                const input = document.getElementById(id);
                if (input) {
                    // Add input event for real-time lookup
                    input.addEventListener('input', () => {
                        clearTimeout(lookupTimeout);
                        lookupTimeout = setTimeout(lookupCustomer, 500); // 500ms delay
                    });
                    // Add blur event for immediate lookup when field loses focus
                    input.addEventListener('blur', lookupCustomer);
                }
            });

            // Set up start call button
            const startCallBtn = document.getElementById('startCallBtn');
            if (startCallBtn) {
                startCallBtn.addEventListener('click', startCall);
                console.log('✅ Start call button event listener added');
            } else {
                console.error('❌ Start call button not found');
            }

            // Set up record button (hold to speak)
            const recordBtn = document.getElementById('recordBtn');
            if (recordBtn) {
                recordBtn.addEventListener('mousedown', startRecording);
                recordBtn.addEventListener('mouseup', stopRecording);
                recordBtn.addEventListener('touchstart', startRecording);
                recordBtn.addEventListener('touchend', stopRecording);
                console.log('✅ Record button event listeners added');
            } else {
                console.error('❌ Record button not found');
            }

            // Set up auto-listen toggle
            const autoListenToggle = document.getElementById('autoListenToggle');
            if (autoListenToggle) {
                autoListenToggle.addEventListener('click', toggleAutoListen);
                console.log('✅ Auto-listen toggle event listener added');
            } else {
                console.error('❌ Auto-listen toggle not found');
            }

            // Set up end call button
            const endCallBtn = document.getElementById('endCallBtn');
            if (endCallBtn) {
                endCallBtn.addEventListener('click', endCall);
                console.log('✅ End call button event listener added');
            } else {
                console.error('❌ End call button not found');
            }

            console.log('✅ All event listeners set up successfully');
        });
    </script>
</body>
</html>
