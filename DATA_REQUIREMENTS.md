# Smart Customer Support Orchestrator - Data Requirements

## Overview
This document outlines the data requirements for the Smart Customer Support Orchestrator, a multi-agent AI system that processes customer support requests through specialized agents.

## Database Schema

### 1. Knowledge Base (`knowledge_base`)
**Purpose**: Store company documentation, policies, and FAQs for agent reference

| Column | Type | Description |
|--------|------|-------------|
| document_id | STRING | Unique document identifier |
| title | STRING | Document title |
| content | STRING | Full document content (searchable) |
| category | STRING | Document category (policies, technical, billing, etc.) |
| tags | ARRAY<STRING> | Searchable tags |
| last_updated | TIMESTAMP | Last modification date |
| created_at | TIMESTAMP | Creation date |
| version | INT64 | Document version number |
| status | STRING | active/inactive/draft |
| author | STRING | Document author |
| approval_status | STRING | approved/pending/rejected |

**Sample Categories**: policies, shipping, technical, quality, billing, returns

### 2. Support Tickets (`support_tickets`)
**Purpose**: Store all processed customer support interactions

| Column | Type | Description |
|--------|------|-------------|
| ticket_id | STRING | Unique ticket identifier |
| customer_id | STRING | Customer identifier |
| customer_message | STRING | Original customer message |
| intent | STRING | Detected customer intent |
| sentiment | STRING | Sentiment analysis result |
| priority | STRING | Assigned priority level |
| final_response | STRING | Final AI-generated response |
| status | STRING | completed/escalated/pending |
| escalated | BOOLEAN | Whether ticket was escalated |
| escalation_reason | STRING | Reason for escalation |
| resolution_type | STRING | Type of resolution provided |
| customer_satisfaction_score | FLOAT64 | Post-resolution satisfaction (1-5) |
| total_processing_time | FLOAT64 | Total processing time in seconds |
| created_at | TIMESTAMP | Ticket creation time |
| completed_at | TIMESTAMP | Ticket completion time |
| channel | STRING | Communication channel (email, chat, phone) |
| urgency_score | FLOAT64 | Urgency score (0-1) |
| confidence_score | FLOAT64 | AI confidence score (0-1) |
| quality_score | FLOAT64 | Response quality score (0-1) |

**Intent Values**: order_issue, billing_dispute, technical_support, general_inquiry, account_access, refund_request, product_defect, shipping_delay, complaint, compliment, feature_request

**Sentiment Values**: very_positive, positive, neutral, negative, very_negative

**Priority Values**: low, medium, high, urgent

### 3. Agent Responses (`agent_responses`)
**Purpose**: Store individual agent processing results for analysis

| Column | Type | Description |
|--------|------|-------------|
| response_id | STRING | Unique response identifier |
| ticket_id | STRING | Associated ticket ID |
| agent_name | STRING | Agent that processed (intake, knowledge, resolution, quality) |
| agent_result | JSON | Complete agent result data |
| processing_time | FLOAT64 | Agent processing time in seconds |
| created_at | TIMESTAMP | Processing timestamp |
| success | BOOLEAN | Whether processing succeeded |
| error_message | STRING | Error details if failed |

### 4. Customer Profiles (`customer_profiles`)
**Purpose**: Enhanced customer information for personalized support

| Column | Type | Description |
|--------|------|-------------|
| customer_id | STRING | Unique customer identifier |
| email | STRING | Customer email address |
| customer_type | STRING | regular/premium/vip |
| registration_date | TIMESTAMP | Account registration date |
| total_orders | INT64 | Total number of orders |
| total_spent | FLOAT64 | Total amount spent |
| support_tier | STRING | standard/priority/vip |
| preferred_channel | STRING | Preferred communication method |
| language_preference | STRING | Language code (en, es, fr, etc.) |
| timezone | STRING | Customer timezone |
| last_contact | TIMESTAMP | Last support contact |
| satisfaction_avg | FLOAT64 | Average satisfaction score |
| escalation_history | INT64 | Number of past escalations |
| created_at | TIMESTAMP | Profile creation date |
| updated_at | TIMESTAMP | Last profile update |

### 5. FAQs (`faqs`)
**Purpose**: Frequently asked questions with usage analytics

| Column | Type | Description |
|--------|------|-------------|
| faq_id | STRING | Unique FAQ identifier |
| question | STRING | FAQ question |
| answer | STRING | FAQ answer |
| category | STRING | FAQ category |
| subcategory | STRING | FAQ subcategory |
| keywords | ARRAY<STRING> | Search keywords |
| view_count | INT64 | Number of times viewed |
| helpful_votes | INT64 | Helpful votes received |
| unhelpful_votes | INT64 | Unhelpful votes received |
| last_updated | TIMESTAMP | Last update date |
| created_at | TIMESTAMP | Creation date |
| status | STRING | active/inactive |
| priority | INT64 | Display priority (1=high, 2=medium, 3=low) |

### 6. System Metrics (`system_metrics`)
**Purpose**: Track overall system performance

| Column | Type | Description |
|--------|------|-------------|
| metric_id | STRING | Unique metric identifier |
| metric_date | DATE | Date of metrics |
| total_tickets_processed | INT64 | Total tickets processed |
| average_response_time | FLOAT64 | Average response time in seconds |
| escalation_rate | FLOAT64 | Escalation rate (0-1) |
| customer_satisfaction_avg | FLOAT64 | Average satisfaction score |
| agent_performance | JSON | Individual agent metrics |
| uptime_percentage | FLOAT64 | System uptime percentage |
| error_count | INT64 | Number of errors |
| created_at | TIMESTAMP | Metric creation time |

### 7. Escalation Rules (`escalation_rules`)
**Purpose**: Define automatic escalation triggers

| Column | Type | Description |
|--------|------|-------------|
| rule_id | STRING | Unique rule identifier |
| rule_name | STRING | Human-readable rule name |
| trigger_conditions | JSON | Conditions that trigger escalation |
| escalation_target | STRING | Where to escalate (human_agent, supervisor, specialist) |
| priority_level | STRING | Priority level for escalated tickets |
| active | BOOLEAN | Whether rule is active |
| created_at | TIMESTAMP | Rule creation date |
| updated_at | TIMESTAMP | Last rule update |

### 8. Training Data (`training_data`)
**Purpose**: Store training examples for model improvement

| Column | Type | Description |
|--------|------|-------------|
| training_id | STRING | Unique training example identifier |
| customer_message | STRING | Example customer message |
| correct_intent | STRING | Correct intent classification |
| correct_sentiment | STRING | Correct sentiment classification |
| correct_priority | STRING | Correct priority assignment |
| human_response | STRING | Human-written response example |
| quality_score | FLOAT64 | Quality score for the example |
| feedback_type | STRING | positive/negative/correction |
| created_at | TIMESTAMP | Example creation date |
| validated | BOOLEAN | Whether example is validated |
| validator_id | STRING | ID of person who validated |

## Data Flow

### 1. Customer Message Processing Flow
```
Customer Message → Intake Agent → Knowledge Agent → Resolution Agent → Quality Agent → Final Response
```

### 2. Data Storage Flow
```
1. Customer message stored in support_tickets
2. Each agent result stored in agent_responses
3. Final ticket updated with complete results
4. System metrics updated daily
5. Training data collected from feedback
```

## Sample Data Volumes

### Initial Setup (Demo/Testing)
- Knowledge Base: 50-100 documents
- FAQs: 20-50 entries
- Customer Profiles: 100-500 customers
- Historical Tickets: 500-1000 tickets
- Training Data: 200-500 examples

### Production Scale (Estimated)
- Knowledge Base: 1,000-5,000 documents
- FAQs: 200-1,000 entries
- Customer Profiles: 10,000-100,000 customers
- Support Tickets: 1,000-10,000 per day
- Training Data: 5,000-50,000 examples

## Data Quality Requirements

### 1. Knowledge Base
- All documents must have clear titles and categories
- Content should be regularly updated (monthly review)
- Tags should be comprehensive for search optimization
- Approval workflow for all content changes

### 2. Support Tickets
- All required fields must be populated
- Customer satisfaction scores collected within 24 hours
- Processing times accurately recorded
- Escalation reasons clearly documented

### 3. Customer Profiles
- Email addresses validated and unique
- Customer type accurately classified
- Support tier aligned with business rules
- Regular profile updates from order system

## Data Privacy and Security

### 1. PII Protection
- Customer emails and personal data encrypted
- Access controls on customer profiles
- Data retention policies (7 years for tickets)
- GDPR/CCPA compliance for data deletion

### 2. Data Access
- Role-based access to different data types
- Audit logging for all data access
- Secure API endpoints for data retrieval
- Regular security reviews

## Setup Instructions

### 1. Quick Setup
```bash
# Run the automated setup
python setup_database.py
```

### 2. Manual Setup
```sql
-- 1. Create tables
-- Run: sql/create_tables.sql

-- 2. Insert sample data  
-- Run: sql/insert_sample_data.sql

-- 3. Test with sample queries
-- Run: sql/useful_queries.sql
```

### 3. Verification
```sql
-- Check table creation
SELECT table_name, row_count 
FROM `pro-course-433716-v0.customer_support.__TABLES__`;

-- Test data retrieval
SELECT * FROM `pro-course-433716-v0.customer_support.support_tickets` LIMIT 5;
```

## Integration Points

### 1. External Systems
- CRM system → Customer profiles
- Order management → Customer history
- Email system → Ticket creation
- Chat platform → Real-time tickets

### 2. AI/ML Services
- Vertex AI → Agent processing
- Natural Language API → Sentiment analysis
- Translation API → Multi-language support
- AutoML → Custom model training

### 3. Monitoring
- Cloud Monitoring → System metrics
- Cloud Logging → Error tracking
- BigQuery → Analytics and reporting
- Data Studio → Dashboards

This comprehensive data setup provides everything needed for the Smart Customer Support Orchestrator to function effectively in both demo and production environments.
