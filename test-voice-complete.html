<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Interface Complete Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            width: 200px;
        }
        #console-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Voice Interface Complete Test</h1>
        <p>This page tests all the functionality that should work in voice.html</p>
        
        <div class="test-section info">
            <h3>📋 Test Results</h3>
            <div id="test-results">
                <div>⏳ Running tests...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 Customer Lookup Test</h3>
            <input type="email" id="testEmail" placeholder="Try: <EMAIL>" value="<EMAIL>" />
            <button onclick="testCustomerLookup()">Test Lookup</button>
            <div id="lookup-result"></div>
        </div>

        <div class="test-section">
            <h3>📞 Voice Call Test</h3>
            <input type="text" id="testName" placeholder="Customer name" value="John Smith" />
            <button onclick="testVoiceCall()">Test Start Call</button>
            <div id="call-result"></div>
        </div>

        <div class="test-section">
            <h3>🌐 API Connection Test</h3>
            <button onclick="testAPIConnection()">Test API</button>
            <div id="api-result"></div>
        </div>

        <div class="test-section">
            <h3>📝 Console Output</h3>
            <div id="console-output"></div>
        </div>
    </div>

    <script>
        // Console capture
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : '✅';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // Global error handler
        window.addEventListener('error', (e) => {
            console.error(`JavaScript Error: ${e.message} at line ${e.lineno}`);
        });

        // API Base URL
        const API_BASE = window.location.origin;
        
        function updateTestResult(elementId, message, success = true) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${success ? 'success' : 'error'}">${message}</div>`;
        }

        // Test customer lookup
        async function testCustomerLookup() {
            const email = document.getElementById('testEmail').value.trim();
            console.log(`Testing customer lookup for: ${email}`);
            
            try {
                const response = await fetch(`${API_BASE}/voice/customer-lookup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_email: email,
                        customer_name: '',
                        customer_id: ''
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('Customer lookup successful:', result);
                    updateTestResult('lookup-result', 
                        `✅ Found: ${result.found ? result.customer.name : 'Not found'}`, true);
                } else {
                    throw new Error(`${response.status} ${response.statusText}`);
                }
            } catch (error) {
                console.error('Customer lookup error:', error.message);
                updateTestResult('lookup-result', `❌ Error: ${error.message}`, false);
            }
        }

        // Test voice call
        async function testVoiceCall() {
            const name = document.getElementById('testName').value.trim();
            console.log(`Testing voice call for: ${name}`);
            
            try {
                const response = await fetch(`${API_BASE}/voice/introduction`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_name: name,
                        customer_email: '<EMAIL>',
                        customer_id: ''
                    })
                });
                
                if (response.ok) {
                    const angelaMessage = response.headers.get('X-Angela-Message') || 'No message';
                    console.log('Voice call successful, Angela says:', angelaMessage);
                    updateTestResult('call-result', `✅ Angela: "${angelaMessage}"`, true);
                } else {
                    throw new Error(`${response.status} ${response.statusText}`);
                }
            } catch (error) {
                console.error('Voice call error:', error.message);
                updateTestResult('call-result', `❌ Error: ${error.message}`, false);
            }
        }

        // Test API connection
        async function testAPIConnection() {
            console.log(`Testing API connection to: ${API_BASE}`);
            
            try {
                const response = await fetch(`${API_BASE}/api`);
                if (response.ok) {
                    const data = await response.json();
                    console.log('API connection successful:', data);
                    updateTestResult('api-result', `✅ Connected: ${data.name} v${data.version}`, true);
                } else {
                    throw new Error(`${response.status} ${response.statusText}`);
                }
            } catch (error) {
                console.error('API connection error:', error.message);
                updateTestResult('api-result', `❌ Error: ${error.message}`, false);
            }
        }

        // Run automatic tests
        async function runAllTests() {
            console.log('🚀 Starting automatic tests...');
            
            const tests = [
                { name: 'API Connection', func: testAPIConnection },
                { name: 'Customer Lookup', func: testCustomerLookup },
                { name: 'Voice Call', func: testVoiceCall }
            ];
            
            const results = [];
            
            for (const test of tests) {
                try {
                    console.log(`Running ${test.name} test...`);
                    await test.func();
                    results.push(`✅ ${test.name}: PASSED`);
                } catch (error) {
                    results.push(`❌ ${test.name}: FAILED - ${error.message}`);
                }
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
            }
            
            const testResultsDiv = document.getElementById('test-results');
            testResultsDiv.innerHTML = results.map(result => `<div>${result}</div>`).join('');
            
            console.log('🏁 All tests completed');
        }

        // Initialize when page loads
        window.addEventListener('load', () => {
            console.log('🚀 Test page loaded successfully');
            console.log(`🌐 API Base URL: ${API_BASE}`);
            console.log('✅ All functions are available and ready');
            
            // Run tests automatically after a short delay
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
