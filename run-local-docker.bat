@echo off
REM Smart Customer Support Orchestrator - Local Docker Runner (Windows)
echo 🚀 Building and running Smart Customer Support Orchestrator locally...

REM Check if Dock<PERSON> is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo [ERROR] .env file not found. Please create it with your configuration.
    pause
    exit /b 1
)

echo [INFO] Checking Google Cloud credentials...

REM Check for Google Cloud credentials
if not exist "src\vertexai.json" (
    echo [WARNING] Google Cloud service account key not found at src\vertexai.json
    echo [WARNING] Voice features may not work without proper credentials
)

REM Stop any existing container
echo [INFO] Stopping any existing containers...
docker stop smart-customer-support-local >nul 2>&1
docker rm smart-customer-support-local >nul 2>&1

REM Build the Docker image
echo [INFO] Building Docker image...
docker build -t smart-customer-support:local .
if errorlevel 1 (
    echo [ERROR] Failed to build Docker image
    pause
    exit /b 1
)

echo [SUCCESS] Docker image built successfully

REM Run the container
echo [INFO] Starting container on port 8080...
docker run -d --name smart-customer-support-local --env-file .env -p 8080:8080 -v "%cd%\src\vertexai.json:/app/src/vertexai.json:ro" smart-customer-support:local
if errorlevel 1 (
    echo [ERROR] Failed to start container
    pause
    exit /b 1
)

echo [SUCCESS] Container started successfully!

REM Wait a moment for the container to start
echo [INFO] Waiting for application to start...
timeout /t 5 /nobreak >nul

REM Check if the application is responding
curl -f http://localhost:8080/health >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] Application is healthy and ready!
    echo.
    echo 🌐 Access your application at:
    echo    • Main Interface: http://localhost:8080
    echo    • Voice Interface: http://localhost:8080/voice.html
    echo    • API Documentation: http://localhost:8080/docs
    echo    • Health Check: http://localhost:8080/health
    echo.
    echo 📋 Useful commands:
    echo    • View logs: docker logs smart-customer-support-local -f
    echo    • Stop container: docker stop smart-customer-support-local
    echo    • Remove container: docker rm smart-customer-support-local
    echo.
    echo [INFO] Container is running in the background.
) else (
    echo [WARNING] Application may still be starting up. Check logs with:
    echo docker logs smart-customer-support-local -f
)

echo.
echo Press any key to exit...
pause >nul
