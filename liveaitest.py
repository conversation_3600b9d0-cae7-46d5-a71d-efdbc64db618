import requests
import json
import time

# Endpoint
URL = "http://localhost:8000/process-message"
HEADERS = {"Content-Type": "application/json"}

# Output file
OUTPUT_FILE = "responses.txt"

# Test cases
tests = [
    # 1. Sentiment / Tone Handling
    {"name": "Polite", "data": {"customer_id": "CUST_001", "content": "Could you please show me my orders?", "channel": "web"}},
    {"name": "Frustrated", "data": {"customer_id": "CUST_001", "content": "This is taking forever, where are my orders?!", "channel": "web"}},
    {"name": "Profanity", "data": {"customer_id": "CUST_001", "content": "where are my fucking orders", "channel": "web"}},
    {"name": "Sarcasm", "data": {"customer_id": "CUST_001", "content": "Wow, such amazing service. Do I even have orders or what?", "channel": "web"}},

    # 2. Security / Privacy
    {"name": "Invalid customer_id", "data": {"customer_id": "CUST_999", "content": "my orders", "channel": "web"}},
    {"name": "Empty customer_id", "data": {"customer_id": "", "content": "my orders", "channel": "web"}},
    {"name": "Other customer request", "data": {"customer_id": "CUST_001", "content": "Show me CUST_002's orders.", "channel": "web"}},

    # 3. Ambiguity
    {"name": "Vague query 1", "data": {"customer_id": "CUST_001", "content": "Where's my stuff?", "channel": "web"}},
    {"name": "Vague query 2", "data": {"customer_id": "CUST_001", "content": "Check the thing I bought last week.", "channel": "web"}},

    # 4. Edge Cases
    {"name": "No orders", "data": {"customer_id": "CUST_NOORDERS", "content": "my orders", "channel": "web"}},
    {"name": "Huge orders", "data": {"customer_id": "CUST_HUGE", "content": "my orders", "channel": "web"}},

    # 5. Escalation
    {"name": "Refund request", "data": {"customer_id": "CUST_001", "content": "I want a refund now", "channel": "web"}},
    {"name": "Threatening", "data": {"customer_id": "CUST_001", "content": "If I don't get my order, I'll report you", "channel": "web"}},
    {"name": "Human escalation", "data": {"customer_id": "CUST_001", "content": "Let me speak to a human agent", "channel": "web"}},

    # 6. Multi-Channel
    {"name": "Channel email", "data": {"customer_id": "CUST_001", "content": "my orders", "channel": "email"}},
    {"name": "Channel whatsapp", "data": {"customer_id": "CUST_001", "content": "my orders", "channel": "whatsapp"}},

    # 7. Robustness
    {"name": "Empty message", "data": {"customer_id": "CUST_001", "content": "", "channel": "web"}},
    {"name": "Random text", "data": {"customer_id": "CUST_001", "content": "asdfghjkl", "channel": "web"}},
    {"name": "Foreign language", "data": {"customer_id": "CUST_001", "content": "¿Dónde está mi pedido?", "channel": "web"}},
    {"name": "Emoji only", "data": {"customer_id": "CUST_001", "content": "📦❓", "channel": "web"}},
]

def run_tests():
    with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
        for test in tests:
            header = f"\n===== {test['name']} =====\n"
            print(header.strip())
            f.write(header)

            try:
                resp = requests.post(URL, headers=HEADERS, data=json.dumps(test["data"]))
                if resp.status_code == 200:
                    response_json = resp.json()
                    formatted = json.dumps(response_json, indent=2, ensure_ascii=False)
                    print(formatted)
                    f.write(formatted + "\n")
                else:
                    error_msg = f"Error {resp.status_code}: {resp.text}"
                    print(error_msg)
                    f.write(error_msg + "\n")
            except Exception as e:
                err = f"Exception: {e}"
                print(err)
                f.write(err + "\n")

            f.flush()  # make sure data is written
            time.sleep(1)  # avoid overwhelming server

if __name__ == "__main__":
    run_tests()
