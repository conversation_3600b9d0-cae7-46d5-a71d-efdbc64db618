#!/usr/bin/env python3
"""
Smart Customer Support Orchestrator - Application Starter
Starts both backend API and frontend interface for the hackathon demo
"""

import subprocess
import sys
import time
import os
import signal
import threading
from pathlib import Path

def print_banner():
    """Print application banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🧠 Smart Customer Support Orchestrator               ║
    ║                                                              ║
    ║        Agent Development Kit (ADK) Hackathon Submission      ║
    ║                                                              ║
    ║        Features:                                             ║
    ║        • AI Consciousness & Brain System                     ║
    ║        • Swarm Intelligence                                  ║
    ║        • Collective Intelligence                             ║
    ║        • Emotional Contagion Management                      ║
    ║        • 15+ Google Cloud Services                           ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """Check if required dependencies are installed."""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        "streamlit",
        "fastapi", 
        "uvicorn",
        "plotly",
        "pandas",
        "requests"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("📦 Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "-r", "requirements-frontend.txt"
            ])
            print("✅ Dependencies installed successfully!")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies. Please run:")
            print("   pip install -r requirements-frontend.txt")
            return False
    
    return True

def start_backend():
    """Start the FastAPI backend server."""
    print("🚀 Starting backend API server...")
    
    try:
        # Start FastAPI with uvicorn
        backend_process = subprocess.Popen([
            sys.executable, "-m", "uvicorn",
            "src.api.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Check if process is still running
        if backend_process.poll() is None:
            print("✅ Backend API server started successfully!")
            print("   📡 API URL: http://localhost:8000")
            print("   📚 API Docs: http://localhost:8000/docs")
            return backend_process
        else:
            print("❌ Failed to start backend server")
            stdout, stderr = backend_process.communicate()
            print(f"Error: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        return None

def start_frontend():
    """Start the Streamlit frontend."""
    print("🎨 Starting frontend interface...")
    
    try:
        # Start Streamlit
        frontend_process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run",
            "frontend/app.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0",
            "--browser.gatherUsageStats", "false"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(5)
        
        # Check if process is still running
        if frontend_process.poll() is None:
            print("✅ Frontend interface started successfully!")
            print("   🌐 Frontend URL: http://localhost:8501")
            return frontend_process
        else:
            print("❌ Failed to start frontend")
            stdout, stderr = frontend_process.communicate()
            print(f"Error: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ Error starting frontend: {e}")
        return None

def monitor_processes(backend_process, frontend_process):
    """Monitor running processes and restart if needed."""
    print("👀 Monitoring processes...")
    
    try:
        while True:
            time.sleep(5)
            
            # Check backend
            if backend_process and backend_process.poll() is not None:
                print("⚠️  Backend process stopped, restarting...")
                backend_process = start_backend()
            
            # Check frontend
            if frontend_process and frontend_process.poll() is not None:
                print("⚠️  Frontend process stopped, restarting...")
                frontend_process = start_frontend()
            
            # If both processes failed, exit
            if not backend_process and not frontend_process:
                print("❌ Both processes failed, exiting...")
                break
                
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        
        # Terminate processes
        if backend_process:
            backend_process.terminate()
            print("   🔴 Backend stopped")
        
        if frontend_process:
            frontend_process.terminate()
            print("   🔴 Frontend stopped")
        
        print("✅ Application shutdown complete")

def main():
    """Main application starter."""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    print("\n🚀 Starting Smart Customer Support Orchestrator...")
    print("=" * 60)
    
    # Start backend
    backend_process = start_backend()
    if not backend_process:
        print("❌ Failed to start backend, exiting...")
        sys.exit(1)
    
    # Start frontend
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ Failed to start frontend, stopping backend...")
        backend_process.terminate()
        sys.exit(1)
    
    print("\n🎉 Application started successfully!")
    print("=" * 60)
    print("🌐 Frontend: http://localhost:8501")
    print("📡 Backend API: http://localhost:8000")
    print("📚 API Docs: http://localhost:8000/docs")
    print("=" * 60)
    print("\n💡 Demo Instructions:")
    print("1. Open http://localhost:8501 in your browser")
    print("2. Try the different demo scenarios:")
    print("   • Angry customer with billing issue")
    print("   • Complex technical problem (triggers swarm intelligence)")
    print("   • Policy exception request (triggers collective intelligence)")
    print("3. Monitor the brain system analytics in real-time")
    print("4. Explore swarm intelligence and collective intelligence tabs")
    print("\n🧠 Brain System Features:")
    print("• Agent consciousness evolution")
    print("• Emotional contagion management")
    print("• Swarm intelligence for complex problems")
    print("• Collective decision making")
    print("• Memory consolidation and learning")
    print("\n🏆 Hackathon Compliance:")
    print("✅ ADK Framework Integration")
    print("✅ 15+ Google Cloud Services")
    print("✅ Innovation Beyond Standard Pipelines")
    print("✅ Complete Architecture Documentation")
    print("\nPress Ctrl+C to stop the application")
    
    # Monitor processes
    monitor_processes(backend_process, frontend_process)

if __name__ == "__main__":
    main()
