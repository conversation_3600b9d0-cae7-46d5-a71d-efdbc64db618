<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Outbound Call Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2a4365;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #4a5568;
            font-size: 1.2em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card .icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .stat-card .value {
            font-size: 2.5em;
            font-weight: bold;
            color: #2a4365;
            margin-bottom: 10px;
        }

        .stat-card .label {
            color: #4a5568;
            font-size: 1.1em;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .panel h2 {
            color: #2a4365;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn.success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .btn.warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }

        .btn.danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2a4365;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .call-queue {
            max-height: 400px;
            overflow-y: auto;
        }

        .call-item {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: background-color 0.3s ease;
        }

        .call-item:hover {
            background: #edf2f7;
        }

        .call-item .customer-name {
            font-weight: bold;
            color: #2a4365;
            margin-bottom: 5px;
        }

        .call-item .call-details {
            color: #4a5568;
            font-size: 0.9em;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-scheduled {
            background: #bee3f8;
            color: #2b6cb0;
        }

        .status-calling {
            background: #fbb6ce;
            color: #b83280;
        }

        .status-connected {
            background: #c6f6d5;
            color: #2f855a;
        }

        .status-completed {
            background: #d6f5d6;
            color: #22543d;
        }

        .status-failed {
            background: #fed7d7;
            color: #c53030;
        }

        .campaign-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .campaign-item {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .campaign-info h3 {
            color: #2a4365;
            margin-bottom: 5px;
        }

        .campaign-info p {
            color: #4a5568;
            font-size: 0.9em;
        }

        .campaign-actions {
            display: flex;
            gap: 10px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #4a5568;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- Header -->
        <div class="header">
            <h1>🤖 AI Outbound Call Dashboard</h1>
            <p>Manage and monitor AI-initiated customer calls</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon">📞</div>
                <div class="value" id="totalCalls">0</div>
                <div class="label">Total Calls Today</div>
            </div>
            <div class="stat-card">
                <div class="icon">✅</div>
                <div class="value" id="successfulCalls">0</div>
                <div class="label">Successful Calls</div>
            </div>
            <div class="stat-card">
                <div class="icon">⏱️</div>
                <div class="value" id="avgDuration">0m</div>
                <div class="label">Avg Call Duration</div>
            </div>
            <div class="stat-card">
                <div class="icon">📊</div>
                <div class="value" id="successRate">0%</div>
                <div class="label">Success Rate</div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Call Queue Panel -->
            <div class="panel">
                <h2>📋 Call Queue</h2>
                <div class="call-queue" id="callQueue">
                    <div class="loading">Loading call queue...</div>
                </div>
                <button class="btn" onclick="refreshCallQueue()">🔄 Refresh Queue</button>
            </div>

            <!-- Campaign Management Panel -->
            <div class="panel">
                <h2>🎯 Active Campaigns</h2>
                <div class="campaign-list" id="campaignList">
                    <div class="loading">Loading campaigns...</div>
                </div>
                <button class="btn success" onclick="showCreateCampaignModal()">➕ Create Campaign</button>
            </div>
        </div>

        <!-- Quick Actions Panel -->
        <div class="panel">
            <h2>⚡ Quick Actions</h2>
            <button class="btn" onclick="scheduleCall()">📞 Schedule Single Call</button>
            <button class="btn success" onclick="showCreateCampaignModal()">🎯 Create Campaign</button>
            <button class="btn warning" onclick="pauseAllCampaigns()">⏸️ Pause All Campaigns</button>
            <button class="btn danger" onclick="emergencyStop()">🛑 Emergency Stop</button>
        </div>
    </div>

    <!-- Create Campaign Modal -->
    <div id="createCampaignModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('createCampaignModal')">&times;</span>
            <h2>Create New Campaign</h2>
            <form id="campaignForm">
                <div class="form-group">
                    <label for="campaignName">Campaign Name:</label>
                    <input type="text" id="campaignName" required>
                </div>
                <div class="form-group">
                    <label for="campaignType">Campaign Type:</label>
                    <select id="campaignType" required>
                        <option value="follow_up">Follow-up Calls</option>
                        <option value="survey">Customer Survey</option>
                        <option value="proactive_support">Proactive Support</option>
                        <option value="retention">Customer Retention</option>
                        <option value="product_promotion">Product Promotion</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="targetCustomers">Target Customers:</label>
                    <select id="targetCustomers" required>
                        <option value="premium">Premium Customers</option>
                        <option value="vip">VIP Customers</option>
                        <option value="regular">Regular Customers</option>
                        <option value="all">All Customers</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="startTime">Start Time:</label>
                    <input type="datetime-local" id="startTime" required>
                </div>
                <div class="form-group">
                    <label for="maxCallsPerHour">Max Calls Per Hour:</label>
                    <input type="number" id="maxCallsPerHour" value="10" min="1" max="60" required>
                </div>
                <button type="submit" class="btn success">Create Campaign</button>
                <button type="button" class="btn" onclick="closeModal('createCampaignModal')">Cancel</button>
            </form>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
        });

        async function loadDashboardData() {
            try {
                await Promise.all([
                    loadStatistics(),
                    loadCallQueue(),
                    loadCampaigns()
                ]);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        async function loadStatistics() {
            // Mock statistics for demo
            document.getElementById('totalCalls').textContent = Math.floor(Math.random() * 100) + 50;
            document.getElementById('successfulCalls').textContent = Math.floor(Math.random() * 80) + 40;
            document.getElementById('avgDuration').textContent = (Math.random() * 5 + 2).toFixed(1) + 'm';
            document.getElementById('successRate').textContent = (Math.random() * 20 + 75).toFixed(0) + '%';
        }

        async function loadCallQueue() {
            try {
                // This would call the actual API endpoint
                // const response = await fetch(`${API_BASE}/outbound/queue`);
                // const calls = await response.json();
                
                // Mock data for demo
                const calls = [
                    {
                        call_id: 'call_001',
                        customer_name: 'John Doe',
                        phone_number: '******-0123',
                        purpose: 'follow_up',
                        status: 'scheduled',
                        scheduled_time: new Date(Date.now() + 300000).toISOString()
                    },
                    {
                        call_id: 'call_002',
                        customer_name: 'Sarah Wilson',
                        phone_number: '******-0456',
                        purpose: 'survey',
                        status: 'calling',
                        scheduled_time: new Date().toISOString()
                    },
                    {
                        call_id: 'call_003',
                        customer_name: 'Mike Johnson',
                        phone_number: '******-0789',
                        purpose: 'proactive_support',
                        status: 'connected',
                        scheduled_time: new Date(Date.now() - 120000).toISOString()
                    }
                ];
                
                displayCallQueue(calls);
            } catch (error) {
                console.error('Error loading call queue:', error);
                document.getElementById('callQueue').innerHTML = '<div class="loading">Error loading call queue</div>';
            }
        }

        function displayCallQueue(calls) {
            const queueContainer = document.getElementById('callQueue');
            
            if (calls.length === 0) {
                queueContainer.innerHTML = '<div class="loading">No calls in queue</div>';
                return;
            }
            
            queueContainer.innerHTML = calls.map(call => `
                <div class="call-item">
                    <div class="customer-name">${call.customer_name}</div>
                    <div class="call-details">
                        <span class="status-badge status-${call.status}">${call.status}</span>
                        Purpose: ${call.purpose} | Phone: ${call.phone_number}
                        <br>Scheduled: ${new Date(call.scheduled_time).toLocaleString()}
                    </div>
                </div>
            `).join('');
        }

        async function loadCampaigns() {
            try {
                // Mock data for demo
                const campaigns = [
                    {
                        campaign_id: 'camp_001',
                        name: 'Q4 Customer Satisfaction Survey',
                        status: 'active',
                        total_calls_scheduled: 150,
                        total_calls_completed: 45,
                        success_rate: 0.78
                    },
                    {
                        campaign_id: 'camp_002',
                        name: 'Premium Customer Follow-up',
                        status: 'scheduled',
                        total_calls_scheduled: 75,
                        total_calls_completed: 0,
                        success_rate: 0.0
                    }
                ];
                
                displayCampaigns(campaigns);
            } catch (error) {
                console.error('Error loading campaigns:', error);
                document.getElementById('campaignList').innerHTML = '<div class="loading">Error loading campaigns</div>';
            }
        }

        function displayCampaigns(campaigns) {
            const campaignContainer = document.getElementById('campaignList');
            
            if (campaigns.length === 0) {
                campaignContainer.innerHTML = '<div class="loading">No active campaigns</div>';
                return;
            }
            
            campaignContainer.innerHTML = campaigns.map(campaign => `
                <div class="campaign-item">
                    <div class="campaign-info">
                        <h3>${campaign.name}</h3>
                        <p>Status: <span class="status-badge status-${campaign.status}">${campaign.status}</span></p>
                        <p>Progress: ${campaign.total_calls_completed}/${campaign.total_calls_scheduled} calls</p>
                        <p>Success Rate: ${(campaign.success_rate * 100).toFixed(1)}%</p>
                    </div>
                    <div class="campaign-actions">
                        ${campaign.status === 'active' ? 
                            '<button class="btn warning" onclick="pauseCampaign(\'' + campaign.campaign_id + '\')">⏸️ Pause</button>' :
                            '<button class="btn success" onclick="resumeCampaign(\'' + campaign.campaign_id + '\')">▶️ Resume</button>'
                        }
                        <button class="btn danger" onclick="cancelCampaign('${campaign.campaign_id}')">🛑 Cancel</button>
                    </div>
                </div>
            `).join('');
        }

        function showCreateCampaignModal() {
            document.getElementById('createCampaignModal').style.display = 'block';
            
            // Set default start time to now + 1 hour
            const now = new Date();
            now.setHours(now.getHours() + 1);
            document.getElementById('startTime').value = now.toISOString().slice(0, 16);
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        document.getElementById('campaignForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('campaignName').value,
                campaign_type: document.getElementById('campaignType').value,
                target_customers: document.getElementById('targetCustomers').value,
                start_time: document.getElementById('startTime').value,
                max_calls_per_hour: parseInt(document.getElementById('maxCallsPerHour').value)
            };
            
            try {
                // This would call the actual API endpoint
                // const response = await fetch(`${API_BASE}/campaigns`, {
                //     method: 'POST',
                //     headers: { 'Content-Type': 'application/json' },
                //     body: JSON.stringify(formData)
                // });
                
                alert('Campaign created successfully!');
                closeModal('createCampaignModal');
                loadCampaigns();
            } catch (error) {
                console.error('Error creating campaign:', error);
                alert('Error creating campaign. Please try again.');
            }
        });

        async function refreshCallQueue() {
            await loadCallQueue();
        }

        async function scheduleCall() {
            const customerId = prompt('Enter Customer ID:');
            if (customerId) {
                try {
                    // This would call the actual API endpoint
                    alert(`Call scheduled for customer ${customerId}`);
                    loadCallQueue();
                } catch (error) {
                    console.error('Error scheduling call:', error);
                    alert('Error scheduling call. Please try again.');
                }
            }
        }

        async function pauseCampaign(campaignId) {
            try {
                // This would call the actual API endpoint
                alert(`Campaign ${campaignId} paused`);
                loadCampaigns();
            } catch (error) {
                console.error('Error pausing campaign:', error);
                alert('Error pausing campaign. Please try again.');
            }
        }

        async function resumeCampaign(campaignId) {
            try {
                // This would call the actual API endpoint
                alert(`Campaign ${campaignId} resumed`);
                loadCampaigns();
            } catch (error) {
                console.error('Error resuming campaign:', error);
                alert('Error resuming campaign. Please try again.');
            }
        }

        async function cancelCampaign(campaignId) {
            if (confirm('Are you sure you want to cancel this campaign?')) {
                try {
                    // This would call the actual API endpoint
                    alert(`Campaign ${campaignId} cancelled`);
                    loadCampaigns();
                } catch (error) {
                    console.error('Error cancelling campaign:', error);
                    alert('Error cancelling campaign. Please try again.');
                }
            }
        }

        async function pauseAllCampaigns() {
            if (confirm('Are you sure you want to pause all active campaigns?')) {
                try {
                    // This would call the actual API endpoint
                    alert('All campaigns paused');
                    loadCampaigns();
                } catch (error) {
                    console.error('Error pausing campaigns:', error);
                    alert('Error pausing campaigns. Please try again.');
                }
            }
        }

        async function emergencyStop() {
            if (confirm('EMERGENCY STOP: This will immediately stop all outbound calls and campaigns. Are you sure?')) {
                try {
                    // This would call the actual API endpoint
                    alert('Emergency stop activated. All calls and campaigns stopped.');
                    loadDashboardData();
                } catch (error) {
                    console.error('Error during emergency stop:', error);
                    alert('Error during emergency stop. Please contact support.');
                }
            }
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modals = document.getElementsByClassName('modal');
            for (let modal of modals) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            }
        }
    </script>
</body>
</html>
