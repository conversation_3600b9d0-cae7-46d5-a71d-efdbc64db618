# AI Customer Support System - Git Ignore File

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
# .python-version

# pipenv
#Pipfile.lock

# poetry
#poetry.lock

# pdm
#pdm.lock
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Linux
*~

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# AI/ML specific files
# Model files (can be large)
*.pkl
*.joblib
*.h5
*.pb
*.onnx
*.tflite
models/
checkpoints/

# Data files (never commit sensitive data)
*.csv
*.json
*.parquet
*.feather
data/
datasets/
raw_data/
processed_data/

# Audio files (can be large)
*.wav
*.mp3
*.m4a
*.flac
*.aac
audio_files/
recordings/
voice_samples/
call_recordings/

# Configuration files with secrets
config.json
secrets.json
credentials.json
service-account-key.json
.env.local
.env.production
.env.staging

# Google Cloud specific
gcloud-service-key.json
google-credentials.json
*.json.key
bigquery-key.json

# Twilio credentials
twilio-config.json
twilio-credentials.json

# API keys and tokens (NEVER COMMIT THESE!)
api_keys.txt
tokens.txt
*.key
*.pem
*.crt
openai_key.txt
anthropic_key.txt

# BigQuery cache
.bigquery_cache/
bq_cache/

# Streamlit
.streamlit/
streamlit_cache/

# FastAPI
.fastapi/

# Logs and monitoring
logs/
*.log
*.out
*.err
app.log
error.log
access.log

# Database files
*.db
*.sqlite
*.sqlite3
local.db
dev.db
test.db

# Cache directories
.cache/
.pytest_cache/
.mypy_cache/
__pycache__/

# Coverage reports
htmlcov/
.coverage
coverage.xml
*.cover

# Profiling data
*.prof
*.pstats
*.mprof

# Memory dumps
*.hprof
*.dump

# JVM crash logs
hs_err_pid*

# Customer Support System Specific
# Customer data (NEVER commit real customer data)
customer_data/
real_customer_info/
production_data/
customer_database/
sensitive_data/

# Call recordings (privacy sensitive)
call_recordings/
voice_data/
conversation_logs/
audio_cache/

# Analytics and metrics
analytics_data/
metrics/
reports/
performance_data/

# Backup files
*.backup
backup/
backups/
db_backup/

# Local development
local_config.py
dev_settings.py
test_data/
sandbox/
experiments/
local_test/

# Build artifacts
build/
dist/
*.tar.gz
*.zip
*.whl

# Documentation build
docs/_build/
docs/build/

# Virtual environment activation scripts
activate
activate.bat
activate.ps1

# Performance and debugging
*.prof
*.pstats
debug_output/
trace_logs/

# System files
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Network capture files
*.pcap
*.cap

# Jupyter notebook outputs
*.ipynb_checkpoints/

# Docker
.dockerignore
docker-compose.override.yml

# Cloud Build
cloudbuild-local.yaml

# Terraform (if used for infrastructure)
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Kubernetes (if used)
*.kubeconfig
k8s-secrets.yaml

# Frontend (if applicable)
node_modules/
package-lock.json
yarn.lock
frontend/build/
frontend/dist/
frontend/.next/

# Test outputs
test_output/
test_results/
pytest_cache/

# IDE specific
.vscode/settings.json
.idea/workspace.xml

# Local configuration overrides
local_settings.py
local_config.yaml
dev.yaml
staging.yaml
production.yaml

# Monitoring and observability
monitoring_data/
traces/
spans/

# AI model artifacts
model_cache/
embeddings/
vector_store/
fine_tuned_models/