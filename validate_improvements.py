#!/usr/bin/env python3
"""
Validation script for customer support improvements
Tests all new features without requiring external dependencies
"""

import asyncio
import sys
import os
import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

# Mock structlog for testing
class MockLogger:
    def bind(self, **kwargs):
        return self
    def info(self, *args, **kwargs):
        pass
    def error(self, *args, **kwargs):
        pass

def get_logger():
    return MockLogger()

# Mock structlog module
sys.modules['structlog'] = type(sys)('structlog')
sys.modules['structlog'].get_logger = get_logger

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.sentiment_analyzer import SentimentAnalyzer, EmotionalState, EscalationLevel
from services.empathetic_response_generator import EmpatheticResponseGenerator
from services.order_history_summarizer import OrderHistorySummarizer
from services.action_link_generator import ActionLinkGenerator, ActionType


async def test_sentiment_analysis():
    """Test sentiment analysis functionality."""
    print("🧠 Testing Sentiment Analysis...")
    
    analyzer = SentimentAnalyzer()
    
    # Test cases
    test_cases = [
        {
            "message": "I'm absolutely furious! This service is terrible!",
            "expected_emotion": EmotionalState.ANGRY,
            "expected_escalation": [EscalationLevel.ESCALATE, EscalationLevel.MONITOR]
        },
        {
            "message": "Thank you so much! This is fantastic service!",
            "expected_emotion": EmotionalState.HAPPY,
            "expected_escalation": [EscalationLevel.NONE]
        },
        {
            "message": "I'm going to sue you idiots and report this!",
            "expected_emotion": EmotionalState.THREATENING,
            "expected_escalation": [EscalationLevel.URGENT_ESCALATE]
        },
        {
            "message": "I need help with my account please.",
            "expected_emotion": EmotionalState.NEUTRAL,
            "expected_escalation": [EscalationLevel.NONE]
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            result = await analyzer.analyze_sentiment(test_case["message"])
            
            emotion_correct = result.primary_emotion == test_case["expected_emotion"]
            escalation_correct = result.escalation_level in test_case["expected_escalation"]
            
            if emotion_correct and escalation_correct:
                print(f"  ✅ Test {i}: PASSED - {test_case['message'][:30]}...")
                passed += 1
            else:
                print(f"  ❌ Test {i}: FAILED - Expected {test_case['expected_emotion']}, got {result.primary_emotion}")
                
        except Exception as e:
            print(f"  ❌ Test {i}: ERROR - {str(e)}")
    
    print(f"  📊 Sentiment Analysis: {passed}/{total} tests passed\n")
    return passed == total


async def test_empathetic_responses():
    """Test empathetic response generation."""
    print("💝 Testing Empathetic Response Generation...")
    
    generator = EmpatheticResponseGenerator()
    analyzer = SentimentAnalyzer()
    
    test_messages = [
        "I'm really angry about this issue!",
        "Thank you for your excellent service!",
        "I'm disappointed with my recent order."
    ]
    
    passed = 0
    total = len(test_messages)
    
    for i, message in enumerate(test_messages, 1):
        try:
            # Analyze sentiment first
            sentiment = await analyzer.analyze_sentiment(message)
            
            # Generate empathetic response
            response = await generator.generate_empathetic_response(
                sentiment, message, {"first_name": "John"}
            )
            
            # Check if response has required components
            has_opening = bool(response.get("opening"))
            has_empathy = bool(response.get("empathy"))
            has_tone = bool(response.get("tone_adaptation"))
            
            if has_opening and has_empathy and has_tone:
                print(f"  ✅ Test {i}: PASSED - Generated empathetic response")
                passed += 1
            else:
                print(f"  ❌ Test {i}: FAILED - Missing response components")
                
        except Exception as e:
            print(f"  ❌ Test {i}: ERROR - {str(e)}")
    
    print(f"  📊 Empathetic Responses: {passed}/{total} tests passed\n")
    return passed == total


async def test_order_summarization():
    """Test order history summarization."""
    print("📋 Testing Order History Summarization...")
    
    summarizer = OrderHistorySummarizer()
    
    # Test customer with many orders
    frequent_customer = {
        "customer_id": "CUST_001",
        "total_orders": 25,
        "total_spent": 5000.0,
        "registration_date": "2022-01-01T00:00:00Z",
        "recent_orders": [
            {"order_id": "ORD_001", "date": "2024-01-15T00:00:00Z", "status": "delivered", "total": 199.99}
        ]
    }
    
    # Test new customer
    new_customer = {
        "customer_id": "CUST_002",
        "total_orders": 1,
        "total_spent": 50.0,
        "registration_date": "2024-01-01T00:00:00Z",
        "recent_orders": []
    }
    
    passed = 0
    total = 4
    
    try:
        # Test frequent customer summarization
        summary1 = await summarizer.summarize_order_history(frequent_customer)
        if summary1.loyalty_tier in ["VIP", "Premium"] and summary1.order_frequency in ["frequent", "regular"]:
            print("  ✅ Test 1: PASSED - Frequent customer summary")
            passed += 1
        else:
            print("  ❌ Test 1: FAILED - Incorrect frequent customer classification")
        
        # Test new customer summarization
        summary2 = await summarizer.summarize_order_history(new_customer)
        if summary2.loyalty_tier == "New" and summary2.order_frequency == "new":
            print("  ✅ Test 2: PASSED - New customer summary")
            passed += 1
        else:
            print("  ❌ Test 2: FAILED - Incorrect new customer classification")
        
        # Test summarization decision logic
        should_summarize_frequent = summarizer.should_summarize_instead_of_asking(frequent_customer)
        should_summarize_new = summarizer.should_summarize_instead_of_asking(new_customer)
        
        if should_summarize_frequent and not should_summarize_new:
            print("  ✅ Test 3: PASSED - Summarization decision logic")
            passed += 1
        else:
            print("  ❌ Test 3: FAILED - Incorrect summarization decision")
        
        # Test summary text generation
        if "VIP" in summary1.summary_text or "premium" in summary1.summary_text.lower():
            print("  ✅ Test 4: PASSED - Summary text generation")
            passed += 1
        else:
            print("  ❌ Test 4: FAILED - Summary text missing key information")
            
    except Exception as e:
        print(f"  ❌ Order Summarization: ERROR - {str(e)}")
    
    print(f"  📊 Order Summarization: {passed}/{total} tests passed\n")
    return passed == total


def test_action_links():
    """Test action link generation."""
    print("🔗 Testing Action Link Generation...")
    
    generator = ActionLinkGenerator("https://support.example.com")
    
    test_cases = [
        {
            "message": "Where is my order?",
            "expected_actions": [ActionType.TRACK_ORDER]
        },
        {
            "message": "I want a refund!",
            "expected_actions": [ActionType.REQUEST_REFUND]
        },
        {
            "message": "I need to return this item",
            "expected_actions": [ActionType.RETURN_ITEM]
        }
    ]
    
    passed = 0
    total = len(test_cases) + 2  # Additional tests for formatting and creation
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            actions = generator.generate_contextual_actions(test_case["message"], {})
            action_types = [action.action_type for action in actions]
            
            if any(expected in action_types for expected in test_case["expected_actions"]):
                print(f"  ✅ Test {i}: PASSED - Generated correct action for '{test_case['message'][:20]}...'")
                passed += 1
            else:
                print(f"  ❌ Test {i}: FAILED - Missing expected action types")
                
        except Exception as e:
            print(f"  ❌ Test {i}: ERROR - {str(e)}")
    
    # Test action formatting
    try:
        actions = generator.generate_contextual_actions("I need help", {})
        formatted = generator.format_actions_for_response(actions)
        
        if "Quick Actions:" in formatted and "Click here" in formatted:
            print("  ✅ Test 4: PASSED - Action formatting")
            passed += 1
        else:
            print("  ❌ Test 4: FAILED - Incorrect action formatting")
    except Exception as e:
        print(f"  ❌ Test 4: ERROR - {str(e)}")
    
    # Test individual action creation
    try:
        action = generator._create_action_link(ActionType.TRACK_ORDER, {"customer_id": "CUST_001"})
        
        if action and action.action_type == ActionType.TRACK_ORDER and "CUST_001" in action.url:
            print("  ✅ Test 5: PASSED - Individual action creation")
            passed += 1
        else:
            print("  ❌ Test 5: FAILED - Incorrect action creation")
    except Exception as e:
        print(f"  ❌ Test 5: ERROR - {str(e)}")
    
    print(f"  📊 Action Links: {passed}/{total} tests passed\n")
    return passed == total


async def test_integration():
    """Test integration of all improvements."""
    print("🔄 Testing Integration...")
    
    # Initialize all services
    sentiment_analyzer = SentimentAnalyzer()
    empathetic_generator = EmpatheticResponseGenerator()
    action_generator = ActionLinkGenerator()
    
    # Test angry customer flow
    message = "I'm furious! I want my money back!"
    customer_info = {"customer_id": "CUST_001", "first_name": "John"}
    
    try:
        # Full integration test
        sentiment = await sentiment_analyzer.analyze_sentiment(message, customer_info)
        empathetic_response = await empathetic_generator.generate_empathetic_response(
            sentiment, message, customer_info
        )
        actions = action_generator.generate_contextual_actions(message, customer_info, sentiment)
        
        # Check integration
        has_escalation = sentiment.escalation_level != EscalationLevel.NONE
        has_empathy = empathetic_response["empathy_level"] > 0.5
        has_refund_action = any(action.action_type == ActionType.REQUEST_REFUND for action in actions)
        
        if has_escalation and has_empathy and has_refund_action:
            print("  ✅ Integration Test: PASSED - All services work together")
            return True
        else:
            print("  ❌ Integration Test: FAILED - Services not properly integrated")
            return False
            
    except Exception as e:
        print(f"  ❌ Integration Test: ERROR - {str(e)}")
        return False


async def main():
    """Run all validation tests."""
    print("🚀 Validating Customer Support Improvements")
    print("=" * 50)
    
    results = []
    
    # Run all tests
    results.append(await test_sentiment_analysis())
    results.append(await test_empathetic_responses())
    results.append(await test_order_summarization())
    results.append(test_action_links())
    results.append(await test_integration())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("=" * 50)
    print(f"📊 VALIDATION SUMMARY: {passed}/{total} test suites passed")
    
    if passed == total:
        print("🎉 ALL IMPROVEMENTS VALIDATED SUCCESSFULLY!")
        print("\n✨ Features implemented:")
        print("  • Sentiment analysis and escalation detection")
        print("  • Emotional tone adaptation")
        print("  • Order history summarization")
        print("  • Action links and quick buttons")
        print("  • Full integration between all services")
    else:
        print("⚠️  Some tests failed. Please review the output above.")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
