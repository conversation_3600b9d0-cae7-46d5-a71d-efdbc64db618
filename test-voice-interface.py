#!/usr/bin/env python3
"""
Simple HTTP server to test the voice.html interface locally
This serves the HTML files and provides mock API endpoints for testing
"""

import http.server
import socketserver
import json
import os
from urllib.parse import urlparse, parse_qs
import threading
import time

class <PERSON>ckAP<PERSON>Handler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/':
            self.path = '/index.html'
        elif self.path == '/api':
            self.send_mock_api_info()
            return
        elif self.path == '/health':
            self.send_mock_health()
            return
        elif self.path == '/docs':
            self.send_mock_docs()
            return
        
        # Serve static files
        super().do_GET()
    
    def do_POST(self):
        """Handle POST requests with mock responses"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        if self.path == '/voice/customer-lookup':
            self.send_mock_customer_lookup(post_data)
        elif self.path == '/voice/introduction':
            self.send_mock_introduction(post_data)
        elif self.path == '/voice/process-voice-message':
            self.send_mock_voice_processing(post_data)
        else:
            self.send_error(404, "Endpoint not found")
    
    def send_mock_api_info(self):
        """Send mock API info"""
        response = {
            "name": "Smart Customer Support Orchestrator (TEST MODE)",
            "version": "1.0.0-test",
            "description": "Multi-Agent AI System for Automated Customer Support",
            "status": "running",
            "mode": "test"
        }
        self.send_json_response(response)
    
    def send_mock_health(self):
        """Send mock health check"""
        response = {
            "status": "healthy",
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S"),
            "mode": "test"
        }
        self.send_json_response(response)
    
    def send_mock_docs(self):
        """Redirect to FastAPI docs info"""
        docs_html = """
        <!DOCTYPE html>
        <html>
        <head><title>API Documentation</title></head>
        <body>
            <h1>API Documentation (Test Mode)</h1>
            <p>This is a test server. In production, you would see the full FastAPI documentation here.</p>
            <h2>Available Test Endpoints:</h2>
            <ul>
                <li><strong>GET /api</strong> - API information</li>
                <li><strong>GET /health</strong> - Health check</li>
                <li><strong>POST /voice/customer-lookup</strong> - Customer lookup (mock)</li>
                <li><strong>POST /voice/introduction</strong> - Voice introduction (mock)</li>
                <li><strong>POST /voice/process-voice-message</strong> - Voice processing (mock)</li>
            </ul>
            <p><a href="/voice.html">Test Voice Interface</a></p>
        </body>
        </html>
        """
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(docs_html.encode())
    
    def send_mock_customer_lookup(self, post_data):
        """Send mock customer lookup response"""
        try:
            data = json.loads(post_data.decode())
            email = data.get('customer_email', '')
            name = data.get('customer_name', '')
            
            # Mock customer data
            if email == '<EMAIL>' or name.lower() == 'john smith':
                response = {
                    "found": True,
                    "customer": {
                        "customer_id": "CUST_001",
                        "name": "John Smith",
                        "email": "<EMAIL>",
                        "customer_type": "premium",
                        "total_orders": 15,
                        "total_spent": 2500.75
                    }
                }
            else:
                response = {
                    "found": False,
                    "customer": None
                }
            
            self.send_json_response(response)
        except Exception as e:
            self.send_error(400, f"Invalid request: {str(e)}")
    
    def send_mock_introduction(self, post_data):
        """Send mock voice introduction"""
        try:
            data = json.loads(post_data.decode())
            customer_name = data.get('customer_name', '')
            
            # Create mock audio response (empty MP3-like data)
            mock_audio = b'\x00' * 1024  # Mock audio data
            
            intro_text = f"Hello {customer_name}! I'm Angela from customer support. How can I help you today?" if customer_name else "Hello! I'm Angela from customer support. How can I assist you today?"
            
            self.send_response(200)
            self.send_header('Content-Type', 'audio/mpeg')
            self.send_header('X-Angela-Message', intro_text)
            self.send_header('X-Customer-Identified', 'true' if customer_name else 'false')
            self.send_header('X-Customer-ID', 'CUST_001' if customer_name else 'unknown')
            self.send_header('X-Customer-Name', customer_name)
            self.end_headers()
            self.wfile.write(mock_audio)
        except Exception as e:
            self.send_error(400, f"Invalid request: {str(e)}")
    
    def send_mock_voice_processing(self, post_data):
        """Send mock voice processing response"""
        response = {
            "transcript": "What's my order status?",
            "ai_response": "I can help you check your order status. Let me look that up for you right away.",
            "audio_response": "00112233445566778899aabbccddeeff",  # Mock hex audio data
            "customer_id": "CUST_001",
            "conversation_id": "conv_test_123",
            "conversation_continues": True
        }
        self.send_json_response(response)
    
    def send_json_response(self, data):
        """Send JSON response with CORS headers"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def main():
    PORT = 8082
    
    print("🧪 Starting Voice Interface Test Server...")
    print(f"📁 Serving files from: {os.getcwd()}")
    print(f"🌐 Server running at: http://localhost:{PORT}")
    print(f"🎤 Voice Interface: http://localhost:{PORT}/voice.html")
    print(f"📚 API Docs: http://localhost:{PORT}/docs")
    print(f"❤️  Health Check: http://localhost:{PORT}/health")
    print("\n🔧 This is a TEST SERVER with mock API responses")
    print("   Real voice processing is not available in test mode")
    print("\n⏹️  Press Ctrl+C to stop the server")
    print("-" * 60)
    
    with socketserver.TCPServer(("", PORT), MockAPIHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n\n🛑 Server stopped by user")
            print("✅ Test completed")

if __name__ == "__main__":
    main()
