"""Main entry point for the Smart Customer Support Orchestrator."""

import asyncio
import argparse
import sys
import os
from datetime import datetime

from src.api.main import app
from src.orchestrator import SupportOrchestrator
from src.models import CustomerMessage
from src.config import settings
import uvicorn
import structlog


async def run_demo():
    """Run a simple demo of the orchestrator."""
    print("🤖 Smart Customer Support Orchestrator Demo")
    print("=" * 50)
    
    # Initialize orchestrator
    orchestrator = SupportOrchestrator()
    
    # Demo messages
    demo_messages = [
        {
            "customer_id": "demo_001",
            "content": "My order #12345 arrived damaged! I'm very upset and need a refund immediately!",
            "scenario": "Angry Customer - Damaged Order"
        },
        {
            "customer_id": "demo_002", 
            "content": "Hi, I'm having trouble logging into my account. The password reset isn't working.",
            "scenario": "Technical Support - Account Access"
        },
        {
            "customer_id": "demo_003",
            "content": "Thank you so much for the excellent service! The product exceeded my expectations.",
            "scenario": "Positive Feedback"
        }
    ]
    
    for i, demo in enumerate(demo_messages, 1):
        print(f"\n📨 Demo {i}: {demo['scenario']}")
        print("-" * 30)
        print(f"Customer Message: {demo['content']}")
        
        # Create customer message
        customer_message = CustomerMessage(
            message_id=f"demo_{i}_{datetime.now().strftime('%H%M%S')}",
            customer_id=demo["customer_id"],
            content=demo["content"],
            channel="demo"
        )
        
        try:
            # Process message
            print("\n🔄 Processing through agent workflow...")
            ticket = await orchestrator.process_customer_message(customer_message)
            
            # Display results
            print(f"\n✅ Processing Complete!")
            print(f"Ticket ID: {ticket.ticket_id}")
            print(f"Intent: {ticket.intake_result.intent}")
            print(f"Sentiment: {ticket.intake_result.sentiment.value}")
            print(f"Priority: {ticket.intake_result.priority.value}")
            print(f"Quality Score: {ticket.quality_result.quality_score:.2f}")
            print(f"Escalated: {'Yes' if ticket.escalated else 'No'}")
            print(f"Processing Time: {ticket.total_processing_time:.2f}s")
            print(f"\n💬 Final Response:")
            print(f'"{ticket.final_response}"')
            
        except Exception as e:
            print(f"❌ Error processing message: {str(e)}")
        
        print("\n" + "=" * 50)
    
    # Show system metrics
    print("\n📊 System Metrics:")
    metrics = await orchestrator.get_system_metrics()
    print(f"Total Processed: {metrics.total_tickets_processed}")
    print(f"Average Response Time: {metrics.average_response_time:.2f}s")
    print(f"Escalation Rate: {metrics.escalation_rate:.1%}")
    
    await orchestrator.shutdown()


async def run_interactive_demo():
    """Run an interactive demo where users can input messages."""
    print("🤖 Interactive Smart Customer Support Demo")
    print("=" * 50)
    print("Enter customer messages to see how the AI agents process them.")
    print("Type 'quit' to exit.\n")
    
    orchestrator = SupportOrchestrator()
    
    try:
        while True:
            # Get user input
            user_input = input("Customer Message: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            if not user_input:
                continue
            
            # Create customer message
            customer_message = CustomerMessage(
                message_id=f"interactive_{datetime.now().strftime('%H%M%S')}",
                customer_id="interactive_user",
                content=user_input,
                channel="interactive"
            )
            
            try:
                print("\n🔄 Processing...")
                ticket = await orchestrator.process_customer_message(customer_message)
                
                print(f"\n📋 Analysis Results:")
                print(f"  Intent: {ticket.intake_result.intent}")
                print(f"  Sentiment: {ticket.intake_result.sentiment.value}")
                print(f"  Priority: {ticket.intake_result.priority.value}")
                print(f"  Urgency: {ticket.intake_result.urgency_score:.2f}")
                
                print(f"\n💬 AI Response:")
                print(f'"{ticket.final_response}"')
                
                if ticket.escalated:
                    print(f"\n⚠️  Escalated: {ticket.escalation_reason}")
                
                print(f"\n⏱️  Processing Time: {ticket.total_processing_time:.2f}s")
                print("-" * 50)
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
    
    finally:
        await orchestrator.shutdown()


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Smart Customer Support Orchestrator")
    parser.add_argument("--mode", choices=["api", "demo", "interactive"], default="api",
                       help="Run mode: api server, demo, or interactive demo")
    parser.add_argument("--host", default=settings.api_host, help="API host")
    parser.add_argument("--port", type=int, help="API port")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()

    # Cloud Run sets PORT environment variable
    if args.port is None:
        args.port = int(os.environ.get('PORT', 8080))

    if args.debug:
        import logging
        logging.basicConfig(level=logging.DEBUG)
    
    if args.mode == "api":
        print("🚀 Starting Smart Customer Support Orchestrator API")
        print(f"📡 Server will be available at: http://{args.host}:{args.port}")
        print("📖 API docs at: http://{args.host}:{args.port}/docs")
        print("🏥 Health check at: http://{args.host}:{args.port}/health")
        
        try:
            # Import and test the app first
            print("📦 Loading FastAPI application...")
            from src.api.main import app
            print("✅ FastAPI app loaded successfully")

            uvicorn.run(
                app,  # Use app object directly
                host=args.host,
                port=args.port,
                workers=1,
                log_level="info" if not args.debug else "debug",
                reload=False,  # Disable reload in production
                access_log=True
            )
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    
    elif args.mode == "demo":
        asyncio.run(run_demo())
    
    elif args.mode == "interactive":
        asyncio.run(run_interactive_demo())


if __name__ == "__main__":
    main()
