[tool:pytest]
# Pytest configuration for Smart Customer Support Orchestrator

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    unit: Unit tests for individual components
    integration: Integration tests across multiple components
    performance: Performance and load testing
    slow: Tests that take longer to run
    bigquery: Tests requiring BigQuery integration
    vertex_ai: Tests requiring Vertex AI integration

# Output options
addopts = 
    -v
    --strict-markers
    --tb=short
    --durations=10
    --color=yes
    --disable-warnings

# Async support
asyncio_mode = auto

# Minimum version
minversion = 7.0

# Test timeout (in seconds)
timeout = 300

# Coverage options (if pytest-cov is installed)
# addopts = --cov=src --cov-report=html --cov-report=term-missing --cov-fail-under=80

# Filtering
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:google.*
    ignore::UserWarning:vertexai.*

# Log configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Parallel execution (if pytest-xdist is installed)
# addopts = -n auto

# Test collection
collect_ignore = setup.py build dist .git __pycache__
