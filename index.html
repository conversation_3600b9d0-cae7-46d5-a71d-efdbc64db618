<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Customer Support System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 40px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .feature h3 {
            color: #333;
            margin: 0 0 10px 0;
        }
        .feature p {
            color: #666;
            margin: 0;
            font-size: 0.9rem;
        }
        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 40px;
        }
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }
        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        .status {
            margin-top: 30px;
            padding: 15px;
            background: #d4edda;
            color: #155724;
            border-radius: 10px;
            border: 1px solid #c3e6cb;
        }
        .api-info {
            margin-top: 30px;
            text-align: left;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🤖</div>
        <h1>AI Customer Support</h1>
        <p class="subtitle">Intelligent Voice-Powered Customer Service</p>
        
        <div class="features">
            <div class="feature">
                <h3>🎤 Voice Interface</h3>
                <p>Natural conversation with Angela, your AI assistant</p>
            </div>
            <div class="feature">
                <h3>🧠 Smart AI</h3>
                <p>Advanced multi-agent system with emotional intelligence</p>
            </div>
            <div class="feature">
                <h3>📊 Customer Data</h3>
                <p>Instant access to customer history and preferences</p>
            </div>
            <div class="feature">
                <h3>⚡ Real-time</h3>
                <p>Fast speech-to-text and text-to-speech processing</p>
            </div>
        </div>

        <div class="buttons">
            <a href="voice.html" class="btn btn-primary">🎤 Start Voice Chat</a>
            <a href="test_voice_system.html" class="btn btn-secondary">🧪 Test System</a>
            <a href="docs" class="btn btn-secondary">📚 API Docs</a>
        </div>

        <div class="status" id="status">
            ✅ System Status: <span id="statusText">Checking...</span>
        </div>

        <div class="api-info">
            <strong>🔗 API Endpoints:</strong><br>
            • <code>GET /health</code> - System health check<br>
            • <code>POST /voice/customer-lookup</code> - Customer database lookup<br>
            • <code>POST /voice/introduction</code> - AI voice introduction<br>
            • <code>POST /voice/text-to-speech</code> - Convert text to speech<br>
            • <code>POST /voice/speech-to-text</code> - Convert speech to text<br>
            • <code>POST /voice/process-voice-message</code> - End-to-end voice processing
        </div>
    </div>

    <script>
        // Check system health on page load
        async function checkHealth() {
            const statusElement = document.getElementById('statusText');
            try {
                const response = await fetch('/health');
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    statusElement.textContent = 'Online and Ready';
                    statusElement.style.color = '#155724';
                } else {
                    statusElement.textContent = 'System Issues Detected';
                    statusElement.style.color = '#721c24';
                }
            } catch (error) {
                statusElement.textContent = 'Connection Error';
                statusElement.style.color = '#721c24';
            }
        }

        // Check health when page loads
        window.addEventListener('load', checkHealth);
    </script>
</body>
</html>
