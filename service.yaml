apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: ai-customer-support
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Auto-scaling configuration
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "10"
        
        # Resource allocation
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "2"
        
        # Networking
        run.googleapis.com/execution-environment: gen2
        
        # Timeout
        run.googleapis.com/timeout: "300s"
        
        # Concurrency
        run.googleapis.com/cpu-throttling: "false"
    spec:
      containerConcurrency: 100
      timeoutSeconds: 300
      containers:
      - image: gcr.io/pro-course-433716-v0/ai-customer-support:latest
        ports:
        - name: http1
          containerPort: 8080
        env:
        - name: GOOGLE_CLOUD_PROJECT
          value: "pro-course-433716-v0"
        - name: BIGQUERY_DATASET
          value: "customer_support"
        - name: GOOGLE_CLOUD_REGION
          value: "us-central1"
        - name: PORT
          value: "8080"
        - name: PYTHONPATH
          value: "/app"
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
          requests:
            cpu: "1"
            memory: "1Gi"
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
  traffic:
  - percent: 100
    latestRevision: true
