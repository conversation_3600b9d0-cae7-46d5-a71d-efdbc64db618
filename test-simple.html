<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 600px;
            margin: 0 auto;
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        input {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 5px;
            width: 200px;
        }
        #output {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Voice Interface Test</h1>
        <p>This is a simple test to verify JavaScript functionality.</p>
        
        <div>
            <input type="email" id="testEmail" placeholder="Enter email (try: <EMAIL>)" />
            <button id="testLookup">Test Customer Lookup</button>
        </div>
        
        <div>
            <button id="testCall">Test Start Call</button>
            <button id="testAPI">Test API Connection</button>
        </div>
        
        <div id="output">
            <strong>Output:</strong><br>
            <div id="results">Ready for testing...</div>
        </div>
    </div>

    <script>
        // Add global error handler
        window.addEventListener('error', (e) => {
            console.error('JavaScript Error:', e.error);
            addOutput(`❌ JavaScript Error: ${e.message} at line ${e.lineno}`);
        });

        // API Base URL
        const API_BASE = window.location.origin;
        
        function addOutput(message) {
            const results = document.getElementById('results');
            results.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
        }

        // Test customer lookup
        async function testCustomerLookup() {
            const email = document.getElementById('testEmail').value.trim();
            addOutput(`🔍 Testing customer lookup for: ${email || 'empty email'}`);
            
            try {
                const response = await fetch(`${API_BASE}/voice/customer-lookup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_email: email,
                        customer_name: '',
                        customer_id: ''
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    addOutput(`✅ Customer lookup successful: ${JSON.stringify(result)}`);
                } else {
                    addOutput(`❌ Customer lookup failed: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                addOutput(`❌ Customer lookup error: ${error.message}`);
            }
        }

        // Test start call
        async function testStartCall() {
            addOutput(`📞 Testing start call...`);
            
            try {
                const response = await fetch(`${API_BASE}/voice/introduction`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_name: 'Test User',
                        customer_email: '<EMAIL>',
                        customer_id: ''
                    })
                });
                
                if (response.ok) {
                    addOutput(`✅ Start call successful: Got audio response`);
                    addOutput(`📝 Angela Message: ${response.headers.get('X-Angela-Message') || 'No message header'}`);
                } else {
                    addOutput(`❌ Start call failed: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                addOutput(`❌ Start call error: ${error.message}`);
            }
        }

        // Test API connection
        async function testAPIConnection() {
            addOutput(`🌐 Testing API connection to: ${API_BASE}`);
            
            try {
                const response = await fetch(`${API_BASE}/api`);
                if (response.ok) {
                    const data = await response.json();
                    addOutput(`✅ API connection successful: ${JSON.stringify(data)}`);
                } else {
                    addOutput(`❌ API connection failed: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                addOutput(`❌ API connection error: ${error.message}`);
            }
        }

        // Initialize when page loads
        window.addEventListener('load', () => {
            addOutput(`🚀 Page loaded successfully`);
            addOutput(`🌐 API Base URL: ${API_BASE}`);
            
            // Set up event listeners
            document.getElementById('testLookup').addEventListener('click', testCustomerLookup);
            document.getElementById('testCall').addEventListener('click', testStartCall);
            document.getElementById('testAPI').addEventListener('click', testAPIConnection);
            
            addOutput(`✅ Event listeners attached successfully`);
            
            // Auto-test API connection
            testAPIConnection();
        });
    </script>
</body>
</html>
