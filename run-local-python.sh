#!/bin/bash

# Smart Customer Support Orchestrator - Local Python Runner
echo "🚀 Running Smart Customer Support Orchestrator locally with Python..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed. Please install Python 3 and try again."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found. Please create it with your configuration."
    exit 1
fi

print_status "Checking Python version..."
python3 --version

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_status "Creating virtual environment..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        print_error "Failed to create virtual environment"
        exit 1
    fi
    print_success "Virtual environment created"
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Install/upgrade pip
print_status "Upgrading pip..."
pip install --upgrade pip

# Install requirements
print_status "Installing requirements..."
if pip install -r requirements.txt; then
    print_success "Requirements installed successfully"
else
    print_error "Failed to install requirements"
    exit 1
fi

# Check for Google Cloud credentials
if [ ! -f "src/vertexai.json" ]; then
    print_warning "Google Cloud service account key not found at src/vertexai.json"
    print_warning "Voice features may not work without proper credentials"
fi

# Set environment variables
print_status "Loading environment variables..."
export $(cat .env | grep -v '^#' | xargs)

# Set Python path
export PYTHONPATH=$(pwd)

print_status "Starting application on port 8080..."
echo ""
echo "🌐 Application will be available at:"
echo "   • Main Interface: http://localhost:8080"
echo "   • Voice Interface: http://localhost:8080/voice.html"
echo "   • API Documentation: http://localhost:8080/docs"
echo "   • Health Check: http://localhost:8080/health"
echo ""
echo "Press Ctrl+C to stop the application"
echo ""

# Run the application
python -m uvicorn src.api.main:app --host 0.0.0.0 --port 8080 --reload
