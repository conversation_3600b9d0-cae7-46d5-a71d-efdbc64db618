<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Support Actions - Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .action-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .action-btn {
            display: inline-block;
            padding: 15px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .action-btn.priority-1 {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        
        .action-btn.priority-2 {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        }
        
        .action-btn.priority-3 {
            background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
        }
        
        .demo-message {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        
        .success-message {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4caf50;
        }
        
        .back-link {
            text-align: center;
            margin-top: 30px;
        }
        
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Customer Support Actions Demo</h1>
        
        <div class="demo-message">
            <strong>Demo Mode:</strong> This page demonstrates the action links functionality. 
            In a real implementation, these would connect to actual customer service systems.
        </div>
        
        <div class="action-section">
            <h2>📦 Order Management</h2>
            <div class="action-buttons">
                <a href="#" class="action-btn priority-1" onclick="showAction('Track Order')">
                    📦 Track Your Order
                </a>
                <a href="#" class="action-btn priority-2" onclick="showAction('Cancel Order')">
                    ❌ Cancel Order
                </a>
                <a href="#" class="action-btn priority-3" onclick="showAction('Update Address')">
                    🏠 Update Delivery Address
                </a>
            </div>
        </div>
        
        <div class="action-section">
            <h2>💰 Returns & Refunds</h2>
            <div class="action-buttons">
                <a href="#" class="action-btn priority-2" onclick="showAction('Request Refund')">
                    💰 Request Refund
                </a>
                <a href="#" class="action-btn priority-2" onclick="showAction('Return Item')">
                    📮 Return Item
                </a>
            </div>
        </div>
        
        <div class="action-section">
            <h2>🎧 Support & Help</h2>
            <div class="action-buttons">
                <a href="#" class="action-btn priority-1" onclick="showAction('Escalate Issue')">
                    ⬆️ Escalate to Manager
                </a>
                <a href="#" class="action-btn priority-3" onclick="showAction('Contact Support')">
                    💬 Contact Support
                </a>
                <a href="#" class="action-btn priority-3" onclick="showAction('View Account')">
                    👤 View Account
                </a>
            </div>
        </div>
        
        <div class="action-section">
            <h2>⭐ Additional Services</h2>
            <div class="action-buttons">
                <a href="#" class="action-btn priority-3" onclick="showAction('Reorder Items')">
                    🔄 Reorder Items
                </a>
                <a href="#" class="action-btn priority-3" onclick="showAction('Leave Review')">
                    ⭐ Leave Review
                </a>
            </div>
        </div>
        
        <div id="action-result" style="display: none;"></div>
        
        <div class="back-link">
            <a href="/">← Back to Customer Support Interface</a>
        </div>
    </div>
    
    <script>
        function showAction(actionName) {
            const resultDiv = document.getElementById('action-result');
            resultDiv.innerHTML = `
                <div class="success-message">
                    <strong>✅ Action Triggered:</strong> ${actionName}<br>
                    <em>In a real system, this would redirect you to the appropriate service page or start the requested process.</em>
                </div>
            `;
            resultDiv.style.display = 'block';
            
            // Scroll to result
            resultDiv.scrollIntoView({ behavior: 'smooth' });
            
            // Hide after 5 seconds
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
