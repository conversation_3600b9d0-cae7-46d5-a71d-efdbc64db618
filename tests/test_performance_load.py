"""Performance and load testing for the Smart Customer Support Orchestrator."""

import pytest
import asyncio
import time
import statistics
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List
import concurrent.futures

from src.orchestrator import SupportOrchestrator
from src.models import CustomerMessage


class TestPerformanceAndLoad:
    """Performance and load testing suite."""
    
    @pytest.fixture
    def performance_orchestrator(self):
        """Create orchestrator optimized for performance testing."""
        with patch('google.cloud.bigquery.Client'), \
             patch('google.cloud.storage.Client'), \
             patch('src.adk.agent_framework.aiplatform.init'), \
             patch('src.adk.google_cloud_integration.CloudServicesManager'), \
             patch('src.orchestrator.structlog.get_logger'):
            return SupportOrchestrator(use_adk=True)
    
    @pytest.fixture
    def load_test_messages(self):
        """Generate messages for load testing."""
        message_templates = [
            "I need help with my order #{order_id}",
            "My account is locked and I can't access it",
            "The service is down and affecting my business",
            "I want to cancel my subscription",
            "Your billing system charged me incorrectly",
            "I'm very happy with your service!",
            "Technical issue with API integration",
            "Request for feature enhancement",
            "Complaint about customer service",
            "Question about pricing plans"
        ]
        
        messages = []
        for i in range(100):
            template = message_templates[i % len(message_templates)]
            content = template.replace("{order_id}", f"ORD{1000 + i}")
            
            messages.append(CustomerMessage(
                message_id=f"load_msg_{i:03d}",
                customer_id=f"cust_{i:03d}",
                content=content,
                channel="api" if i % 3 == 0 else "web",
                metadata={
                    "load_test": True,
                    "batch": i // 10,
                    "priority": "high" if i % 5 == 0 else "normal"
                }
            ))
        
        return messages
    
    @pytest.mark.asyncio
    async def test_single_message_processing_time(self, performance_orchestrator):
        """Test processing time for a single message."""
        test_message = CustomerMessage(
            message_id="perf_single",
            customer_id="perf_customer",
            content="I need help with my account",
            channel="web"
        )
        
        # Mock fast processing
        with patch.object(performance_orchestrator.adk_orchestrator, 'process_customer_message') as mock_process:
            mock_process.return_value = {
                "workflow_id": "perf_single",
                "processing_time": 1.2,
                "success": True
            }
            
            # Measure processing time
            start_time = time.time()
            result = await performance_orchestrator.process_message(test_message)
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # Verify performance
            assert result is not None
            assert processing_time < 5.0  # Should process in under 5 seconds
            mock_process.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_concurrent_message_processing(self, performance_orchestrator, load_test_messages):
        """Test concurrent processing of multiple messages."""
        # Use first 20 messages for concurrent test
        concurrent_messages = load_test_messages[:20]
        
        # Mock concurrent processing
        with patch.object(performance_orchestrator.adk_orchestrator, 'process_customer_message') as mock_process:
            mock_process.return_value = {
                "workflow_id": "concurrent_test",
                "processing_time": 1.5,
                "success": True,
                "agents_involved": 3
            }
            
            # Process messages concurrently
            start_time = time.time()
            tasks = [performance_orchestrator.process_message(msg) for msg in concurrent_messages]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            # Verify concurrent processing
            assert len(results) == 20
            successful_results = [r for r in results if not isinstance(r, Exception)]
            assert len(successful_results) >= 18  # Allow for some failures in testing
            
            # Should process 20 messages faster than 20 * single_message_time
            assert total_time < 60.0  # Should complete in under 1 minute
            assert mock_process.call_count == 20
    
    @pytest.mark.asyncio
    async def test_brain_state_performance_under_load(self, performance_orchestrator):
        """Test brain state updates performance under load."""
        brain_intensive_messages = []
        for i in range(50):
            brain_intensive_messages.append(CustomerMessage(
                message_id=f"brain_load_{i}",
                customer_id=f"brain_cust_{i}",
                content=f"Complex emotional issue requiring brain state updates {i}",
                channel="chat",
                metadata={"emotional_complexity": "high", "brain_updates_required": True}
            ))
        
        # Mock brain state intensive processing
        with patch.object(performance_orchestrator.adk_orchestrator, 'process_customer_message') as mock_process:
            def mock_brain_processing(message):
                return {
                    "workflow_id": f"brain_{message.message_id}",
                    "processing_time": 2.0,
                    "brain_state_updates": {
                        "emotional_agent": {"stress_level": 0.3, "empathy_sensitivity": 0.8},
                        "consciousness_evolution": {"awareness_level": 0.85},
                        "memory_consolidation": {"patterns_learned": 3}
                    },
                    "success": True
                }
            
            mock_process.side_effect = mock_brain_processing
            
            # Process brain-intensive messages
            start_time = time.time()
            tasks = [performance_orchestrator.process_message(msg) for msg in brain_intensive_messages]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            # Verify brain state performance
            assert len(results) == 50
            successful_results = [r for r in results if not isinstance(r, Exception)]
            assert len(successful_results) >= 45  # Allow for some test failures
            
            # Brain state updates should not significantly slow down processing
            assert total_time < 120.0  # Should complete in under 2 minutes
    
    @pytest.mark.asyncio
    async def test_swarm_intelligence_scaling(self, performance_orchestrator):
        """Test swarm intelligence performance scaling."""
        complex_messages = []
        for i in range(10):
            complex_messages.append(CustomerMessage(
                message_id=f"swarm_complex_{i}",
                customer_id=f"swarm_cust_{i}",
                content=f"Extremely complex technical issue requiring swarm intelligence {i}",
                channel="api",
                metadata={
                    "complexity": "very_high",
                    "requires_swarm": True,
                    "business_impact": "critical"
                }
            ))
        
        # Mock swarm intelligence processing
        with patch.object(performance_orchestrator.adk_orchestrator, 'process_customer_message') as mock_process:
            def mock_swarm_processing(message):
                return {
                    "workflow_id": f"swarm_{message.message_id}",
                    "swarm_activated": True,
                    "primary_agent": "technical_agent",
                    "spawned_agents": [f"specialist_{j}" for j in range(3)],
                    "swarm_processing_time": 5.0,
                    "collective_analysis_time": 2.0,
                    "consensus_building_time": 1.5,
                    "total_processing_time": 8.5,
                    "success": True
                }
            
            mock_process.side_effect = mock_swarm_processing
            
            # Process swarm-intensive messages
            start_time = time.time()
            tasks = [performance_orchestrator.process_message(msg) for msg in complex_messages]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            # Verify swarm scaling
            assert len(results) == 10
            successful_results = [r for r in results if not isinstance(r, Exception)]
            assert len(successful_results) >= 8  # Allow for some test failures
            
            # Swarm intelligence should scale efficiently
            assert total_time < 180.0  # Should complete in under 3 minutes
    
    @pytest.mark.asyncio
    async def test_memory_consolidation_performance(self, performance_orchestrator):
        """Test memory consolidation performance impact."""
        memory_messages = []
        for i in range(30):
            memory_messages.append(CustomerMessage(
                message_id=f"memory_{i}",
                customer_id=f"memory_cust_{i}",
                content=f"Learning scenario {i} for memory consolidation testing",
                channel="web",
                metadata={
                    "learning_scenario": True,
                    "memory_importance": 0.8,
                    "pattern_complexity": "medium"
                }
            ))
        
        # Mock memory consolidation processing
        with patch.object(performance_orchestrator.adk_orchestrator, 'process_customer_message') as mock_process:
            def mock_memory_processing(message):
                return {
                    "workflow_id": f"memory_{message.message_id}",
                    "processing_time": 1.8,
                    "memory_consolidation": {
                        "patterns_identified": 5,
                        "memories_updated": 3,
                        "consolidation_time": 0.5
                    },
                    "brain_state_updates": {
                        "memory_agent": {
                            "pattern_recognition_accuracy": 0.92,
                            "memory_confidence": 0.88
                        }
                    },
                    "success": True
                }
            
            mock_process.side_effect = mock_memory_processing
            
            # Process memory-intensive messages
            start_time = time.time()
            tasks = [performance_orchestrator.process_message(msg) for msg in memory_messages]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            # Verify memory consolidation performance
            assert len(results) == 30
            successful_results = [r for r in results if not isinstance(r, Exception)]
            assert len(successful_results) >= 27  # Allow for some test failures
            
            # Memory consolidation should not severely impact performance
            assert total_time < 90.0  # Should complete in under 1.5 minutes
    
    @pytest.mark.asyncio
    async def test_emotional_contagion_resistance_performance(self, performance_orchestrator):
        """Test performance impact of emotional contagion resistance."""
        # Create sequence of increasingly negative messages
        negative_messages = []
        emotions = ["frustrated", "angry", "furious", "livid", "enraged"]
        
        for i in range(25):
            emotion = emotions[i % len(emotions)]
            negative_messages.append(CustomerMessage(
                message_id=f"negative_{i}",
                customer_id=f"negative_cust_{i}",
                content=f"I am {emotion} about this terrible service! This is unacceptable!",
                channel="chat",
                metadata={
                    "emotional_intensity": 0.7 + (i % 5) * 0.05,
                    "emotional_contagion_test": True
                }
            ))
        
        # Mock emotional contagion processing
        with patch.object(performance_orchestrator.adk_orchestrator, 'process_customer_message') as mock_process:
            def mock_emotional_processing(message):
                msg_index = int(message.message_id.split('_')[1])
                return {
                    "workflow_id": f"emotional_{message.message_id}",
                    "processing_time": 2.2,
                    "emotional_analysis": {
                        "customer_emotion": "negative",
                        "agent_emotional_state": "stable_empathy",
                        "contagion_resistance_applied": msg_index > 10,
                        "resistance_processing_time": 0.3 if msg_index > 10 else 0.0
                    },
                    "brain_state_updates": {
                        "emotional_agent": {
                            "emotional_contagion_resistance": min(1.0, 0.5 + msg_index * 0.02),
                            "stress_level": min(0.8, 0.2 + msg_index * 0.01)
                        }
                    },
                    "success": True
                }
            
            mock_process.side_effect = mock_emotional_processing
            
            # Process emotional sequence
            start_time = time.time()
            tasks = [performance_orchestrator.process_message(msg) for msg in negative_messages]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            # Verify emotional contagion resistance performance
            assert len(results) == 25
            successful_results = [r for r in results if not isinstance(r, Exception)]
            assert len(successful_results) >= 22  # Allow for some test failures
            
            # Emotional contagion resistance should not significantly impact performance
            assert total_time < 120.0  # Should complete in under 2 minutes
    
    def test_performance_metrics_collection(self, performance_orchestrator):
        """Test performance metrics collection and analysis."""
        # Simulate performance data collection
        processing_times = [1.2, 1.5, 0.8, 2.1, 1.3, 1.7, 0.9, 1.4, 1.6, 1.1]
        brain_update_times = [0.3, 0.4, 0.2, 0.5, 0.3, 0.4, 0.2, 0.3, 0.4, 0.3]
        memory_consolidation_times = [0.5, 0.6, 0.4, 0.7, 0.5, 0.6, 0.4, 0.5, 0.6, 0.5]
        
        # Calculate performance metrics
        avg_processing_time = statistics.mean(processing_times)
        max_processing_time = max(processing_times)
        min_processing_time = min(processing_times)
        std_processing_time = statistics.stdev(processing_times)
        
        avg_brain_update_time = statistics.mean(brain_update_times)
        avg_memory_time = statistics.mean(memory_consolidation_times)
        
        # Verify performance metrics
        assert avg_processing_time < 2.0  # Average should be under 2 seconds
        assert max_processing_time < 3.0  # Max should be under 3 seconds
        assert min_processing_time > 0.5  # Min should be reasonable
        assert std_processing_time < 1.0  # Standard deviation should be low
        
        assert avg_brain_update_time < 0.5  # Brain updates should be fast
        assert avg_memory_time < 0.7  # Memory consolidation should be efficient
        
        # Performance should be consistent
        performance_consistency = std_processing_time / avg_processing_time
        assert performance_consistency < 0.5  # Coefficient of variation should be low
