"""End-to-end integration tests for the complete Smart Customer Support Orchestrator."""

import pytest
import asyncio
import json
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List

from src.orchestrator import SupportOrchestrator
from src.models import CustomerMessage
from src.adk.adk_orchestrator import ADKSmartSupportOrchestrator


class TestEndToEndIntegration:
    """Comprehensive end-to-end integration tests."""
    
    @pytest.fixture
    def mock_all_services(self):
        """Mock all external services for integration testing."""
        with patch('google.cloud.bigquery.Client') as mock_bq, \
             patch('google.cloud.storage.Client') as mock_storage, \
             patch('src.adk.agent_framework.aiplatform.init') as mock_vertex, \
             patch('src.adk.google_cloud_integration.CloudServicesManager') as mock_cloud:
            
            # Mock BigQuery
            mock_bq_instance = MagicMock()
            mock_bq.return_value = mock_bq_instance
            mock_job = MagicMock()
            mock_job.result.return_value = []
            mock_bq_instance.query.return_value = mock_job
            
            # Mock Cloud Storage
            mock_storage_instance = MagicMock()
            mock_storage.return_value = mock_storage_instance
            
            # Mock Cloud Services Manager
            mock_cloud_instance = MagicMock()
            mock_cloud.return_value = mock_cloud_instance
            mock_cloud_instance.vertex_ai.generate_content_async = AsyncMock(
                return_value=MagicMock(text="Mock AI response")
            )
            
            yield {
                'bigquery': mock_bq_instance,
                'storage': mock_storage_instance,
                'cloud_services': mock_cloud_instance
            }
    
    @pytest.fixture
    def support_orchestrator(self, mock_all_services):
        """Create support orchestrator with mocked services."""
        with patch('src.orchestrator.structlog.get_logger'):
            return SupportOrchestrator(use_adk=True)
    
    @pytest.fixture
    def complex_customer_scenarios(self):
        """Complex customer scenarios for testing."""
        return [
            {
                "name": "angry_customer_billing_dispute",
                "message": CustomerMessage(
                    message_id="angry_001",
                    customer_id="cust_angry_001",
                    content="I am absolutely furious! You charged me twice for the same service and your support team has been completely useless. I want a full refund immediately and I'm considering legal action!",
                    channel="phone",
                    metadata={"call_duration": 300, "previous_contacts": 3}
                ),
                "expected_outcomes": {
                    "intent": "billing_dispute",
                    "sentiment": "very_negative",
                    "priority": "high",
                    "escalation_needed": True,
                    "emotional_state": "angry",
                    "de_escalation_required": True
                }
            },
            {
                "name": "happy_customer_compliment",
                "message": CustomerMessage(
                    message_id="happy_001",
                    customer_id="cust_happy_001",
                    content="I just wanted to say thank you so much for the excellent service! Your team resolved my issue quickly and professionally. This is why I love being your customer!",
                    channel="email",
                    metadata={"satisfaction_survey": "5_stars"}
                ),
                "expected_outcomes": {
                    "intent": "compliment",
                    "sentiment": "very_positive",
                    "priority": "low",
                    "escalation_needed": False,
                    "emotional_state": "happy",
                    "follow_up_action": "thank_customer"
                }
            },
            {
                "name": "complex_technical_issue",
                "message": CustomerMessage(
                    message_id="tech_001",
                    customer_id="cust_tech_001",
                    content="My API integration is failing with error code 500 when trying to authenticate. I've checked the documentation and my credentials are correct. This is blocking our production deployment.",
                    channel="api",
                    metadata={"api_version": "v2", "error_code": "500", "business_impact": "high"}
                ),
                "expected_outcomes": {
                    "intent": "technical_support",
                    "sentiment": "neutral",
                    "priority": "high",
                    "escalation_needed": True,
                    "requires_technical_expertise": True,
                    "business_impact": "high"
                }
            }
        ]
    
    @pytest.mark.asyncio
    async def test_complete_workflow_angry_customer(self, support_orchestrator, complex_customer_scenarios, mock_all_services):
        """Test complete workflow for angry customer scenario."""
        scenario = complex_customer_scenarios[0]  # angry_customer_billing_dispute
        message = scenario["message"]
        expected = scenario["expected_outcomes"]
        
        # Mock AI responses for each agent
        mock_responses = {
            "intake": json.dumps({
                "intent": expected["intent"],
                "sentiment": expected["sentiment"],
                "urgency_score": 0.9,
                "confidence_score": 0.85,
                "entities": {"billing_amount": "$299.99", "service": "premium_plan"},
                "escalation_indicators": ["legal_threat", "multiple_contacts"],
                "key_phrases": ["furious", "useless", "legal action"],
                "summary": "Angry customer with billing dispute threatening legal action"
            }),
            "emotional": json.dumps({
                "emotional_state": expected["emotional_state"],
                "intensity": 0.9,
                "empathy_level": 0.8,
                "de_escalation_strategy": "immediate_supervisor_escalation",
                "emotional_trajectory": ["frustrated", "angry", "furious"]
            }),
            "knowledge": json.dumps({
                "relevant_policies": ["billing_dispute_resolution", "refund_policy"],
                "similar_cases": 3,
                "resolution_success_rate": 0.85,
                "recommended_actions": ["immediate_refund", "account_credit", "supervisor_call"]
            }),
            "resolution": json.dumps({
                "recommended_response": "I sincerely apologize for this billing error and the frustration it has caused. I'm immediately processing a full refund and escalating to my supervisor for a personal follow-up call.",
                "action_items": ["process_refund", "escalate_to_supervisor", "schedule_callback"],
                "compensation_offered": "$50_account_credit",
                "resolution_confidence": 0.9
            })
        }
        
        # Mock the ADK orchestrator's process method
        with patch.object(support_orchestrator.adk_orchestrator, 'process_customer_message') as mock_process:
            mock_process.return_value = {
                "workflow_id": "workflow_angry_001",
                "processing_time": 3.2,
                "agents_involved": ["intake", "emotional", "knowledge", "resolution", "quality"],
                "final_response": mock_responses["resolution"],
                "escalation_triggered": True,
                "customer_satisfaction_prediction": 0.7,
                "brain_state_updates": {
                    "emotional_agent": {"stress_level": 0.4, "empathy_sensitivity": 0.9},
                    "resolution_agent": {"confidence_level": 0.8, "learning_mode": True}
                }
            }
            
            # Process the message
            result = await support_orchestrator.process_message(message)
            
            # Verify workflow execution
            assert result is not None
            mock_process.assert_called_once_with(message)
            
            # Verify expected outcomes
            workflow_result = mock_process.return_value
            assert workflow_result["escalation_triggered"] == expected["escalation_needed"]
            assert len(workflow_result["agents_involved"]) >= 4  # Multiple agents involved
            assert workflow_result["processing_time"] > 0
    
    @pytest.mark.asyncio
    async def test_swarm_intelligence_activation(self, support_orchestrator, complex_customer_scenarios, mock_all_services):
        """Test swarm intelligence activation for complex scenarios."""
        scenario = complex_customer_scenarios[2]  # complex_technical_issue
        message = scenario["message"]
        
        # Mock swarm intelligence activation
        with patch.object(support_orchestrator.adk_orchestrator, 'process_customer_message') as mock_process:
            mock_process.return_value = {
                "workflow_id": "swarm_tech_001",
                "swarm_activated": True,
                "primary_agent": "technical_support_agent",
                "spawned_agents": ["api_specialist_agent", "documentation_agent", "escalation_agent"],
                "swarm_coordination": "hierarchical_consensus",
                "collective_analysis": {
                    "technical_complexity": 0.9,
                    "business_impact": 0.8,
                    "resolution_confidence": 0.85
                },
                "swarm_decision": {
                    "immediate_actions": ["api_diagnostics", "credential_verification", "error_log_analysis"],
                    "escalation_path": "senior_technical_team",
                    "estimated_resolution_time": "2_hours"
                },
                "brain_state_updates": {
                    "swarm_intelligence_network": {
                        "network_id": "swarm_net_tech_001",
                        "efficiency": 0.89,
                        "consensus_reached": True
                    }
                }
            }
            
            # Process complex technical message
            result = await support_orchestrator.process_message(message)
            
            # Verify swarm activation
            assert result is not None
            mock_process.assert_called_once_with(message)
            
            swarm_result = mock_process.return_value
            assert swarm_result["swarm_activated"] == True
            assert len(swarm_result["spawned_agents"]) >= 2
            assert "collective_analysis" in swarm_result
            assert "swarm_decision" in swarm_result
    
    @pytest.mark.asyncio
    async def test_emotional_contagion_management(self, support_orchestrator, complex_customer_scenarios, mock_all_services):
        """Test emotional contagion management across multiple angry customers."""
        angry_scenario = complex_customer_scenarios[0]
        
        # Simulate multiple angry customers in sequence
        angry_messages = []
        for i in range(5):
            angry_messages.append(CustomerMessage(
                message_id=f"angry_seq_{i}",
                customer_id=f"cust_angry_seq_{i}",
                content=f"I'm extremely angry about issue #{i}! This is unacceptable!",
                channel="chat",
                metadata={"sequence": i, "emotional_intensity": 0.8 + i * 0.05}
            ))
        
        # Mock emotional contagion tracking
        with patch.object(support_orchestrator.adk_orchestrator, 'process_customer_message') as mock_process:
            emotional_states = []
            
            for i, message in enumerate(angry_messages):
                # Simulate increasing emotional contagion resistance
                mock_process.return_value = {
                    "workflow_id": f"emotional_seq_{i}",
                    "emotional_analysis": {
                        "customer_emotion": "angry",
                        "agent_emotional_state": "stable_empathy",
                        "emotional_contagion_detected": i > 2,  # After 3rd angry customer
                        "contagion_resistance_applied": i > 2,
                        "agent_stress_level": min(0.8, 0.2 + i * 0.1)
                    },
                    "brain_state_updates": {
                        "emotional_agent": {
                            "emotional_contagion_resistance": min(1.0, 0.5 + i * 0.1),
                            "stress_level": min(0.8, 0.2 + i * 0.1),
                            "empathy_sensitivity": max(0.3, 0.8 - i * 0.05)  # Slight decrease to protect agent
                        }
                    }
                }
                
                result = await support_orchestrator.process_message(message)
                emotional_states.append(mock_process.return_value["emotional_analysis"])
            
            # Verify emotional contagion management
            assert len(emotional_states) == 5
            
            # Check that contagion resistance increased over time
            later_states = emotional_states[3:]  # Last 2 interactions
            for state in later_states:
                assert state["emotional_contagion_detected"] == True
                assert state["contagion_resistance_applied"] == True
    
    @pytest.mark.asyncio
    async def test_collective_intelligence_consensus(self, support_orchestrator, mock_all_services):
        """Test collective intelligence consensus for difficult decisions."""
        difficult_message = CustomerMessage(
            message_id="difficult_001",
            customer_id="cust_difficult_001",
            content="I want a full refund for a service I used for 11 months out of a 12-month contract, but I'm claiming it never worked properly despite no previous complaints.",
            channel="email",
            metadata={"contract_length": 12, "usage_months": 11, "previous_complaints": 0}
        )
        
        # Mock collective intelligence session
        with patch.object(support_orchestrator.adk_orchestrator, 'process_customer_message') as mock_process:
            mock_process.return_value = {
                "workflow_id": "collective_difficult_001",
                "collective_intelligence_activated": True,
                "participating_agents": [
                    "policy_agent", "customer_service_agent", "legal_agent", "financial_agent"
                ],
                "session_leader": "policy_agent",
                "collective_analysis": {
                    "policy_perspective": {"refund_eligibility": 0.2, "confidence": 0.9},
                    "customer_service_perspective": {"customer_satisfaction_risk": 0.7, "confidence": 0.8},
                    "legal_perspective": {"liability_risk": 0.3, "confidence": 0.85},
                    "financial_perspective": {"cost_impact": 0.6, "confidence": 0.9}
                },
                "consensus_building": {
                    "initial_disagreement": True,
                    "consensus_rounds": 3,
                    "final_consensus_score": 0.75,
                    "agreed_decision": "partial_refund_with_conditions"
                },
                "collective_decision": {
                    "action": "offer_25_percent_refund",
                    "conditions": ["acknowledge_service_usage", "future_loyalty_program"],
                    "escalation_if_rejected": True,
                    "confidence_level": 0.82
                },
                "brain_state_updates": {
                    "collective_intelligence_session": {
                        "session_id": "ci_session_difficult_001",
                        "consensus_reached": True,
                        "collective_intelligence_score": 0.82
                    }
                }
            }
            
            # Process difficult decision message
            result = await support_orchestrator.process_message(difficult_message)
            
            # Verify collective intelligence
            assert result is not None
            mock_process.assert_called_once_with(difficult_message)
            
            ci_result = mock_process.return_value
            assert ci_result["collective_intelligence_activated"] == True
            assert len(ci_result["participating_agents"]) >= 3
            assert ci_result["consensus_building"]["final_consensus_score"] > 0.7
            assert "collective_decision" in ci_result
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self, support_orchestrator, mock_all_services):
        """Test system performance under high message load."""
        # Create multiple concurrent messages
        concurrent_messages = []
        for i in range(10):
            concurrent_messages.append(CustomerMessage(
                message_id=f"load_test_{i}",
                customer_id=f"cust_load_{i}",
                content=f"Load test message {i} with varying complexity",
                channel="api",
                metadata={"load_test": True, "message_index": i}
            ))
        
        # Mock concurrent processing
        with patch.object(support_orchestrator.adk_orchestrator, 'process_customer_message') as mock_process:
            mock_process.return_value = {
                "workflow_id": "load_test",
                "processing_time": 1.5,
                "agents_involved": ["intake", "knowledge", "resolution"],
                "success": True,
                "brain_state_stable": True
            }
            
            # Process messages concurrently
            start_time = datetime.utcnow()
            tasks = [support_orchestrator.process_message(msg) for msg in concurrent_messages]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = datetime.utcnow()
            
            processing_time = (end_time - start_time).total_seconds()
            
            # Verify performance
            assert len(results) == 10
            assert all(result is not None for result in results if not isinstance(result, Exception))
            assert processing_time < 30.0  # Should handle 10 messages in under 30 seconds
            assert mock_process.call_count == 10
