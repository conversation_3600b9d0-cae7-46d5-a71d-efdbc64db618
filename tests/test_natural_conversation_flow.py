"""
Tests for Natural Conversation Flow

This module tests the natural conversation flow functionality including
casual conversation, smooth transitions to business topics, and context management.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from src.services.natural_conversation_flow import (
    NaturalConversationFlow, 
    ConversationPhase, 
    BusinessIntent, 
    ConversationContext
)
from src.services.conversation_phase_detector import ConversationPhaseDetector, MessageType
from src.services.enhanced_response_generator import EnhancedResponseGenerator


class TestNaturalConversationFlow:
    """Test cases for natural conversation flow."""
    
    @pytest.fixture
    def natural_flow(self):
        """Create natural conversation flow instance."""
        return NaturalConversationFlow()
    
    @pytest.fixture
    def sample_customer_info(self):
        """Sample customer information."""
        return {
            "customer_id": "CUST_001",
            "first_name": "<PERSON>",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "outstanding_balance": 0.0
        }
    
    @pytest.fixture
    def conversation_context(self):
        """Sample conversation context."""
        return ConversationContext(
            customer_id="CUST_001",
            conversation_id="conv_test_001",
            current_phase=ConversationPhase.GREETING,
            turns_in_phase=0,
            total_turns=0,
            detected_business_intent=None,
            customer_mood="neutral",
            transition_readiness=0.0,
            pending_business_tasks=[],
            last_business_mention=None
        )
    
    @pytest.mark.asyncio
    async def test_initialize_conversation(self, natural_flow):
        """Test conversation initialization."""
        with patch('src.services.natural_conversation_flow.customer_db') as mock_db:
            mock_db.get_customer_info.return_value = {
                "customer_id": "CUST_001",
                "first_name": "John"
            }
            
            context = await natural_flow.initialize_conversation(
                customer_id="CUST_001",
                conversation_id="conv_test_001"
            )
            
            assert context.customer_id == "CUST_001"
            assert context.conversation_id == "conv_test_001"
            assert context.current_phase == ConversationPhase.GREETING
            assert context.total_turns == 0
    
    @pytest.mark.asyncio
    async def test_greeting_response(self, natural_flow, conversation_context, sample_customer_info):
        """Test greeting response generation."""
        response, updated_context = await natural_flow.generate_response(
            context=conversation_context,
            customer_message="Hello!",
            customer_info=sample_customer_info
        )
        
        assert "Hello John" in response or "Hi" in response
        assert updated_context.current_phase == ConversationPhase.CASUAL_CHAT
        assert updated_context.total_turns == 1
    
    @pytest.mark.asyncio
    async def test_casual_conversation_flow(self, natural_flow, sample_customer_info):
        """Test casual conversation flow."""
        # Start with greeting
        context = ConversationContext(
            customer_id="CUST_001",
            conversation_id="conv_test_001",
            current_phase=ConversationPhase.CASUAL_CHAT,
            turns_in_phase=0,
            total_turns=1,
            detected_business_intent=None,
            customer_mood="positive",
            transition_readiness=0.3,
            pending_business_tasks=[],
            last_business_mention=None
        )
        
        # Customer responds positively
        response, updated_context = await natural_flow.generate_response(
            context=context,
            customer_message="I'm doing great, thanks! How are you?",
            customer_info=sample_customer_info
        )
        
        assert "wonderful" in response.lower() or "great" in response.lower()
        assert updated_context.customer_mood == "positive"
        assert updated_context.transition_readiness > 0.3
    
    @pytest.mark.asyncio
    async def test_business_intent_detection(self, natural_flow, sample_customer_info):
        """Test business intent detection and immediate transition."""
        context = ConversationContext(
            customer_id="CUST_001",
            conversation_id="conv_test_001",
            current_phase=ConversationPhase.CASUAL_CHAT,
            turns_in_phase=1,
            total_turns=2,
            detected_business_intent=None,
            customer_mood="neutral",
            transition_readiness=0.5,
            pending_business_tasks=[],
            last_business_mention=None
        )
        
        # Customer mentions business topic
        response, updated_context = await natural_flow.generate_response(
            context=context,
            customer_message="Actually, I wanted to check on my recent order",
            customer_info=sample_customer_info
        )
        
        assert updated_context.current_phase == ConversationPhase.BUSINESS_FOCUS
        assert updated_context.detected_business_intent == BusinessIntent.PARCEL_TRACKING
    
    @pytest.mark.asyncio
    async def test_smooth_transition_to_business(self, natural_flow, sample_customer_info):
        """Test smooth transition from casual to business conversation."""
        context = ConversationContext(
            customer_id="CUST_001",
            conversation_id="conv_test_001",
            current_phase=ConversationPhase.CASUAL_CHAT,
            turns_in_phase=2,
            total_turns=3,
            detected_business_intent=None,
            customer_mood="positive",
            transition_readiness=0.8,
            pending_business_tasks=["outstanding_payment"],
            last_business_mention=None
        )
        
        response, updated_context = await natural_flow.generate_response(
            context=context,
            customer_message="That sounds good!",
            customer_info=sample_customer_info
        )
        
        # Should transition to business focus
        assert updated_context.current_phase == ConversationPhase.TRANSITION
        assert "by the way" in response.lower() or "speaking of" in response.lower()


class TestConversationPhaseDetector:
    """Test cases for conversation phase detector."""
    
    @pytest.fixture
    def phase_detector(self):
        """Create phase detector instance."""
        return ConversationPhaseDetector()
    
    def test_message_classification(self, phase_detector):
        """Test message classification."""
        # Test greeting
        classifications = phase_detector.classify_message("Hello, how are you?")
        assert MessageType.GREETING in classifications
        
        # Test business inquiry
        classifications = phase_detector.classify_message("I need to track my package")
        assert MessageType.BUSINESS_INQUIRY in classifications
        
        # Test complaint
        classifications = phase_detector.classify_message("I'm frustrated with this service")
        assert MessageType.COMPLAINT in classifications
    
    def test_business_intent_detection(self, phase_detector):
        """Test business intent detection."""
        # Test parcel tracking
        intent = phase_detector.detect_business_intent("Where is my package?")
        assert intent == BusinessIntent.PARCEL_TRACKING
        
        # Test payment inquiry
        intent = phase_detector.detect_business_intent("I have a question about my bill")
        assert intent == BusinessIntent.PAYMENT_REMINDER
        
        # Test order management
        intent = phase_detector.detect_business_intent("I want to cancel my order")
        assert intent == BusinessIntent.ORDER_MANAGEMENT
    
    def test_sentiment_analysis(self, phase_detector):
        """Test sentiment analysis."""
        # Test positive sentiment
        sentiment = phase_detector.analyze_sentiment("I'm very happy with the service!")
        assert sentiment == "positive"
        
        # Test negative sentiment
        sentiment = phase_detector.analyze_sentiment("This is terrible and frustrating")
        assert sentiment == "negative"
        
        # Test neutral sentiment
        sentiment = phase_detector.analyze_sentiment("I have a question about my account")
        assert sentiment == "neutral"
    
    def test_transition_readiness_calculation(self, phase_detector):
        """Test transition readiness calculation."""
        context = ConversationContext(
            customer_id="CUST_001",
            conversation_id="conv_test_001",
            current_phase=ConversationPhase.CASUAL_CHAT,
            turns_in_phase=2,
            total_turns=3,
            detected_business_intent=BusinessIntent.PARCEL_TRACKING,
            customer_mood="positive",
            transition_readiness=0.0,
            pending_business_tasks=["outstanding_payment"],
            last_business_mention=None
        )
        
        readiness = phase_detector.calculate_transition_readiness(
            context, "That sounds great!", [MessageType.CASUAL_RESPONSE]
        )
        
        # Should be high due to business intent and pending tasks
        assert readiness > 0.7


class TestEnhancedResponseGenerator:
    """Test cases for enhanced response generator."""
    
    @pytest.fixture
    def response_generator(self):
        """Create enhanced response generator instance."""
        return EnhancedResponseGenerator()
    
    @pytest.mark.asyncio
    async def test_natural_response_generation(self, response_generator):
        """Test natural response generation."""
        with patch.multiple(
            'src.services.enhanced_response_generator',
            customer_db=AsyncMock()
        ):
            customer_info = {"customer_id": "CUST_001", "first_name": "John"}
            
            response, metadata = await response_generator.generate_natural_response(
                customer_message="Hello, how are you?",
                customer_info=customer_info,
                conversation_id="conv_test_001"
            )
            
            assert isinstance(response, str)
            assert len(response) > 0
            assert isinstance(metadata, dict)
            assert "conversation_phase" in metadata
            assert "turn_analysis" in metadata
    
    @pytest.mark.asyncio
    async def test_business_focused_response(self, response_generator):
        """Test business-focused response generation."""
        with patch.multiple(
            'src.services.enhanced_response_generator',
            customer_db=AsyncMock()
        ):
            customer_info = {"customer_id": "CUST_001", "first_name": "John"}
            
            response, metadata = await response_generator.generate_natural_response(
                customer_message="I need to track my package",
                customer_info=customer_info,
                conversation_id="conv_test_001"
            )
            
            assert "track" in response.lower() or "package" in response.lower()
            assert metadata["business_intent_detected"] == "parcel_tracking"
    
    @pytest.mark.asyncio
    async def test_empathetic_response_for_complaint(self, response_generator):
        """Test empathetic response for complaints."""
        with patch.multiple(
            'src.services.enhanced_response_generator',
            customer_db=AsyncMock()
        ):
            customer_info = {"customer_id": "CUST_001", "first_name": "John"}
            
            response, metadata = await response_generator.generate_natural_response(
                customer_message="I'm really frustrated with this service!",
                customer_info=customer_info,
                conversation_id="conv_test_001"
            )
            
            # Should contain empathetic language
            empathetic_words = ["sorry", "understand", "apologize", "frustrating"]
            assert any(word in response.lower() for word in empathetic_words)
            assert metadata["turn_analysis"]["sentiment"] == "negative"


class TestIntegrationScenarios:
    """Integration test scenarios for natural conversation flow."""
    
    @pytest.mark.asyncio
    async def test_complete_conversation_flow(self):
        """Test complete conversation flow from greeting to business resolution."""
        natural_flow = NaturalConversationFlow()
        customer_info = {"customer_id": "CUST_001", "first_name": "Sarah"}
        
        with patch('src.services.natural_conversation_flow.customer_db') as mock_db:
            mock_db.get_customer_info.return_value = customer_info
            
            # Initialize conversation
            context = await natural_flow.initialize_conversation(
                customer_id="CUST_001",
                conversation_id="conv_integration_001"
            )
            
            # Step 1: Greeting
            response1, context = await natural_flow.generate_response(
                context=context,
                customer_message="Hi there!",
                customer_info=customer_info
            )
            assert context.current_phase == ConversationPhase.CASUAL_CHAT
            
            # Step 2: Casual conversation
            response2, context = await natural_flow.generate_response(
                context=context,
                customer_message="I'm doing well, thanks for asking!",
                customer_info=customer_info
            )
            assert context.current_phase == ConversationPhase.CASUAL_CHAT
            
            # Step 3: Business transition (should happen naturally)
            response3, context = await natural_flow.generate_response(
                context=context,
                customer_message="That's great to hear!",
                customer_info=customer_info
            )
            # Should transition to business or stay in casual depending on readiness
            assert context.current_phase in [ConversationPhase.CASUAL_CHAT, ConversationPhase.TRANSITION]
            
            # Step 4: Direct business inquiry
            response4, context = await natural_flow.generate_response(
                context=context,
                customer_message="Actually, I wanted to check on my recent order",
                customer_info=customer_info
            )
            assert context.current_phase == ConversationPhase.BUSINESS_FOCUS
            assert context.detected_business_intent == BusinessIntent.PARCEL_TRACKING
    
    @pytest.mark.asyncio
    async def test_angry_customer_scenario(self):
        """Test handling of angry customer with natural flow."""
        response_generator = EnhancedResponseGenerator()
        customer_info = {"customer_id": "CUST_002", "first_name": "Mike"}
        
        with patch.multiple(
            'src.services.enhanced_response_generator',
            customer_db=AsyncMock()
        ):
            response, metadata = await response_generator.generate_natural_response(
                customer_message="I'm absolutely furious! My order is late and nobody is helping me!",
                customer_info=customer_info,
                conversation_id="conv_angry_001"
            )
            
            # Should detect negative sentiment and provide empathetic response
            assert metadata["turn_analysis"]["sentiment"] == "negative"
            assert metadata["empathy_applied"] > 0.8
            
            # Response should contain empathetic language
            empathetic_indicators = ["sorry", "understand", "apologize", "frustrating", "help"]
            assert any(indicator in response.lower() for indicator in empathetic_indicators)


if __name__ == "__main__":
    pytest.main([__file__])
