"""Comprehensive unit tests for customer support agents and brain system."""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List

from src.agents import IntakeAgent, KnowledgeAgent, ResolutionAgent, QualityAgent
from src.models import CustomerMessage, AgentContext
from src.config import Priority, Sentiment
from src.adk.innovative_agents import (
    EmotionalIntelligenceAgent, PredictiveAnalyticsAgent,
    MultiModalAgent, CollaborativeAgent, AdaptiveLearningAgent,
    EmotionalState
)
from src.adk.agent_framework import ADKAgent, ADKMessage, ADKAgentConfig, AgentType, AgentCapability
from src.adk.adk_orchestrator import ADKSmartSupportOrchestrator


@pytest.fixture
def sample_customer_message():
    """Sample customer message for testing."""
    return CustomerMessage(
        message_id="test_msg_001",
        customer_id="test_customer",
        content="My order #12345 arrived damaged and I need a refund!",
        channel="test"
    )


@pytest.fixture
def sample_context(sample_customer_message):
    """Sample agent context for testing."""
    return AgentContext(
        message_id=sample_customer_message.message_id,
        customer_message=sample_customer_message
    )


class TestIntakeAgent:
    """Test cases for the Intake Agent."""
    
    @pytest.fixture
    def intake_agent(self, mock_vertex_ai, mock_google_cloud_services):
        """Create intake agent for testing."""
        return IntakeAgent()
    
    @pytest.mark.asyncio
    async def test_process_angry_customer(self, intake_agent, sample_context):
        """Test processing an angry customer message."""
        # Mock the AI response
        mock_response = """
        {
            "intent": "refund_request",
            "sentiment": "very_negative",
            "urgency_score": 0.9,
            "confidence_score": 0.85,
            "entities": {"order_id": "12345"},
            "escalation_indicators": ["sentiment_very_negative"],
            "key_phrases": ["damaged", "refund"],
            "summary": "Customer received damaged order and wants refund"
        }
        """
        
        intake_agent._generate_response = AsyncMock(return_value=mock_response)
        
        # Process the context
        result_context = await intake_agent.process(sample_context)
        
        # Verify results
        assert result_context.intake_result is not None
        assert result_context.intake_result.intent == "refund_request"
        assert result_context.intake_result.sentiment == Sentiment.VERY_NEGATIVE
        assert result_context.intake_result.priority == Priority.HIGH
        assert result_context.intake_result.urgency_score == 0.9
        assert "sentiment_very_negative" in result_context.intake_result.escalation_flags
    
    @pytest.mark.asyncio
    async def test_process_positive_feedback(self, intake_agent):
        """Test processing positive customer feedback."""
        positive_message = CustomerMessage(
            message_id="test_positive",
            customer_id="happy_customer",
            content="Thank you for the excellent service! The product is amazing!",
            channel="test"
        )
        
        context = AgentContext(
            message_id=positive_message.message_id,
            customer_message=positive_message
        )
        
        mock_response = """
        {
            "intent": "compliment",
            "sentiment": "very_positive",
            "urgency_score": 0.1,
            "confidence_score": 0.9,
            "entities": {},
            "escalation_indicators": [],
            "key_phrases": ["excellent service", "amazing"],
            "summary": "Customer expressing satisfaction with product and service"
        }
        """
        
        intake_agent._generate_response = AsyncMock(return_value=mock_response)
        
        result_context = await intake_agent.process(context)
        
        assert result_context.intake_result.intent == "compliment"
        assert result_context.intake_result.sentiment == Sentiment.VERY_POSITIVE
        assert result_context.intake_result.priority == Priority.LOW
        assert len(result_context.intake_result.escalation_flags) == 0


class TestKnowledgeAgent:
    """Test cases for the Knowledge Agent."""
    
    @pytest.fixture
    def knowledge_agent(self, mock_vertex_ai, mock_google_cloud_services):
        """Create knowledge agent for testing."""
        return KnowledgeAgent()
    
    @pytest.mark.asyncio
    async def test_generate_search_queries(self, knowledge_agent, sample_context):
        """Test search query generation."""
        # Add intake result to context
        from src.models import IntakeResult
        sample_context.intake_result = IntakeResult(
            message_id=sample_context.message_id,
            intent="refund_request",
            sentiment=Sentiment.NEGATIVE,
            priority=Priority.HIGH,
            urgency_score=0.8,
            extracted_entities={"order_id": "12345"},
            escalation_flags=[],
            confidence_score=0.85,
            processing_time=1.0
        )
        
        mock_queries = '["refund policy", "damaged product return", "order 12345", "customer compensation"]'
        knowledge_agent._generate_response = AsyncMock(return_value=mock_queries)
        
        queries = await knowledge_agent._generate_search_queries(sample_context)
        
        assert isinstance(queries, list)
        assert len(queries) > 0
        assert any("refund" in query.lower() for query in queries)


class TestResolutionAgent:
    """Test cases for the Resolution Agent."""
    
    @pytest.fixture
    def resolution_agent(self):
        """Create resolution agent for testing."""
        with patch('src.agents.base_agent.aiplatform.init'):
            with patch('src.agents.base_agent.GenerativeModel'):
                return ResolutionAgent()
    
    @pytest.mark.asyncio
    async def test_generate_response_draft(self, resolution_agent, sample_context):
        """Test response draft generation."""
        # Add required results to context
        from src.models import IntakeResult, KnowledgeResult
        
        sample_context.intake_result = IntakeResult(
            message_id=sample_context.message_id,
            intent="refund_request",
            sentiment=Sentiment.NEGATIVE,
            priority=Priority.HIGH,
            urgency_score=0.8,
            extracted_entities={"order_id": "12345"},
            escalation_flags=[],
            confidence_score=0.85,
            processing_time=1.0
        )
        
        sample_context.knowledge_result = KnowledgeResult(
            message_id=sample_context.message_id,
            relevant_documents=[{
                "title": "Refund Policy",
                "content": "We offer full refunds within 30 days...",
                "relevance_score": 0.9
            }],
            faq_matches=[],
            similar_tickets=[],
            search_queries_used=["refund policy"],
            total_results_found=1,
            relevance_scores=[0.9],
            processing_time=1.5
        )
        
        mock_response = "I sincerely apologize for the damaged item in your order #12345. I'll process a full refund immediately and arrange for a replacement at no additional cost."
        
        resolution_agent._generate_response = AsyncMock(return_value=mock_response)
        
        result_context = await resolution_agent.process(sample_context)
        
        assert result_context.resolution_result is not None
        assert len(result_context.resolution_result.response_draft) > 0
        assert "apologize" in result_context.resolution_result.response_draft.lower()
        assert "12345" in result_context.resolution_result.response_draft


class TestQualityAgent:
    """Test cases for the Quality Agent."""
    
    @pytest.fixture
    def quality_agent(self):
        """Create quality agent for testing."""
        with patch('src.agents.base_agent.aiplatform.init'):
            with patch('src.agents.base_agent.GenerativeModel'):
                return QualityAgent()
    
    @pytest.mark.asyncio
    async def test_analyze_response_quality(self, quality_agent, sample_context):
        """Test response quality analysis."""
        # Add required results to context
        from src.models import IntakeResult, KnowledgeResult, ResolutionResult
        
        sample_context.intake_result = IntakeResult(
            message_id=sample_context.message_id,
            intent="refund_request",
            sentiment=Sentiment.NEGATIVE,
            priority=Priority.HIGH,
            urgency_score=0.8,
            extracted_entities={"order_id": "12345"},
            escalation_flags=[],
            confidence_score=0.85,
            processing_time=1.0
        )
        
        sample_context.knowledge_result = KnowledgeResult(
            message_id=sample_context.message_id,
            relevant_documents=[],
            faq_matches=[],
            similar_tickets=[],
            search_queries_used=[],
            total_results_found=0,
            relevance_scores=[],
            processing_time=1.5
        )
        
        sample_context.resolution_result = ResolutionResult(
            message_id=sample_context.message_id,
            response_draft="I apologize for the damaged item. I'll process your refund immediately.",
            action_plan=["Process refund", "Send confirmation email"],
            escalation_recommended=False,
            escalation_reason=None,
            confidence_score=0.8,
            response_type="standard",
            suggested_followup="Follow up in 24 hours",
            processing_time=2.0
        )
        
        # Mock quality analysis response
        mock_quality_response = """
        {
            "accuracy": 0.9,
            "helpfulness": 0.85,
            "politeness": 0.9,
            "clarity": 0.8,
            "completeness": 0.75,
            "empathy": 0.85
        }
        """
        
        # Mock brand voice response
        mock_brand_response = "0.85"
        
        quality_agent._generate_response = AsyncMock(side_effect=[mock_quality_response, mock_brand_response, "[]"])
        
        result_context = await quality_agent.process(sample_context)
        
        assert result_context.quality_result is not None
        assert result_context.quality_result.quality_score > 0.7
        assert result_context.quality_result.brand_voice_score > 0.7
        assert len(result_context.quality_result.final_response) > 0


class TestAgentIntegration:
    """Integration tests for agent workflow."""
    
    @pytest.mark.asyncio
    async def test_full_agent_workflow(self, sample_customer_message, mock_vertex_ai, mock_google_cloud_services):
        """Test complete agent workflow integration."""
        # Use mocked services from fixtures
        intake_agent = IntakeAgent()
        knowledge_agent = KnowledgeAgent()
        resolution_agent = ResolutionAgent()
        quality_agent = QualityAgent()

        # Mock responses for each agent
        intake_agent._generate_response = AsyncMock(return_value='{"intent": "refund_request", "sentiment": "negative", "urgency_score": 0.8, "confidence_score": 0.85, "entities": {}, "escalation_indicators": [], "key_phrases": [], "summary": "test"}')

        knowledge_agent._generate_response = AsyncMock(return_value='["refund policy"]')
        knowledge_agent._search_documents = AsyncMock(return_value=[])
        knowledge_agent._search_faqs = AsyncMock(return_value=[])
        knowledge_agent._search_similar_tickets = AsyncMock(return_value=[])

        resolution_agent._generate_response = AsyncMock(return_value="I apologize for the issue. I'll help you with a refund.")

        quality_agent._generate_response = AsyncMock(side_effect=[
            '{"accuracy": 0.9, "helpfulness": 0.85, "politeness": 0.9, "clarity": 0.8, "completeness": 0.75, "empathy": 0.85}',
            '0.85',
            '[]'
        ])

        # Create initial context
        context = AgentContext(
            message_id=sample_customer_message.message_id,
            customer_message=sample_customer_message
        )

        # Run through agent workflow
        context = await intake_agent.process(context)
        assert context.intake_result is not None

        context = await knowledge_agent.process(context)
        assert context.knowledge_result is not None

        context = await resolution_agent.process(context)
        assert context.resolution_result is not None

        context = await quality_agent.process(context)
        assert context.quality_result is not None

        # Verify final state
        assert len(context.agent_history) == 4
        assert context.quality_result.final_response is not None
        assert len(context.quality_result.final_response) > 0


if __name__ == "__main__":
    pytest.main([__file__])
