"""Pytest configuration and shared fixtures for Smart Customer Support Orchestrator tests."""

import pytest
import asyncio
import json
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List

from src.models import CustomerMessage, AgentContext
from src.config import Priority, Sentiment


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_google_cloud_services():
    """Mock all Google Cloud services for testing."""
    with patch('google.cloud.bigquery.Client') as mock_bq, \
         patch('google.cloud.storage.Client') as mock_storage, \
         patch('google.cloud.logging.Client') as mock_logging, \
         patch('google.cloud.monitoring_v3.MetricServiceClient') as mock_monitoring:
        
        # Mock BigQuery
        mock_bq_instance = MagicMock()
        mock_bq.return_value = mock_bq_instance
        mock_job = MagicMock()
        mock_job.result.return_value = []
        mock_bq_instance.query.return_value = mock_job
        
        # Mock Storage
        mock_storage_instance = MagicMock()
        mock_storage.return_value = mock_storage_instance
        
        # Mock Logging
        mock_logging_instance = MagicMock()
        mock_logging.return_value = mock_logging_instance
        
        # Mock Monitoring
        mock_monitoring_instance = MagicMock()
        mock_monitoring.return_value = mock_monitoring_instance
        
        yield {
            'bigquery': mock_bq_instance,
            'storage': mock_storage_instance,
            'logging': mock_logging_instance,
            'monitoring': mock_monitoring_instance
        }


@pytest.fixture
def mock_vertex_ai():
    """Mock Vertex AI services for testing."""
    # Since the actual modules may not be installed, we'll just return a mock
    mock_model_instance = MagicMock()

    # Mock async content generation
    mock_response = MagicMock()
    mock_response.text = "Mock AI response"
    mock_model_instance.generate_content_async = AsyncMock(return_value=mock_response)

    yield mock_model_instance


@pytest.fixture
def sample_customer_messages():
    """Sample customer messages for testing various scenarios."""
    return {
        "angry_billing": CustomerMessage(
            message_id="angry_001",
            customer_id="cust_angry",
            content="I'm furious about being charged twice! This is unacceptable!",
            channel="phone",
            metadata={"call_duration": 300, "previous_contacts": 2}
        ),
        "happy_compliment": CustomerMessage(
            message_id="happy_001",
            customer_id="cust_happy",
            content="Thank you for the excellent service! You resolved my issue perfectly.",
            channel="email",
            metadata={"satisfaction_rating": 5}
        ),
        "technical_issue": CustomerMessage(
            message_id="tech_001",
            customer_id="cust_tech",
            content="API returning 500 error when authenticating with valid credentials.",
            channel="api",
            metadata={"error_code": "500", "api_version": "v2"}
        ),
        "refund_request": CustomerMessage(
            message_id="refund_001",
            customer_id="cust_refund",
            content="My order #12345 arrived damaged. I need a full refund.",
            channel="web",
            metadata={"order_id": "12345", "damage_type": "packaging"}
        ),
        "account_access": CustomerMessage(
            message_id="access_001",
            customer_id="cust_access",
            content="I can't log into my account. Password reset isn't working.",
            channel="chat",
            metadata={"login_attempts": 5, "reset_attempts": 2}
        )
    }


@pytest.fixture
def sample_agent_contexts(sample_customer_messages):
    """Sample agent contexts for testing workflows."""
    contexts = {}
    
    for key, message in sample_customer_messages.items():
        contexts[key] = AgentContext(
            message_id=message.message_id,
            customer_message=message,
            workflow_start_time=datetime.utcnow()
        )
    
    return contexts


@pytest.fixture
def mock_brain_state_data():
    """Mock brain state data for testing."""
    return {
        "agent_alpha_001": {
            "brain_state_id": "brain_alpha_001",
            "agent_id": "agent_alpha_001",
            "awareness_level": 0.85,
            "focus_intensity": 0.7,
            "multitasking_capacity": 1.2,
            "primary_mode": "problem_solving",
            "cognitive_load": 0.6,
            "stress_level": 0.3,
            "confidence_level": 0.8,
            "working_memory": json.dumps({
                "current_task": "customer_support",
                "active_context": "billing_dispute"
            }),
            "current_emotional_state": "focused_empathy",
            "empathy_sensitivity": 0.8,
            "learning_mode": True,
            "team_awareness_level": 0.9
        },
        "agent_beta_002": {
            "brain_state_id": "brain_beta_002",
            "agent_id": "agent_beta_002",
            "awareness_level": 0.78,
            "focus_intensity": 0.8,
            "multitasking_capacity": 1.0,
            "primary_mode": "empathizing",
            "cognitive_load": 0.5,
            "stress_level": 0.2,
            "confidence_level": 0.9,
            "working_memory": json.dumps({
                "current_task": "emotional_support",
                "active_context": "customer_frustration"
            }),
            "current_emotional_state": "calm_empathy",
            "empathy_sensitivity": 0.9,
            "learning_mode": True,
            "team_awareness_level": 0.8
        }
    }


@pytest.fixture
def mock_ai_responses():
    """Mock AI responses for different agent types."""
    return {
        "intake_analysis": json.dumps({
            "intent": "billing_dispute",
            "sentiment": "negative",
            "urgency_score": 0.8,
            "confidence_score": 0.85,
            "entities": {"amount": "$299.99", "service": "premium_plan"},
            "escalation_indicators": ["angry_tone", "billing_error"],
            "key_phrases": ["charged twice", "unacceptable"],
            "summary": "Customer angry about duplicate billing charge"
        }),
        "emotional_analysis": json.dumps({
            "emotional_state": "angry",
            "intensity": 0.8,
            "empathy_level": 0.7,
            "de_escalation_strategy": "immediate_acknowledgment",
            "emotional_trajectory": ["frustrated", "angry"]
        }),
        "knowledge_search": json.dumps({
            "relevant_documents": ["billing_policy.pdf", "refund_procedures.pdf"],
            "faq_matches": ["How to dispute a charge", "Refund processing time"],
            "similar_tickets": 3,
            "confidence_score": 0.9
        }),
        "resolution_response": json.dumps({
            "recommended_response": "I sincerely apologize for the billing error. I'm processing an immediate refund.",
            "action_items": ["process_refund", "send_confirmation", "follow_up_call"],
            "escalation_needed": False,
            "confidence_score": 0.88
        })
    }


@pytest.fixture
def mock_swarm_intelligence_data():
    """Mock swarm intelligence data for testing."""
    return {
        "network_id": "swarm_net_001",
        "primary_agent_id": "agent_alpha_001",
        "spawned_agents": ["agent_alpha_001_sub1", "agent_alpha_001_sub2"],
        "swarm_size": 3,
        "task_type": "complex_technical_issue",
        "task_complexity": "very_high",
        "load_distribution": {
            "agent_alpha_001": 0.4,
            "agent_alpha_001_sub1": 0.3,
            "agent_alpha_001_sub2": 0.3
        },
        "swarm_coordination_method": "hierarchical_consensus",
        "swarm_efficiency": 0.89,
        "consensus_reached": True,
        "collective_decision": {
            "action": "escalate_to_technical_team",
            "confidence": 0.92,
            "estimated_resolution_time": "2_hours"
        }
    }


@pytest.fixture
def mock_collective_intelligence_session():
    """Mock collective intelligence session data."""
    return {
        "session_id": "ci_session_001",
        "participating_agents": ["policy_agent", "customer_service_agent", "legal_agent"],
        "session_leader_agent": "policy_agent",
        "session_type": "problem_solving",
        "shared_context": {
            "problem_complexity": "high",
            "customer_impact": "medium",
            "policy_implications": "significant"
        },
        "distributed_analysis": {
            "policy_agent": "policy_compliance_check",
            "customer_service_agent": "customer_satisfaction_impact",
            "legal_agent": "legal_risk_assessment"
        },
        "knowledge_pooling": {
            "policy_expertise": 0.9,
            "customer_service_expertise": 0.85,
            "legal_expertise": 0.8
        },
        "collective_decision": {
            "recommended_action": "approve_with_conditions",
            "confidence_level": 0.87,
            "consensus_strength": 0.82
        },
        "session_outcome": "successful_consensus",
        "collective_intelligence_score": 0.85
    }


@pytest.fixture
def performance_test_config():
    """Configuration for performance testing."""
    return {
        "max_processing_time": 5.0,  # seconds
        "max_concurrent_messages": 50,
        "brain_update_timeout": 1.0,  # seconds
        "memory_consolidation_timeout": 2.0,  # seconds
        "swarm_activation_timeout": 10.0,  # seconds
        "collective_intelligence_timeout": 15.0,  # seconds
        "acceptable_failure_rate": 0.05,  # 5%
        "performance_consistency_threshold": 0.3  # coefficient of variation
    }


@pytest.fixture(autouse=True)
def setup_test_logging():
    """Setup logging for tests."""
    with patch('src.adk.adk_orchestrator.structlog.get_logger') as mock_logger, \
         patch('src.orchestrator.orchestrator.structlog.get_logger') as mock_logger2, \
         patch('src.agents.base_agent.structlog.get_logger') as mock_logger3:
        
        # Create mock logger instances
        mock_logger_instance = MagicMock()
        mock_logger.return_value = mock_logger_instance
        mock_logger2.return_value = mock_logger_instance
        mock_logger3.return_value = mock_logger_instance
        
        # Mock logger methods
        mock_logger_instance.bind.return_value = mock_logger_instance
        mock_logger_instance.info = MagicMock()
        mock_logger_instance.error = MagicMock()
        mock_logger_instance.warning = MagicMock()
        mock_logger_instance.debug = MagicMock()
        
        yield mock_logger_instance


# Pytest markers for test categorization
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "brain_system: Brain system tests")
    config.addinivalue_line("markers", "adk_compliance: ADK compliance tests")
    config.addinivalue_line("markers", "slow: Slow running tests")
    config.addinivalue_line("markers", "bigquery: Tests requiring BigQuery")
    config.addinivalue_line("markers", "vertex_ai: Tests requiring Vertex AI")


# Test collection hooks
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names."""
    for item in items:
        # Add markers based on test file names
        if "test_performance" in item.nodeid:
            item.add_marker(pytest.mark.performance)
            item.add_marker(pytest.mark.slow)
        if "test_integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        if "bigquery" in item.nodeid:
            item.add_marker(pytest.mark.bigquery)
        if "vertex" in item.nodeid or "ai" in item.nodeid:
            item.add_marker(pytest.mark.vertex_ai)
