"""
Tests for the customer support improvements:
- Sentiment analysis and escalation detection
- Emotional tone adaptation
- Order history summarization
- Action links and quick buttons
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from src.services.sentiment_analyzer import (
    SentimentAnalyzer, SentimentAnalysis, EmotionalState, EscalationLevel
)
from src.services.empathetic_response_generator import EmpatheticResponseGenerator
from src.services.order_history_summarizer import Order<PERSON><PERSON>orySummarizer, OrderSummary
from src.services.action_link_generator import ActionLinkGenerator, ActionType


class TestSentimentAnalyzer:
    """Test sentiment analysis and escalation detection."""
    
    @pytest.fixture
    def analyzer(self):
        return SentimentAnalyzer()
    
    @pytest.mark.asyncio
    async def test_angry_customer_detection(self, analyzer):
        """Test detection of angry customers."""
        message = "I'm furious about this terrible service! This is absolutely unacceptable!"
        
        result = await analyzer.analyze_sentiment(message)
        
        assert result.primary_emotion == EmotionalState.ANGRY
        assert result.intensity > 0.5
        assert result.escalation_level in [EscalationLevel.ESCALATE, EscalationLevel.MONITOR]
        assert "angry" in result.tone_adaptation
    
    @pytest.mark.asyncio
    async def test_threatening_customer_escalation(self, analyzer):
        """Test escalation for threatening customers."""
        message = "I'm going to sue you idiots and report this to the BBB!"
        
        result = await analyzer.analyze_sentiment(message)
        
        assert result.escalation_level == EscalationLevel.URGENT_ESCALATE
        assert result.escalation_reason is not None
        assert len(result.threat_indicators) > 0
    
    @pytest.mark.asyncio
    async def test_happy_customer_detection(self, analyzer):
        """Test detection of happy customers."""
        message = "Thank you so much! This is fantastic service and I'm really happy!"
        
        result = await analyzer.analyze_sentiment(message)
        
        assert result.primary_emotion in [EmotionalState.HAPPY, EmotionalState.GRATEFUL]
        assert result.escalation_level == EscalationLevel.NONE
        assert "positive" in result.tone_adaptation or "reinforcing" in result.tone_adaptation
    
    @pytest.mark.asyncio
    async def test_frustrated_customer_with_history(self, analyzer):
        """Test escalation for frustrated customers with escalation history."""
        message = "This is so frustrating! Nothing works properly!"
        customer_history = {"escalation_history": 2}
        
        result = await analyzer.analyze_sentiment(message, customer_history)
        
        assert result.primary_emotion == EmotionalState.FRUSTRATED
        assert result.escalation_level == EscalationLevel.ESCALATE
    
    @pytest.mark.asyncio
    async def test_neutral_message(self, analyzer):
        """Test neutral message handling."""
        message = "I need help with my account information please."
        
        result = await analyzer.analyze_sentiment(message)
        
        assert result.primary_emotion == EmotionalState.NEUTRAL
        assert result.escalation_level == EscalationLevel.NONE
        assert result.tone_adaptation == "neutral_professional"


class TestEmpatheticResponseGenerator:
    """Test empathetic response generation."""
    
    @pytest.fixture
    def generator(self):
        return EmpatheticResponseGenerator()
    
    @pytest.fixture
    def angry_sentiment(self):
        return SentimentAnalysis(
            primary_emotion=EmotionalState.ANGRY,
            intensity=0.8,
            confidence=0.9,
            escalation_level=EscalationLevel.ESCALATE,
            escalation_reason="High intensity angry customer",
            empathy_level_needed=0.9,
            tone_adaptation="apologetic_de_escalation",
            detected_keywords=["furious", "unacceptable"],
            threat_indicators=[]
        )
    
    @pytest.mark.asyncio
    async def test_angry_customer_response(self, generator, angry_sentiment):
        """Test empathetic response for angry customers."""
        customer_info = {"first_name": "John"}
        
        response = await generator.generate_empathetic_response(
            angry_sentiment, "I'm furious!", customer_info
        )
        
        assert "John" in response["opening"]
        assert response["tone_adaptation"] == "apologetic_de_escalation"
        assert response["empathy_level"] == 0.9
        assert response["escalation_note"] != ""
    
    @pytest.mark.asyncio
    async def test_happy_customer_response(self, generator):
        """Test response for happy customers."""
        happy_sentiment = SentimentAnalysis(
            primary_emotion=EmotionalState.HAPPY,
            intensity=0.7,
            confidence=0.8,
            escalation_level=EscalationLevel.NONE,
            escalation_reason=None,
            empathy_level_needed=0.2,
            tone_adaptation="positive_reinforcing",
            detected_keywords=["happy", "excellent"],
            threat_indicators=[]
        )
        
        response = await generator.generate_empathetic_response(
            happy_sentiment, "This is excellent!", {}
        )
        
        assert response["tone_adaptation"] == "positive_reinforcing"
        assert "wonderful" in response["opening"] or "fantastic" in response["opening"]
    
    def test_complete_response_formatting(self, generator):
        """Test complete response formatting."""
        response_parts = {
            "opening": "I sincerely apologize",
            "empathy": "Your frustration is justified",
            "action_intro": "Let me resolve this immediately",
            "escalation_note": "I'm escalating this to management",
            "tone_adaptation": "apologetic_de_escalation",
            "empathy_level": 0.9
        }
        
        complete_response = generator.format_complete_response(
            response_parts, "Here's what I can do for you."
        )
        
        assert "I sincerely apologize" in complete_response
        assert "Your frustration is justified" in complete_response
        assert "Here's what I can do for you" in complete_response
        assert "I'm escalating this to management" in complete_response


class TestOrderHistorySummarizer:
    """Test order history summarization."""
    
    @pytest.fixture
    def summarizer(self):
        return OrderHistorySummarizer()
    
    @pytest.fixture
    def customer_with_many_orders(self):
        return {
            "customer_id": "CUST_001",
            "first_name": "John",
            "total_orders": 25,
            "total_spent": 5000.0,
            "registration_date": "2022-01-01T00:00:00Z",
            "recent_orders": [
                {"order_id": "ORD_001", "date": "2024-01-15T00:00:00Z", "status": "delivered", "total": 199.99},
                {"order_id": "ORD_002", "date": "2024-01-10T00:00:00Z", "status": "shipped", "total": 299.99},
                {"order_id": "ORD_003", "date": "2024-01-05T00:00:00Z", "status": "delivered", "total": 149.99}
            ]
        }
    
    @pytest.fixture
    def new_customer(self):
        return {
            "customer_id": "CUST_NEW",
            "first_name": "Jane",
            "total_orders": 1,
            "total_spent": 50.0,
            "registration_date": "2024-01-01T00:00:00Z",
            "recent_orders": [
                {"order_id": "ORD_NEW", "date": "2024-01-20T00:00:00Z", "status": "processing", "total": 50.0}
            ]
        }
    
    @pytest.mark.asyncio
    async def test_summarize_frequent_customer(self, summarizer, customer_with_many_orders):
        """Test summarization for frequent customers."""
        summary = await summarizer.summarize_order_history(customer_with_many_orders)
        
        assert summary.total_orders == 25
        assert summary.total_spent == 5000.0
        assert summary.order_frequency in ["frequent", "regular"]
        assert summary.loyalty_tier in ["VIP", "Premium"]
        assert "VIP" in summary.summary_text or "premium" in summary.summary_text.lower()
        assert summary.average_order_value == 200.0
    
    @pytest.mark.asyncio
    async def test_summarize_new_customer(self, summarizer, new_customer):
        """Test summarization for new customers."""
        summary = await summarizer.summarize_order_history(new_customer)
        
        assert summary.total_orders == 1
        assert summary.order_frequency == "new"
        assert summary.loyalty_tier == "New"
        assert "new customer" in summary.summary_text.lower()
    
    def test_should_summarize_decision(self, summarizer):
        """Test decision logic for when to summarize vs ask for more info."""
        # Should summarize for customers with many orders
        frequent_customer = {"total_orders": 10, "total_spent": 2000}
        assert summarizer.should_summarize_instead_of_asking(frequent_customer) == True
        
        # Should ask for more info for new customers
        new_customer = {"total_orders": 0, "total_spent": 0}
        assert summarizer.should_summarize_instead_of_asking(new_customer) == False
        
        # Should summarize for high-value customers even with few orders
        high_value_customer = {"total_orders": 2, "total_spent": 1500}
        assert summarizer.should_summarize_instead_of_asking(high_value_customer) == True


class TestActionLinkGenerator:
    """Test action link generation."""
    
    @pytest.fixture
    def generator(self):
        return ActionLinkGenerator("https://support.example.com")
    
    @pytest.fixture
    def customer_with_orders(self):
        return {
            "customer_id": "CUST_001",
            "recent_orders": [
                {"order_id": "ORD_001", "status": "shipped", "total": 199.99},
                {"order_id": "ORD_002", "status": "delivered", "total": 299.99}
            ]
        }
    
    def test_generate_tracking_actions(self, generator, customer_with_orders):
        """Test generation of tracking-related actions."""
        message = "Where is my order? I need to track my shipment."
        
        actions = generator.generate_contextual_actions(message, customer_with_orders)
        
        action_types = [action.action_type for action in actions]
        assert ActionType.TRACK_ORDER in action_types
        
        # Check that tracking action has order ID
        tracking_action = next(a for a in actions if a.action_type == ActionType.TRACK_ORDER)
        assert "ORD_001" in tracking_action.url
    
    def test_generate_refund_actions(self, generator):
        """Test generation of refund-related actions."""
        message = "I want my money back! This product is terrible!"
        
        actions = generator.generate_contextual_actions(message, {})
        
        action_types = [action.action_type for action in actions]
        assert ActionType.REQUEST_REFUND in action_types
    
    def test_generate_escalation_actions_for_angry_customer(self, generator):
        """Test escalation actions for angry customers."""
        from src.services.sentiment_analyzer import SentimentAnalysis, EmotionalState, EscalationLevel
        
        angry_sentiment = SentimentAnalysis(
            primary_emotion=EmotionalState.ANGRY,
            intensity=0.8,
            confidence=0.9,
            escalation_level=EscalationLevel.ESCALATE,
            escalation_reason="High intensity angry customer",
            empathy_level_needed=0.9,
            tone_adaptation="apologetic_de_escalation",
            detected_keywords=["furious"],
            threat_indicators=[]
        )
        
        message = "I'm furious with your service!"
        actions = generator.generate_contextual_actions(message, {}, angry_sentiment)
        
        action_types = [action.action_type for action in actions]
        assert ActionType.ESCALATE_ISSUE in action_types
        assert ActionType.CONTACT_SUPPORT in action_types
    
    def test_format_actions_for_response(self, generator):
        """Test formatting actions for response text."""
        actions = generator.generate_contextual_actions("I need help", {})
        
        formatted = generator.format_actions_for_response(actions)
        
        assert "Quick Actions:" in formatted
        assert "Click here" in formatted
        assert any(action.icon in formatted for action in actions)
    
    def test_action_link_creation(self, generator):
        """Test individual action link creation."""
        customer_info = {"customer_id": "CUST_001"}
        
        action = generator._create_action_link(ActionType.TRACK_ORDER, customer_info)
        
        assert action is not None
        assert action.action_type == ActionType.TRACK_ORDER
        assert "CUST_001" in action.url
        assert action.label == "Track Your Order"
        assert action.icon == "📦"


@pytest.mark.asyncio
async def test_integration_angry_customer_flow():
    """Integration test for angry customer flow."""
    # Initialize services
    sentiment_analyzer = SentimentAnalyzer()
    empathetic_generator = EmpatheticResponseGenerator()
    action_generator = ActionLinkGenerator()
    
    # Simulate angry customer message
    message = "I'm absolutely furious! Your service is terrible and I want my money back!"
    customer_info = {"customer_id": "CUST_001", "first_name": "John", "escalation_history": 0}
    
    # Analyze sentiment
    sentiment = await sentiment_analyzer.analyze_sentiment(message, customer_info)
    
    # Generate empathetic response
    empathetic_response = await empathetic_generator.generate_empathetic_response(
        sentiment, message, customer_info
    )
    
    # Generate action links
    actions = action_generator.generate_contextual_actions(message, customer_info, sentiment)
    
    # Verify the complete flow
    assert sentiment.primary_emotion == EmotionalState.ANGRY
    assert sentiment.escalation_level in [EscalationLevel.ESCALATE, EscalationLevel.MONITOR]
    assert empathetic_response["tone_adaptation"] == "apologetic_de_escalation"
    assert any(action.action_type == ActionType.REQUEST_REFUND for action in actions)
    assert any(action.action_type == ActionType.ESCALATE_ISSUE for action in actions)


@pytest.mark.asyncio
async def test_integration_happy_customer_flow():
    """Integration test for happy customer flow."""
    # Initialize services
    sentiment_analyzer = SentimentAnalyzer()
    empathetic_generator = EmpatheticResponseGenerator()
    action_generator = ActionLinkGenerator()
    
    # Simulate happy customer message
    message = "Thank you so much! Your service is amazing and I'm really happy with my purchase!"
    customer_info = {"customer_id": "CUST_002", "first_name": "Sarah"}
    
    # Analyze sentiment
    sentiment = await sentiment_analyzer.analyze_sentiment(message, customer_info)
    
    # Generate empathetic response
    empathetic_response = await empathetic_generator.generate_empathetic_response(
        sentiment, message, customer_info
    )
    
    # Generate action links
    actions = action_generator.generate_contextual_actions(message, customer_info, sentiment)
    
    # Verify the complete flow
    assert sentiment.primary_emotion in [EmotionalState.HAPPY, EmotionalState.GRATEFUL]
    assert sentiment.escalation_level == EscalationLevel.NONE
    assert empathetic_response["tone_adaptation"] == "positive_reinforcing"
    assert any(action.action_type == ActionType.LEAVE_REVIEW for action in actions)
