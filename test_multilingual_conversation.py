#!/usr/bin/env python3
"""
Test Script for Multilingual Natural Conversation Flow

This script demonstrates multilingual support including Shona, Ndebele,
and other African languages in the natural conversation flow.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.services.multilingual_service import multilingual_service, SupportedLanguage
from src.services.natural_conversation_flow import NaturalConversationFlow, BusinessIntent
from src.services.enhanced_response_generator import EnhancedResponseGenerator


class MultilingualConversationDemo:
    """Demo class for multilingual natural conversation flow."""
    
    def __init__(self):
        """Initialize the demo."""
        self.natural_flow = NaturalConversationFlow()
        self.response_generator = EnhancedResponseGenerator()
        
        # Mock customer info for different languages
        self.customers = {
            "shona": {
                "customer_id": "SHONA_001",
                "first_name": "<PERSON><PERSON>",
                "last_name": "<PERSON><PERSON><PERSON><PERSON>",
                "email": "<EMAIL>",
                "language_preference": "sn",
                "outstanding_balance": 15.50
            },
            "ndebele": {
                "customer_id": "NDEBELE_001", 
                "first_name": "Sipho",
                "last_name": "Ndlovu",
                "email": "<EMAIL>",
                "language_preference": "nd",
                "outstanding_balance": 0.0
            },
            "english": {
                "customer_id": "ENG_001",
                "first_name": "Sarah",
                "last_name": "Johnson", 
                "email": "<EMAIL>",
                "language_preference": "en",
                "outstanding_balance": 25.99
            },
            "swahili": {
                "customer_id": "SWAHILI_001",
                "first_name": "Amina",
                "last_name": "Hassan",
                "email": "<EMAIL>", 
                "language_preference": "sw",
                "outstanding_balance": 12.75
            }
        }
    
    async def test_language_detection(self):
        """Test language detection capabilities."""
        print(f"\n{'='*60}")
        print(f"🔍 LANGUAGE DETECTION TEST")
        print(f"{'='*60}")
        
        test_messages = [
            ("Mhoro! Ndiri kutsvaga package yangu", "Shona"),
            ("Sawubona! Ngifuna ukubona i-order yami", "Ndebele"),
            ("Hello! I need to track my package", "English"),
            ("Hujambo! Nataka kufuatilia kifurushi changu", "Swahili"),
            ("Bonjour! Je veux suivre mon colis", "French"),
            ("Hallo! Ek wil my pakkie volg", "Afrikaans"),
            ("", "Empty message"),
            ("123456", "Numbers only"),
            ("😊👋🎉", "Emojis only")
        ]
        
        for message, expected_lang in test_messages:
            print(f"\n📝 Testing: '{message}' (Expected: {expected_lang})")
            
            try:
                detection_result = await multilingual_service.detect_language(message)
                
                print(f"   ✅ Detected: {detection_result.detected_language}")
                print(f"   📊 Confidence: {detection_result.confidence:.2f}")
                print(f"   🌍 Supported: {detection_result.is_supported}")
                print(f"   🔄 Needs Translation: {detection_result.needs_translation}")
                
                if detection_result.alternative_languages:
                    print(f"   🔀 Alternatives: {detection_result.alternative_languages}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    async def test_translation_service(self):
        """Test translation capabilities."""
        print(f"\n{'='*60}")
        print(f"🔄 TRANSLATION SERVICE TEST")
        print(f"{'='*60}")
        
        test_translations = [
            ("Mhoro! Ndiri kutsvaga package yangu", "sn", "en"),
            ("Hello! I need to track my package", "en", "sn"),
            ("Sawubona! Ngifuna ukubona i-order yami", "nd", "en"),
            ("Hujambo! Nataka kufuatilia kifurushi changu", "sw", "en"),
            ("I am very frustrated with this service!", "en", "sn")
        ]
        
        for text, source_lang, target_lang in test_translations:
            print(f"\n📝 Translating: '{text}'")
            print(f"   From: {source_lang} → To: {target_lang}")
            
            try:
                translation_result = await multilingual_service.translate_text(
                    text, target_language=target_lang, source_language=source_lang
                )
                
                print(f"   ✅ Translation: '{translation_result.translated_text}'")
                print(f"   📊 Confidence: {translation_result.confidence:.2f}")
                print(f"   🔧 Method: {translation_result.translation_method}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    async def test_multilingual_conversation_scenario(self, customer_key: str, messages: list):
        """Test a complete multilingual conversation scenario."""
        customer_info = self.customers[customer_key]
        language_name = {
            "sn": "Shona", "nd": "Ndebele", "en": "English", 
            "sw": "Swahili", "af": "Afrikaans"
        }.get(customer_info["language_preference"], "Unknown")
        
        print(f"\n{'='*60}")
        print(f"🎭 MULTILINGUAL SCENARIO: {language_name} Customer")
        print(f"{'='*60}")
        
        # Initialize conversation
        conversation_id = f"multilingual_{customer_key}_{int(datetime.now().timestamp())}"
        
        try:
            context = await self.natural_flow.initialize_conversation(
                customer_id=customer_info["customer_id"],
                conversation_id=conversation_id,
                customer_language_preference=customer_info["language_preference"]
            )
            
            print(f"👤 Customer: {customer_info['first_name']} {customer_info['last_name']}")
            print(f"🌍 Language: {language_name} ({customer_info['language_preference']})")
            print(f"🆔 Conversation ID: {conversation_id}")
            print(f"📊 Initial Phase: {context.current_phase.value}")
            print(f"🔄 Needs Translation: {context.needs_translation}")
            print()
            
            for i, customer_message in enumerate(messages, 1):
                print(f"💬 Turn {i}")
                print(f"👤 Customer ({language_name}): {customer_message}")
                
                # Process multilingual message
                multilingual_result = await multilingual_service.process_multilingual_message(
                    customer_message, customer_info["language_preference"]
                )
                
                print(f"🔍 Language Analysis:")
                print(f"   - Detected: {multilingual_result['language_detection'].detected_language}")
                print(f"   - Confidence: {multilingual_result['language_detection'].confidence:.2f}")
                print(f"   - Business Intent: {multilingual_result.get('business_intent', 'None')}")
                
                if multilingual_result['translation']:
                    print(f"   - Translated: '{multilingual_result['translation'].translated_text}'")
                
                # Generate response using natural conversation flow
                response, updated_context = await self.natural_flow.generate_response(
                    context=context,
                    customer_message=customer_message,
                    customer_info=customer_info
                )
                
                print(f"🤖 AI Response ({language_name}): {response}")
                print(f"📈 Updated Phase: {updated_context.current_phase.value}")
                print(f"📊 Context: Turns={updated_context.total_turns}, Mood={updated_context.customer_mood}")
                if updated_context.detected_business_intent:
                    print(f"🎯 Business Intent: {updated_context.detected_business_intent.value}")
                
                context = updated_context
                print("-" * 60)
                
                # Small delay for readability
                await asyncio.sleep(0.5)
        
        except Exception as e:
            print(f"❌ Error in scenario: {e}")
            import traceback
            traceback.print_exc()
    
    async def test_empathy_in_multiple_languages(self):
        """Test empathetic responses in different languages."""
        print(f"\n{'='*60}")
        print(f"💝 MULTILINGUAL EMPATHY TEST")
        print(f"{'='*60}")
        
        frustrated_messages = [
            ("I am very frustrated with this service!", "en"),
            ("Ndiri kutsamwa zvikuru nebasa renyu!", "sn"),  # Shona: I am very angry with your service
            ("Ngiyacasula kakhulu ngalensizakalo!", "nd"),   # Ndebele: I am very frustrated with this service
            ("Nimechukizwa sana na huduma hii!", "sw"),      # Swahili: I am very frustrated with this service
        ]
        
        for message, language in frustrated_messages:
            print(f"\n😤 Frustrated Customer ({language}): {message}")
            
            try:
                # Get localized empathy response
                empathy_phrase = multilingual_service.get_localized_empathy(language)
                print(f"💝 Empathy Response ({language}): {empathy_phrase}")
                
                # Test with enhanced response generator
                customer_info = {"customer_id": f"TEST_{language.upper()}", "first_name": "Test"}
                response, metadata = await self.response_generator.generate_natural_response(
                    customer_message=message,
                    customer_info=customer_info,
                    conversation_id=f"empathy_test_{language}"
                )
                
                print(f"🤖 Full AI Response: {response}")
                print(f"📊 Empathy Level: {metadata.get('empathy_applied', 0):.2f}")
                
            except Exception as e:
                print(f"❌ Error: {e}")
    
    async def run_all_tests(self):
        """Run all multilingual tests."""
        print("🌍 Welcome to the Multilingual Natural Conversation Flow Demo!")
        print("This demo shows how AI can understand and respond in multiple languages")
        print("including Shona, Ndebele, Swahili, and other African languages.")
        
        # Test 1: Language Detection
        await self.test_language_detection()
        
        # Test 2: Translation Service
        await self.test_translation_service()
        
        # Test 3: Shona Conversation
        await self.test_multilingual_conversation_scenario(
            "shona",
            [
                "Mhoro! Makadii?",  # Hello! How are you?
                "Ndiri kutsvaga package yangu",  # I am looking for my package
                "Ndine dambudziko nebasa renyu"  # I have a problem with your service
            ]
        )
        
        # Test 4: Ndebele Conversation
        await self.test_multilingual_conversation_scenario(
            "ndebele", 
            [
                "Sawubona! Kunjani?",  # Hello! How are you?
                "Ngifuna ukubona i-order yami",  # I want to see my order
                "Ngiyacasula ngalenkinga"  # I am frustrated with this problem
            ]
        )
        
        # Test 5: English Conversation (for comparison)
        await self.test_multilingual_conversation_scenario(
            "english",
            [
                "Hello! How are you today?",
                "I need to track my package please",
                "I'm having issues with my recent order"
            ]
        )
        
        # Test 6: Swahili Conversation
        await self.test_multilingual_conversation_scenario(
            "swahili",
            [
                "Hujambo! Habari gani?",  # Hello! How are you?
                "Nataka kufuatilia kifurushi changu",  # I want to track my package
                "Nina shida na huduma yenu"  # I have a problem with your service
            ]
        )
        
        # Test 7: Empathy in Multiple Languages
        await self.test_empathy_in_multiple_languages()
        
        print(f"\n{'='*60}")
        print("🎊 Multilingual Demo Complete!")
        print("The system successfully demonstrates:")
        print("✅ Language detection for African languages (Shona, Ndebele, Swahili)")
        print("✅ Basic phrase translation and localized responses")
        print("✅ Multilingual business intent detection")
        print("✅ Empathetic responses in multiple languages")
        print("✅ Natural conversation flow across language barriers")
        print("✅ Graceful handling of unsupported languages")
        print(f"{'='*60}")


async def main():
    """Main demo function."""
    demo = MultilingualConversationDemo()
    
    try:
        await demo.run_all_tests()
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Mock the customer database for demo
    import sys
    from unittest.mock import AsyncMock, MagicMock
    
    # Create mock customer database
    mock_customer_db = MagicMock()
    mock_customer_db.get_customer_info = AsyncMock(return_value={
        "customer_id": "TEST_001",
        "first_name": "Test",
        "last_name": "User",
        "email": "<EMAIL>",
        "language_preference": "en",
        "outstanding_balance": 0.0
    })
    mock_customer_db.get_customer_orders = AsyncMock(return_value=[
        {"order_id": "12345", "status": "shipped", "tracking": "TRK123456"}
    ])
    
    # Patch the customer database in all modules
    sys.modules['src.services.customer_database'] = MagicMock()
    sys.modules['src.services.customer_database'].customer_db = mock_customer_db
    
    # Run the demo
    asyncio.run(main())
