#!/bin/bash

# Smart Customer Support Orchestrator - Stop Local Docker
echo "🛑 Stopping Smart Customer Support Orchestrator..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Stop and remove the container
print_status "Stopping container..."
if docker stop smart-customer-support-local > /dev/null 2>&1; then
    print_success "Container stopped"
else
    print_error "Container was not running or failed to stop"
fi

print_status "Removing container..."
if docker rm smart-customer-support-local > /dev/null 2>&1; then
    print_success "Container removed"
else
    print_error "Container was not found or failed to remove"
fi

# Optionally remove the image
read -p "Do you want to remove the Docker image as well? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Removing Docker image..."
    if docker rmi smart-customer-support:local > /dev/null 2>&1; then
        print_success "Docker image removed"
    else
        print_error "Docker image was not found or failed to remove"
    fi
fi

print_success "Cleanup completed!"
