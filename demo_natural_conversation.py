#!/usr/bin/env python3
"""
Demo Script for Natural Conversation Flow

This script demonstrates the natural conversation flow functionality,
showing how the AI transitions from casual conversation to business tasks.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.services.natural_conversation_flow import NaturalConversationFlow, BusinessIntent
from src.services.conversation_phase_detector import ConversationPhaseDetector
from src.services.enhanced_response_generator import EnhancedResponseGenerator


class ConversationDemo:
    """Demo class for natural conversation flow."""
    
    def __init__(self):
        """Initialize the demo."""
        self.natural_flow = NaturalConversationFlow()
        self.phase_detector = ConversationPhaseDetector()
        self.response_generator = EnhancedResponseGenerator()
        
        # Mock customer info
        self.customer_info = {
            "customer_id": "DEMO_001",
            "first_name": "<PERSON>",
            "last_name": "<PERSON>",
            "email": "<EMAIL>",
            "outstanding_balance": 25.99
        }
    
    async def run_conversation_scenario(self, scenario_name: str, messages: list):
        """Run a conversation scenario."""
        print(f"\n{'='*60}")
        print(f"🎭 SCENARIO: {scenario_name}")
        print(f"{'='*60}")
        
        # Initialize conversation
        conversation_id = f"demo_{scenario_name.lower().replace(' ', '_')}_{int(datetime.now().timestamp())}"
        
        try:
            context = await self.natural_flow.initialize_conversation(
                customer_id=self.customer_info["customer_id"],
                conversation_id=conversation_id
            )
            
            print(f"👤 Customer: {self.customer_info['first_name']} {self.customer_info['last_name']}")
            print(f"🆔 Conversation ID: {conversation_id}")
            print(f"📊 Initial Phase: {context.current_phase.value}")
            print()
            
            for i, customer_message in enumerate(messages, 1):
                print(f"💬 Turn {i}")
                print(f"👤 Customer: {customer_message}")
                
                # Analyze the message
                turn_analysis = self.phase_detector.analyze_conversation_turn(context, customer_message)
                
                print(f"🔍 Analysis:")
                print(f"   - Message Types: {', '.join(turn_analysis['message_classifications'])}")
                print(f"   - Sentiment: {turn_analysis['sentiment']}")
                print(f"   - Business Intent: {turn_analysis['business_intent'] or 'None'}")
                print(f"   - Transition Readiness: {turn_analysis['transition_readiness']:.2f}")
                print(f"   - Should Transition: {turn_analysis['should_transition']}")
                if turn_analysis['recommended_phase']:
                    print(f"   - Recommended Phase: {turn_analysis['recommended_phase']}")
                
                # Generate response
                response, updated_context = await self.natural_flow.generate_response(
                    context=context,
                    customer_message=customer_message,
                    customer_info=self.customer_info
                )
                
                print(f"🤖 AI Response: {response}")
                print(f"📈 Updated Phase: {updated_context.current_phase.value}")
                print(f"📊 Context: Turns={updated_context.total_turns}, Mood={updated_context.customer_mood}")
                if updated_context.detected_business_intent:
                    print(f"🎯 Business Intent: {updated_context.detected_business_intent.value}")
                
                context = updated_context
                print("-" * 60)
                
                # Small delay for readability
                await asyncio.sleep(0.5)
        
        except Exception as e:
            print(f"❌ Error in scenario: {e}")
            import traceback
            traceback.print_exc()
    
    async def run_enhanced_response_demo(self):
        """Demo the enhanced response generator."""
        print(f"\n{'='*60}")
        print(f"🚀 ENHANCED RESPONSE GENERATOR DEMO")
        print(f"{'='*60}")
        
        test_messages = [
            "Hello! How are you doing today?",
            "I'm doing great, thanks for asking!",
            "Actually, I wanted to check on my recent order",
            "I'm really frustrated with the delivery delay!",
            "Thank you so much for your help!"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n💬 Test Message {i}: {message}")
            
            try:
                response, metadata = await self.response_generator.generate_natural_response(
                    customer_message=message,
                    customer_info=self.customer_info,
                    conversation_id=f"enhanced_demo_{i}"
                )
                
                print(f"🤖 Response: {response}")
                print(f"📊 Metadata:")
                print(f"   - Phase: {metadata.get('conversation_phase', 'unknown')}")
                print(f"   - Strategy: {metadata.get('response_strategy', {}).get('type', 'unknown')}")
                print(f"   - Empathy Level: {metadata.get('empathy_applied', 0):.2f}")
                print(f"   - Business Intent: {metadata.get('business_intent_detected', 'None')}")
                
            except Exception as e:
                print(f"❌ Error generating response: {e}")
    
    async def run_all_demos(self):
        """Run all demo scenarios."""
        print("🎉 Welcome to the Natural Conversation Flow Demo!")
        print("This demo shows how AI can engage in natural, human-like conversation")
        print("before smoothly transitioning to business tasks.")
        
        # Scenario 1: Happy customer with natural transition
        await self.run_conversation_scenario(
            "Happy Customer - Natural Transition",
            [
                "Hi there! How are you?",
                "I'm doing wonderful, thank you! How about you?",
                "That's great to hear!",
                "By the way, I was wondering about my recent order"
            ]
        )
        
        # Scenario 2: Direct business inquiry
        await self.run_conversation_scenario(
            "Direct Business Inquiry",
            [
                "Hello, I need to track my package",
                "It's order number 12345",
                "When will it arrive?"
            ]
        )
        
        # Scenario 3: Frustrated customer
        await self.run_conversation_scenario(
            "Frustrated Customer",
            [
                "Hi",
                "Not great, to be honest",
                "I'm really frustrated with my recent experience",
                "My payment was charged twice and I can't get through to support!"
            ]
        )
        
        # Scenario 4: Casual conversation that stays casual
        await self.run_conversation_scenario(
            "Extended Casual Conversation",
            [
                "Good morning!",
                "I'm having a lovely day, thanks!",
                "The weather is beautiful today",
                "Yes, it really makes a difference to my mood",
                "Actually, I do have a question about my account"
            ]
        )
        
        # Enhanced response generator demo
        await self.run_enhanced_response_demo()
        
        print(f"\n{'='*60}")
        print("🎊 Demo Complete!")
        print("The natural conversation flow successfully demonstrates:")
        print("✅ Friendly, human-like greetings and casual conversation")
        print("✅ Smooth transitions from casual to business topics")
        print("✅ Empathetic responses to frustrated customers")
        print("✅ Direct handling of business inquiries")
        print("✅ Context-aware conversation management")
        print(f"{'='*60}")


async def main():
    """Main demo function."""
    demo = ConversationDemo()
    
    try:
        await demo.run_all_demos()
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Mock the customer database for demo
    import sys
    from unittest.mock import AsyncMock, MagicMock
    
    # Create mock customer database
    mock_customer_db = MagicMock()
    mock_customer_db.get_customer_info = AsyncMock(return_value={
        "customer_id": "DEMO_001",
        "first_name": "Alex",
        "last_name": "Johnson",
        "email": "<EMAIL>",
        "outstanding_balance": 25.99
    })
    mock_customer_db.get_customer_orders = AsyncMock(return_value=[
        {"order_id": "12345", "status": "shipped", "tracking": "TRK123456"}
    ])
    
    # Patch the customer database in all modules
    sys.modules['src.services.customer_database'] = MagicMock()
    sys.modules['src.services.customer_database'].customer_db = mock_customer_db
    
    # Run the demo
    asyncio.run(main())
