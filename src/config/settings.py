"""Configuration settings for the Smart Customer Support Orchestrator."""

import os
from typing import Dict, Any, List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from enum import Enum


class Priority(str, Enum):
    """Priority levels for customer support tickets."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class Sentiment(str, Enum):
    """Sentiment analysis results."""
    VERY_POSITIVE = "very_positive"
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"
    VERY_NEGATIVE = "very_negative"


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Google Cloud Configuration
    google_cloud_project: str = Field(..., env="GOOGLE_CLOUD_PROJECT")
    google_cloud_region: str = Field(default="us-central1", env="GOOGLE_CLOUD_REGION")
    vertex_ai_location: str = Field(default="us-central1", env="VERTEX_AI_LOCATION")
    
    # BigQuery Configuration
    bigquery_dataset: str = Field(default="customer_support", env="BIGQUERY_DATASET")
    bigquery_table_knowledge: str = Field(default="knowledge_base", env="BIGQUERY_TABLE_KNOWLEDGE")
    bigquery_table_tickets: str = Field(default="support_tickets", env="BIGQUERY_TABLE_TICKETS")
    bigquery_table_responses: str = Field(default="agent_responses", env="BIGQUERY_TABLE_RESPONSES")
    
    # Cloud Storage Configuration
    storage_bucket: str = Field(..., env="STORAGE_BUCKET")
    storage_bucket_documents: str = Field(..., env="STORAGE_BUCKET_DOCUMENTS")
    
    # Vertex AI Models
    vertex_ai_model_intake: str = Field(default="gemini-1.5-pro", env="VERTEX_AI_MODEL_INTAKE")
    vertex_ai_model_knowledge: str = Field(default="gemini-1.5-pro", env="VERTEX_AI_MODEL_KNOWLEDGE")
    vertex_ai_model_resolution: str = Field(default="gemini-1.5-pro", env="VERTEX_AI_MODEL_RESOLUTION")
    vertex_ai_model_quality: str = Field(default="gemini-1.5-pro", env="VERTEX_AI_MODEL_QUALITY")
    
    # Agent Configuration
    intake_agent_temperature: float = Field(default=0.3, env="INTAKE_AGENT_TEMPERATURE")
    knowledge_agent_search_limit: int = Field(default=10, env="KNOWLEDGE_AGENT_SEARCH_LIMIT")
    resolution_agent_max_length: int = Field(default=500, env="RESOLUTION_AGENT_MAX_LENGTH")
    quality_agent_min_score: float = Field(default=0.85, env="QUALITY_AGENT_MIN_SCORE")
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8080, env="API_PORT")
    api_workers: int = Field(default=4, env="API_WORKERS")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    structured_logging: bool = Field(default=True, env="STRUCTURED_LOGGING")
    
    # Monitoring
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    
    # Development
    debug: bool = Field(default=False, env="DEBUG")
    development_mode: bool = Field(default=False, env="DEVELOPMENT_MODE")

    # AI API Keys (optional)
    google_api_key: Optional[str] = Field(default=None, env="GOOGLE_API_KEY")
    google_api_keys: Optional[str] = Field(default=None, env="GOOGLE_API_KEYS")  # Comma-separated
    google_api_key_1: Optional[str] = Field(default=None, env="GOOGLE_API_KEY_1")
    google_api_key_2: Optional[str] = Field(default=None, env="GOOGLE_API_KEY_2")
    google_api_key_3: Optional[str] = Field(default=None, env="GOOGLE_API_KEY_3")
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")

    # Quality and Escalation Settings
    escalation_sentiment_threshold: str = Field(default="negative", env="ESCALATION_SENTIMENT_THRESHOLD")

    # Voice Configuration
    voice_enabled: bool = Field(default=True, env="VOICE_ENABLED")
    voice_language: str = Field(default="en-US", env="VOICE_LANGUAGE")
    voice_speed: float = Field(default=1.0, env="VOICE_SPEED")

    def get_google_api_keys(self) -> List[str]:
        """Get all available Google API keys."""
        keys = []

        # Add individual keys
        if self.google_api_key:
            keys.append(self.google_api_key)
        if self.google_api_key_1:
            keys.append(self.google_api_key_1)
        if self.google_api_key_2:
            keys.append(self.google_api_key_2)
        if self.google_api_key_3:
            keys.append(self.google_api_key_3)

        # Add comma-separated keys
        if self.google_api_keys:
            csv_keys = [k.strip() for k in self.google_api_keys.split(',') if k.strip()]
            keys.extend(csv_keys)

        # Remove duplicates while preserving order
        seen = set()
        unique_keys = []
        for key in keys:
            if key not in seen:
                seen.add(key)
                unique_keys.append(key)

        return unique_keys

    class Config:
        env_file = ".env"
        case_sensitive = False


# Business Rules Configuration
ESCALATION_TRIGGERS = [
    "legal_threat",
    "high_value_customer", 
    "technical_bug_report",
    "sentiment_very_negative",
    "billing_dispute_high_amount",
    "data_privacy_concern"
]

PRIORITY_MAPPING = {
    "order_issue": Priority.HIGH,
    "billing_dispute": Priority.HIGH,
    "technical_support": Priority.MEDIUM,
    "general_inquiry": Priority.LOW,
    "account_access": Priority.MEDIUM,
    "refund_request": Priority.HIGH,
    "product_defect": Priority.HIGH,
    "shipping_delay": Priority.MEDIUM
}

INTENT_CATEGORIES = [
    "order_issue",
    "billing_dispute", 
    "technical_support",
    "general_inquiry",
    "account_access",
    "refund_request",
    "product_defect",
    "shipping_delay",
    "complaint",
    "compliment",
    "feature_request"
]

# Quality thresholds
QUALITY_THRESHOLDS = {
    "min_response_length": 50,
    "max_response_length": 1000,
    "min_politeness_score": 0.7,
    "min_helpfulness_score": 0.8,
    "min_accuracy_score": 0.9
}


# Global settings instance
settings = Settings()
