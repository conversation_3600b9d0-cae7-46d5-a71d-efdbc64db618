"""Knowledge Agent for searching and retrieving relevant information."""

import time
from typing import Dict, Any, List

try:
    from google.cloud import bigquery
    from google.cloud import storage
    GOOGLE_CLOUD_AVAILABLE = True
except ImportError:
    GOOGLE_CLOUD_AVAILABLE = False
    print("Warning: Google Cloud libraries not available. Using mock responses for demo.")

from .base_agent import BaseAgent
from ..models import AgentContex<PERSON>, KnowledgeResult
from ..config import settings


class KnowledgeAgent(BaseAgent):
    """Agent responsible for searching knowledge base and retrieving relevant information."""
    
    def __init__(self):
        """Initialize the Knowledge Agent."""
        super().__init__(
            agent_name="knowledge",
            model_name=settings.vertex_ai_model_knowledge
        )
        
        # Initialize Google Cloud clients
        if GOOGLE_CLOUD_AVAILABLE:
            self.bigquery_client = bigquery.Client(project=settings.google_cloud_project)
            self.storage_client = storage.Client(project=settings.google_cloud_project)
        else:
            self.bigquery_client = None
            self.storage_client = None
        
        # Knowledge base configuration
        self.search_limit = settings.knowledge_agent_search_limit
        self.relevance_threshold = 0.7
    
    async def process(self, context: AgentContext) -> AgentContext:
        """Search for relevant knowledge based on customer message and intake analysis.
        
        Args:
            context: Agent context with customer message and intake results
            
        Returns:
            Context updated with knowledge search results
        """
        start_time = time.time()
        
        if not context.intake_result:
            raise ValueError("Intake result required for knowledge search")
        
        # Generate search queries based on intake analysis
        search_queries = await self._generate_search_queries(context)
        
        # Search different knowledge sources
        relevant_documents = await self._search_documents(search_queries)
        faq_matches = await self._search_faqs(search_queries)
        similar_tickets = await self._search_similar_tickets(context)
        
        # Calculate relevance scores
        relevance_scores = self._calculate_relevance_scores(
            relevant_documents + faq_matches + similar_tickets,
            context.customer_message.content
        )
        
        # Create knowledge result
        knowledge_result = KnowledgeResult(
            message_id=context.message_id,
            relevant_documents=relevant_documents,
            faq_matches=faq_matches,
            similar_tickets=similar_tickets,
            search_queries_used=search_queries,
            total_results_found=len(relevant_documents) + len(faq_matches) + len(similar_tickets),
            relevance_scores=relevance_scores,
            processing_time=time.time() - start_time
        )
        
        # Update context
        context.knowledge_result = knowledge_result
        
        self.logger.info(
            "Knowledge search completed",
            message_id=context.message_id,
            total_results=knowledge_result.total_results_found,
            documents=len(relevant_documents),
            faqs=len(faq_matches),
            similar_tickets=len(similar_tickets)
        )
        
        return context
    
    async def _generate_search_queries(self, context: AgentContext) -> List[str]:
        """Generate search queries based on customer message and intake analysis.
        
        Args:
            context: Agent context with message and intake results
            
        Returns:
            List of search queries to use
        """
        intake = context.intake_result
        message_content = context.customer_message.content
        
        prompt = f"""
Generate effective search queries for a customer support knowledge base based on this analysis:

Customer Message: "{message_content}"
Intent: {intake.intent}
Sentiment: {intake.sentiment.value}
Key Entities: {intake.extracted_entities}

Generate 3-5 specific search queries that would help find relevant:
1. Documentation
2. FAQ entries  
3. Similar past tickets
4. Product information
5. Policy information

Return the queries as a JSON array of strings:
["query1", "query2", "query3", ...]

Focus on the core issue and use specific terms that would match knowledge base content.
"""
        
        response = await self._generate_response(prompt, temperature=0.2)
        
        try:
            queries = self._extract_json_from_response(response)
            if isinstance(queries, list):
                return queries[:5]  # Limit to 5 queries
            else:
                # Fallback queries based on intent and entities
                return self._generate_fallback_queries(context)
        except:
            return self._generate_fallback_queries(context)
    
    def _generate_fallback_queries(self, context: AgentContext) -> List[str]:
        """Generate fallback search queries when AI generation fails.
        
        Args:
            context: Agent context
            
        Returns:
            List of fallback search queries
        """
        intake = context.intake_result
        queries = [intake.intent.replace("_", " ")]
        
        # Add entity-based queries
        for key, value in intake.extracted_entities.items():
            if value:
                queries.append(f"{key} {value}")
        
        # Add sentiment-based queries if negative
        if intake.sentiment.value in ["negative", "very_negative"]:
            queries.append(f"{intake.intent} complaint resolution")
        
        return queries[:5]
    
    async def _search_documents(self, queries: List[str]) -> List[Dict[str, Any]]:
        """Search document knowledge base.

        Args:
            queries: Search queries to use

        Returns:
            List of relevant documents
        """
        if not GOOGLE_CLOUD_AVAILABLE or self.bigquery_client is None:
            return self._mock_search_documents(queries)

        documents = []

        try:
            # Query BigQuery knowledge base
            for query in queries:
                sql = f"""
                SELECT
                    document_id,
                    title,
                    content,
                    category,
                    last_updated,
                    SCORE() as relevance_score
                FROM `{settings.google_cloud_project}.{settings.bigquery_dataset}.{settings.bigquery_table_knowledge}`
                WHERE SEARCH(content, @query)
                ORDER BY relevance_score DESC
                LIMIT {self.search_limit}
                """

                job_config = bigquery.QueryJobConfig(
                    query_parameters=[
                        bigquery.ScalarQueryParameter("query", "STRING", query)
                    ]
                )

                query_job = self.bigquery_client.query(sql, job_config=job_config)
                results = query_job.result()

                for row in results:
                    if row.relevance_score >= self.relevance_threshold:
                        documents.append({
                            "document_id": row.document_id,
                            "title": row.title,
                            "content": row.content[:500],  # Truncate for context
                            "category": row.category,
                            "last_updated": row.last_updated.isoformat() if row.last_updated else None,
                            "relevance_score": float(row.relevance_score),
                            "source": "documents"
                        })

        except Exception as e:
            self.logger.error("Error searching documents", error=str(e))

        # Remove duplicates and sort by relevance
        unique_docs = {doc["document_id"]: doc for doc in documents}
        return sorted(unique_docs.values(), key=lambda x: x["relevance_score"], reverse=True)

    def _mock_search_documents(self, queries: List[str]) -> List[Dict[str, Any]]:
        """Mock document search for demo purposes."""
        mock_documents = [
            {
                "document_id": "doc_001",
                "title": "Return Policy",
                "content": "Our return policy allows customers to return items within 30 days of purchase for a full refund. Items must be in original condition with tags attached.",
                "category": "policies",
                "last_updated": "2024-01-15T10:00:00Z",
                "relevance_score": 0.9,
                "source": "documents"
            },
            {
                "document_id": "doc_002",
                "title": "Shipping Information",
                "content": "We offer free standard shipping on orders over $50. Standard shipping takes 3-5 business days. Express shipping available for $9.99.",
                "category": "shipping",
                "last_updated": "2024-01-10T14:30:00Z",
                "relevance_score": 0.8,
                "source": "documents"
            }
        ]

        # Filter based on query relevance (simple keyword matching)
        relevant_docs = []
        for doc in mock_documents:
            for query in queries:
                if any(word.lower() in doc["content"].lower() or word.lower() in doc["title"].lower()
                      for word in query.split()):
                    relevant_docs.append(doc)
                    break

        return relevant_docs[:self.search_limit]
    
    async def _search_faqs(self, queries: List[str]) -> List[Dict[str, Any]]:
        """Search FAQ knowledge base.
        
        Args:
            queries: Search queries to use
            
        Returns:
            List of relevant FAQ entries
        """
        faqs = []
        
        try:
            # This would typically search a dedicated FAQ table
            # For now, we'll simulate with sample FAQs
            sample_faqs = [
                {
                    "faq_id": "faq_001",
                    "question": "How do I return a damaged product?",
                    "answer": "To return a damaged product, please contact our support team with your order number and photos of the damage. We'll provide a prepaid return label and process your refund within 5-7 business days.",
                    "category": "returns",
                    "relevance_score": 0.9
                },
                {
                    "faq_id": "faq_002", 
                    "question": "What is your refund policy?",
                    "answer": "We offer full refunds within 30 days of purchase for unused items in original packaging. Refunds are processed to the original payment method within 5-7 business days.",
                    "category": "refunds",
                    "relevance_score": 0.8
                },
                {
                    "faq_id": "faq_003",
                    "question": "How can I track my order?",
                    "answer": "You can track your order using the tracking number sent to your email, or by logging into your account and viewing order history.",
                    "category": "shipping",
                    "relevance_score": 0.7
                }
            ]
            
            # Filter FAQs based on query relevance (simplified)
            for faq in sample_faqs:
                for query in queries:
                    if any(word.lower() in faq["question"].lower() or word.lower() in faq["answer"].lower() 
                          for word in query.split()):
                        faq["source"] = "faq"
                        faqs.append(faq)
                        break
        
        except Exception as e:
            self.logger.error("Error searching FAQs", error=str(e))
        
        return faqs[:self.search_limit]
    
    async def _search_similar_tickets(self, context: AgentContext) -> List[Dict[str, Any]]:
        """Search for similar past support tickets.

        Args:
            context: Agent context

        Returns:
            List of similar tickets
        """
        if not GOOGLE_CLOUD_AVAILABLE or self.bigquery_client is None:
            return self._mock_search_similar_tickets(context)

        tickets = []

        try:
            # Query similar tickets based on intent and entities
            intent = context.intake_result.intent

            sql = f"""
            SELECT
                ticket_id,
                customer_message,
                final_response,
                resolution_type,
                created_at,
                customer_satisfaction_score
            FROM `{settings.google_cloud_project}.{settings.bigquery_dataset}.{settings.bigquery_table_tickets}`
            WHERE intent = @intent
            AND status = 'completed'
            AND customer_satisfaction_score >= 4.0
            ORDER BY created_at DESC
            LIMIT {self.search_limit}
            """

            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("intent", "STRING", intent)
                ]
            )

            query_job = self.bigquery_client.query(sql, job_config=job_config)
            results = query_job.result()

            for row in results:
                tickets.append({
                    "ticket_id": row.ticket_id,
                    "customer_message": row.customer_message[:200],  # Truncate
                    "final_response": row.final_response[:300],  # Truncate
                    "resolution_type": row.resolution_type,
                    "created_at": row.created_at.isoformat() if row.created_at else None,
                    "satisfaction_score": float(row.customer_satisfaction_score) if row.customer_satisfaction_score else None,
                    "source": "similar_tickets"
                })

        except Exception as e:
            self.logger.error("Error searching similar tickets", error=str(e))

        return tickets

    def _mock_search_similar_tickets(self, context: AgentContext) -> List[Dict[str, Any]]:
        """Mock similar tickets search for demo purposes."""
        intent = context.intake_result.intent if context.intake_result else "general_inquiry"

        mock_tickets = [
            {
                "ticket_id": "ticket_001",
                "customer_message": "My order arrived damaged and I need a replacement",
                "final_response": "I sincerely apologize for the damaged item. I've arranged for an immediate replacement with expedited shipping at no cost.",
                "resolution_type": "replacement_with_compensation",
                "created_at": "2024-01-15T14:22:00Z",
                "satisfaction_score": 4.5,
                "source": "similar_tickets"
            },
            {
                "ticket_id": "ticket_002",
                "customer_message": "I cannot access my account, password reset not working",
                "final_response": "I can help you regain access to your account. I've manually reset your password and sent new credentials to your email.",
                "resolution_type": "technical_assistance",
                "created_at": "2024-01-16T09:45:00Z",
                "satisfaction_score": 4.2,
                "source": "similar_tickets"
            }
        ]

        # Return tickets that match the intent (simplified)
        return mock_tickets[:2]
    
    def _calculate_relevance_scores(self, results: List[Dict[str, Any]], query: str) -> List[float]:
        """Calculate relevance scores for search results.
        
        Args:
            results: Search results
            query: Original customer message
            
        Returns:
            List of relevance scores
        """
        scores = []
        
        for result in results:
            # Use existing relevance score if available
            if "relevance_score" in result:
                scores.append(result["relevance_score"])
            else:
                # Calculate simple relevance based on keyword matching
                content = ""
                if "content" in result:
                    content += result["content"]
                if "answer" in result:
                    content += result["answer"]
                if "final_response" in result:
                    content += result["final_response"]
                
                # Simple keyword matching score
                query_words = set(query.lower().split())
                content_words = set(content.lower().split())
                
                if len(query_words) > 0:
                    overlap = len(query_words.intersection(content_words))
                    score = overlap / len(query_words)
                else:
                    score = 0.0
                
                scores.append(score)
        
        return scores
