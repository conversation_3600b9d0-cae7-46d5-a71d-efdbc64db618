"""Quality Agent for reviewing and approving customer support responses."""

import time
from typing import Dict, Any, List

from .base_agent import BaseAgent
from ..models import Agent<PERSON>ontext, QualityResult
from ..config import settings, QUALITY_THRESHOLDS


class QualityAgent(BaseAgent):
    """Agent responsible for quality assurance and final response approval."""
    
    def __init__(self):
        """Initialize the Quality Agent."""
        super().__init__(
            agent_name="quality",
            model_name=settings.vertex_ai_model_quality
        )
        
        self.min_quality_score = settings.quality_agent_min_score
        self.quality_thresholds = QUALITY_THRESHOLDS
    
    async def process(self, context: AgentContext) -> AgentContext:
        """Review response quality and provide final approval.
        
        Args:
            context: Agent context with all previous results
            
        Returns:
            Context updated with quality review results
        """
        start_time = time.time()
        
        if not context.resolution_result:
            raise ValueError("Resolution result required for quality review")
        
        # Perform quality analysis
        quality_scores = await self._analyze_response_quality(context)
        
        # Check brand voice compliance
        brand_voice_score = await self._check_brand_voice(context)
        
        # Generate improvement suggestions
        improvements = await self._generate_improvements(context, quality_scores)
        
        # Calculate overall quality score
        overall_score = self._calculate_overall_score(quality_scores, brand_voice_score)
        
        # Determine approval status
        approved = overall_score >= self.min_quality_score
        
        # Generate final response (improved if needed)
        final_response = await self._generate_final_response(context, improvements, approved)
        
        # Create quality result
        quality_result = QualityResult(
            message_id=context.message_id,
            approved=approved,
            quality_score=overall_score,
            brand_voice_score=brand_voice_score,
            accuracy_score=quality_scores.get("accuracy", 0.8),
            helpfulness_score=quality_scores.get("helpfulness", 0.8),
            politeness_score=quality_scores.get("politeness", 0.8),
            suggested_improvements=improvements,
            final_response=final_response,
            processing_time=time.time() - start_time
        )
        
        # Update context
        context.quality_result = quality_result
        
        self.logger.info(
            "Quality review completed",
            message_id=context.message_id,
            approved=approved,
            quality_score=overall_score,
            improvements_count=len(improvements)
        )
        
        return context
    
    async def _analyze_response_quality(self, context: AgentContext) -> Dict[str, float]:
        """Analyze the quality of the generated response.
        
        Args:
            context: Agent context with resolution result
            
        Returns:
            Dictionary of quality scores
        """
        response_draft = context.resolution_result.response_draft
        customer_message = context.customer_message.content
        intake = context.intake_result
        
        prompt = f"""
Analyze the quality of this customer service response across multiple dimensions:

Customer Message: "{customer_message}"
Customer Intent: {intake.intent}
Customer Sentiment: {intake.sentiment.value}

Response to Analyze: "{response_draft}"

Rate the response on these dimensions (0.0 to 1.0):

{{
    "accuracy": "How factually correct and relevant is the response?",
    "helpfulness": "How well does it address the customer's specific need?",
    "politeness": "How professional and courteous is the tone?",
    "clarity": "How clear and easy to understand is the response?",
    "completeness": "How thoroughly does it address the customer's concern?",
    "empathy": "How well does it acknowledge the customer's feelings?"
}}

Return your analysis as JSON:
{{
    "accuracy": 0.0-1.0,
    "helpfulness": 0.0-1.0,
    "politeness": 0.0-1.0,
    "clarity": 0.0-1.0,
    "completeness": 0.0-1.0,
    "empathy": 0.0-1.0
}}
"""
        
        response = await self._generate_response(prompt, temperature=0.2)
        
        try:
            scores = self._extract_json_from_response(response)
            # Validate scores are in range
            for key, value in scores.items():
                if not isinstance(value, (int, float)) or not (0.0 <= value <= 1.0):
                    scores[key] = 0.8  # Default score
            return scores
        except:
            # Return default scores if parsing fails
            return {
                "accuracy": 0.8,
                "helpfulness": 0.8,
                "politeness": 0.8,
                "clarity": 0.8,
                "completeness": 0.8,
                "empathy": 0.8
            }
    
    async def _check_brand_voice(self, context: AgentContext) -> float:
        """Check brand voice compliance.
        
        Args:
            context: Agent context
            
        Returns:
            Brand voice compliance score
        """
        response_draft = context.resolution_result.response_draft
        
        prompt = f"""
Evaluate how well this customer service response aligns with professional brand voice standards:

Response: "{response_draft}"

Brand Voice Guidelines:
- Professional yet friendly
- Empathetic and understanding
- Clear and concise
- Solution-focused
- Respectful and courteous
- Avoids jargon or overly technical language
- Uses positive language even when delivering difficult news

Rate the brand voice compliance from 0.0 to 1.0, where:
- 1.0 = Perfect brand voice alignment
- 0.8+ = Good alignment with minor issues
- 0.6+ = Acceptable with some improvements needed
- Below 0.6 = Significant brand voice issues

Return just the numeric score as a float.
"""
        
        response = await self._generate_response(prompt, temperature=0.1)
        
        try:
            # Extract numeric score from response
            import re
            score_match = re.search(r'(\d+\.?\d*)', response)
            if score_match:
                score = float(score_match.group(1))
                return max(0.0, min(1.0, score))
        except:
            pass
        
        return 0.8  # Default score
    
    async def _generate_improvements(self, context: AgentContext, quality_scores: Dict[str, float]) -> List[str]:
        """Generate specific improvement suggestions.
        
        Args:
            context: Agent context
            quality_scores: Quality analysis scores
            
        Returns:
            List of improvement suggestions
        """
        response_draft = context.resolution_result.response_draft
        low_scores = {k: v for k, v in quality_scores.items() if v < 0.7}
        
        if not low_scores:
            return []  # No improvements needed
        
        prompt = f"""
The following customer service response has some quality issues that need improvement:

Response: "{response_draft}"

Areas needing improvement (scores below 0.7):
{', '.join(f"{k}: {v:.2f}" for k, v in low_scores.items())}

Provide 2-4 specific, actionable improvement suggestions as a JSON array:
["suggestion1", "suggestion2", "suggestion3"]

Focus on concrete changes that would address the low-scoring areas.
"""
        
        response = await self._generate_response(prompt, temperature=0.3)
        
        try:
            improvements = self._extract_json_from_response(response)
            if isinstance(improvements, list):
                return improvements[:4]  # Limit to 4 suggestions
        except:
            pass
        
        # Generate fallback improvements based on low scores
        return self._generate_fallback_improvements(low_scores)
    
    def _generate_fallback_improvements(self, low_scores: Dict[str, float]) -> List[str]:
        """Generate fallback improvement suggestions.
        
        Args:
            low_scores: Dictionary of low quality scores
            
        Returns:
            List of fallback improvement suggestions
        """
        improvements = []
        
        improvement_templates = {
            "accuracy": "Verify factual information and ensure response addresses the specific issue",
            "helpfulness": "Provide more specific, actionable solutions to the customer's problem",
            "politeness": "Use more courteous language and acknowledge the customer's concern",
            "clarity": "Simplify language and structure the response more clearly",
            "completeness": "Address all aspects of the customer's inquiry more thoroughly",
            "empathy": "Show more understanding and acknowledgment of the customer's feelings"
        }
        
        for dimension in low_scores:
            if dimension in improvement_templates:
                improvements.append(improvement_templates[dimension])
        
        return improvements
    
    def _calculate_overall_score(self, quality_scores: Dict[str, float], brand_voice_score: float) -> float:
        """Calculate overall quality score.
        
        Args:
            quality_scores: Individual quality dimension scores
            brand_voice_score: Brand voice compliance score
            
        Returns:
            Overall quality score
        """
        # Weight the different quality dimensions
        weights = {
            "accuracy": 0.25,
            "helpfulness": 0.25,
            "politeness": 0.15,
            "clarity": 0.15,
            "completeness": 0.10,
            "empathy": 0.10
        }
        
        # Calculate weighted average of quality scores
        quality_average = sum(
            quality_scores.get(dim, 0.8) * weight 
            for dim, weight in weights.items()
        )
        
        # Combine with brand voice score (70% quality, 30% brand voice)
        overall_score = (quality_average * 0.7) + (brand_voice_score * 0.3)
        
        return max(0.0, min(1.0, overall_score))
    
    async def _generate_final_response(self, context: AgentContext, improvements: List[str], approved: bool) -> str:
        """Generate the final approved response.
        
        Args:
            context: Agent context
            improvements: List of improvement suggestions
            approved: Whether the original response was approved
            
        Returns:
            Final response text
        """
        original_response = context.resolution_result.response_draft
        
        if approved and not improvements:
            # Response is good as-is
            return original_response
        
        # Generate improved response
        customer_message = context.customer_message.content
        intake = context.intake_result
        
        improvement_text = "\n".join(f"- {imp}" for imp in improvements) if improvements else "Minor refinements for brand voice"
        
        prompt = f"""
Improve this customer service response based on the feedback provided:

Original Customer Message: "{customer_message}"
Customer Intent: {intake.intent}
Customer Sentiment: {intake.sentiment.value}

Original Response: "{original_response}"

Improvements Needed:
{improvement_text}

Generate an improved version that:
1. Addresses all the improvement suggestions
2. Maintains the helpful content from the original
3. Uses professional, empathetic language
4. Stays within reasonable length (under 500 characters)
5. Directly addresses the customer's specific concern

Return only the improved response text, no additional formatting or explanation.
"""
        
        improved_response = await self._generate_response(prompt, temperature=0.3)
        
        # Clean the improved response
        improved_response = improved_response.strip()
        
        # Ensure it's not too long
        if len(improved_response) > 500:
            sentences = improved_response.split('.')
            truncated = ""
            for sentence in sentences:
                if len(truncated + sentence + '.') <= 500:
                    truncated += sentence + '.'
                else:
                    break
            improved_response = truncated.strip()
        
        # Fallback to original if improvement failed
        if len(improved_response) < 20:
            return original_response
        
        return improved_response
