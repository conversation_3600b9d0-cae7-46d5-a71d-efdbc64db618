"""Intake Agent for analyzing customer messages and routing."""

import json
import time
from typing import Dict, Any, List

from .base_agent import BaseAgent
from ..models import Agent<PERSON>ontext, IntakeResult
from ..config import settings, Priority, Sentiment, PRIORITY_MAPPING, ESCALATION_TRIGGERS


class IntakeAgent(BaseAgent):
    """Agent responsible for analyzing incoming customer messages."""
    
    def __init__(self):
        """Initialize the Intake Agent."""
        super().__init__(
            agent_name="intake",
            model_name=settings.vertex_ai_model_intake
        )
    
    async def process(self, context: AgentContext) -> AgentContext:
        """Analyze customer message for intent, sentiment, and priority.
        
        Args:
            context: Agent context with customer message
            
        Returns:
            Context updated with intake analysis results
        """
        start_time = time.time()
        
        # Create analysis prompt
        prompt = self._create_analysis_prompt(context.customer_message.content)
        
        # Generate analysis
        response = await self._generate_response(
            prompt,
            temperature=settings.intake_agent_temperature,
            max_tokens=1000
        )
        
        # Parse the response
        analysis = self._parse_analysis_response(response)
        
        # Create intake result
        intake_result = IntakeResult(
            message_id=context.message_id,
            intent=analysis.get("intent", "general_inquiry"),
            sentiment=Sentiment(analysis.get("sentiment", "neutral")),
            priority=self._determine_priority(analysis),
            urgency_score=analysis.get("urgency_score", 0.5),
            extracted_entities=analysis.get("entities", {}),
            escalation_flags=self._check_escalation_flags(analysis),
            confidence_score=analysis.get("confidence_score", 0.8),
            processing_time=time.time() - start_time
        )
        
        # Update context
        context.intake_result = intake_result
        
        self.logger.info(
            "Intake analysis completed",
            message_id=context.message_id,
            intent=intake_result.intent,
            sentiment=intake_result.sentiment.value,
            priority=intake_result.priority.value,
            urgency_score=intake_result.urgency_score
        )
        
        return context
    
    def _create_analysis_prompt(self, message_content: str) -> str:
        """Create the analysis prompt for the customer message.
        
        Args:
            message_content: The customer's message
            
        Returns:
            Formatted prompt for analysis
        """
        return f"""
You are an expert customer service intake analyst. Analyze the following customer message and provide a detailed analysis.

Customer Message:
"{message_content}"

Please analyze this message and provide your response in the following JSON format:

{{
    "intent": "one of: order_issue, billing_dispute, technical_support, general_inquiry, account_access, account_info, refund_request, product_defect, shipping_delay, complaint, compliment, feature_request",
    "sentiment": "one of: very_positive, positive, neutral, negative, very_negative",
    "urgency_score": "float between 0.0 and 1.0 indicating urgency",
    "confidence_score": "float between 0.0 and 1.0 indicating analysis confidence",
    "entities": {{
        "order_id": "extracted order number if mentioned",
        "product_name": "product mentioned if any",
        "amount": "monetary amount if mentioned",
        "date": "date mentioned if any",
        "customer_type": "new, returning, premium, etc.",
        "requested_info": "email, phone, address, orders, name, status, etc. (if asking for account info)"
    }},
    "escalation_indicators": [
        "list of reasons this might need escalation: legal_threat, high_value_customer, technical_bug_report, sentiment_very_negative, billing_dispute_high_amount, data_privacy_concern"
    ],
    "key_phrases": [
        "important phrases or keywords from the message"
    ],
    "summary": "brief summary of the customer's issue or request"
}}

Focus on accuracy and provide specific, actionable insights.
"""
    
    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """Parse the analysis response from the model.
        
        Args:
            response: Raw response from the model
            
        Returns:
            Parsed analysis data
        """
        try:
            # Extract JSON from response
            analysis = self._extract_json_from_response(response)
            
            # Validate and clean the analysis
            return self._validate_analysis(analysis)
            
        except Exception as e:
            self.logger.error("Error parsing analysis response", error=str(e))
            # Return default analysis
            return {
                "intent": "general_inquiry",
                "sentiment": "neutral",
                "urgency_score": 0.5,
                "confidence_score": 0.3,
                "entities": {},
                "escalation_indicators": [],
                "key_phrases": [],
                "summary": "Unable to analyze message"
            }
    
    def _validate_analysis(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean the analysis data.
        
        Args:
            analysis: Raw analysis data
            
        Returns:
            Validated analysis data
        """
        # Ensure required fields exist with defaults
        validated = {
            "intent": analysis.get("intent", "general_inquiry"),
            "sentiment": analysis.get("sentiment", "neutral"),
            "urgency_score": max(0.0, min(1.0, analysis.get("urgency_score", 0.5))),
            "confidence_score": max(0.0, min(1.0, analysis.get("confidence_score", 0.8))),
            "entities": analysis.get("entities", {}),
            "escalation_indicators": analysis.get("escalation_indicators", []),
            "key_phrases": analysis.get("key_phrases", []),
            "summary": analysis.get("summary", "")
        }
        
        # Validate sentiment values
        valid_sentiments = ["very_positive", "positive", "neutral", "negative", "very_negative"]
        if validated["sentiment"] not in valid_sentiments:
            validated["sentiment"] = "neutral"
        
        return validated
    
    def _determine_priority(self, analysis: Dict[str, Any]) -> Priority:
        """Determine priority based on analysis results.
        
        Args:
            analysis: Analysis results
            
        Returns:
            Determined priority level
        """
        intent = analysis.get("intent", "general_inquiry")
        sentiment = analysis.get("sentiment", "neutral")
        urgency_score = analysis.get("urgency_score", 0.5)
        escalation_indicators = analysis.get("escalation_indicators", [])
        
        # Check for urgent escalation indicators
        if any(indicator in escalation_indicators for indicator in ESCALATION_TRIGGERS):
            return Priority.URGENT
        
        # High urgency score
        if urgency_score >= 0.8:
            return Priority.HIGH
        
        # Very negative sentiment
        if sentiment == "very_negative":
            return Priority.HIGH
        
        # Use intent-based mapping
        if intent in PRIORITY_MAPPING:
            return PRIORITY_MAPPING[intent]
        
        # Default based on urgency score
        if urgency_score >= 0.6:
            return Priority.MEDIUM
        else:
            return Priority.LOW
    
    def _check_escalation_flags(self, analysis: Dict[str, Any]) -> List[str]:
        """Check for escalation flags based on analysis.
        
        Args:
            analysis: Analysis results
            
        Returns:
            List of escalation flags
        """
        flags = []
        
        escalation_indicators = analysis.get("escalation_indicators", [])
        sentiment = analysis.get("sentiment", "neutral")
        urgency_score = analysis.get("urgency_score", 0.5)
        
        # Add escalation indicators from analysis
        for indicator in escalation_indicators:
            if indicator in ESCALATION_TRIGGERS:
                flags.append(indicator)
        
        # Add sentiment-based flags
        if sentiment == "very_negative":
            flags.append("sentiment_very_negative")
        
        # Add urgency-based flags
        if urgency_score >= 0.9:
            flags.append("high_urgency")
        
        return list(set(flags))  # Remove duplicates
