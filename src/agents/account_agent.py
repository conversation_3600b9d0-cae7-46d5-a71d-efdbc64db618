"""Account Agent for handling customer account information requests."""

import json
import time
from typing import Dict, Any, Optional

from .base_agent import BaseAgent
from ..models import AgentContext
from ..services.customer_database import customer_db


def format_currency_for_speech(amount: float) -> str:
    """Convert currency amount to natural speech format.

    Examples:
    1250.75 -> "1 thousand 250 Rands 75 cents"
    89.99 -> "89 Rands 99 cents"
    1500.00 -> "1 thousand 500 Rands"
    25000.50 -> "25 thousand Rands 50 cents"
    """
    if amount == 0:
        return "zero Rands"

    # Split into Rands and cents
    rands = int(amount)
    cents = int(round((amount - rands) * 100))

    # Convert to natural speech format
    rands_text = format_number_naturally(rands)

    # Build the speech string
    if cents > 0:
        if cents < 10:
            cents_text = f"0{cents}"  # "05" for 5 cents
        else:
            cents_text = str(cents)
        return f"{rands_text} Rands {cents_text} cents"
    else:
        return f"{rands_text} Rands"


def format_number_naturally(num: int) -> str:
    """Convert number to natural speech format for currency."""
    if num == 0:
        return "zero"

    # Handle thousands (simplified for natural speech)
    if num >= 1000:
        thousands = num // 1000
        remainder = num % 1000

        if remainder == 0:
            return f"{thousands} thousand"
        elif remainder < 100:
            return f"{thousands} thousand {remainder}"
        else:
            return f"{thousands} thousand {remainder}"

    # For numbers under 1000, just use the number
    return str(num)


class AccountAgent(BaseAgent):
    """Agent responsible for handling customer account information requests."""

    def __init__(self, config=None):
        """Initialize the Account Agent."""
        super().__init__(
            agent_name="account",
            model_name="gemini-1.5-flash"
        )
        # Store config for ADK compatibility
        self.config = config
    
    async def process(self, context: AgentContext) -> AgentContext:
        """Handle account-related requests.
        
        Args:
            context: Agent context with customer message and intake results
            
        Returns:
            Context updated with account information
        """
        start_time = time.time()
        
        # Check if this is an account-related request
        if not self._is_account_request(context):
            self.logger.info("Not an account request, skipping account agent")
            return context
        
        # Get customer information
        customer_info = await customer_db.get_customer_info(context.customer_message.customer_id)
        
        if not customer_info:
            # Handle case where customer is not found
            context.account_info = {
                "found": False,
                "message": "I'm sorry, I couldn't find your account information. Please verify your customer ID or contact support for assistance."
            }
        else:
            # Process the specific account request
            account_response = await self._process_account_request(context, customer_info)
            context.account_info = account_response
        
        processing_time = time.time() - start_time
        
        self.logger.info(
            "Account processing completed",
            customer_id=context.customer_message.customer_id,
            found=context.account_info.get("found", False),
            processing_time=processing_time
        )
        
        return context
    
    def _is_account_request(self, context: AgentContext) -> bool:
        """Check if the request is account-related.

        Args:
            context: Agent context

        Returns:
            True if this is an account request
        """
        # Check for account-related keywords in the message (primary detection)
        message_lower = context.customer_message.content.lower()
        account_keywords = [
            "my email", "what's my email", "whats my email", "email address",
            "my account", "account info", "account information", "my profile",
            "my details", "personal information", "contact info", "my phone",
            "my address", "billing address", "shipping address", "my orders",
            "order history", "recent orders", "account status", "membership", "my name",
            "what order", "my order", "previous order", "last order", "orders",
            "purchase", "purchases", "bought", "what did i buy", "my purchases"
        ]

        keyword_match = any(keyword in message_lower for keyword in account_keywords)

        # Check for greetings (should trigger proactive account assistance)
        greeting_keywords = ["hello", "hi", "hey", "good morning", "good afternoon", "good evening"]
        greeting_match = any(keyword in message_lower for keyword in greeting_keywords)

        # Also check intent if available
        intent_match = False
        if context.intake_result:
            account_intents = ["account_access", "account_info", "profile_inquiry"]
            intent_match = context.intake_result.intent in account_intents

        return keyword_match or intent_match or greeting_match
    
    async def _process_account_request(self, context: AgentContext, customer_info: Dict[str, Any]) -> Dict[str, Any]:
        """Process the specific account request.
        
        Args:
            context: Agent context
            customer_info: Customer information from database
            
        Returns:
            Account response information
        """
        message_lower = context.customer_message.content.lower()
        
        # Determine what specific information is being requested (check specific requests first)
        if any(keyword in message_lower for keyword in ["email", "email address"]):
            return await self._handle_email_request(customer_info, context)
        elif any(keyword in message_lower for keyword in ["hello", "hi", "hey", "good morning", "good afternoon", "good evening"]) and not any(keyword in message_lower for keyword in ["email", "order", "phone", "address", "name", "status"]):
            return await self._handle_greeting_request(customer_info, context)
        elif any(keyword in message_lower for keyword in ["phone", "phone number"]):
            return await self._handle_phone_request(customer_info, context)
        elif any(keyword in message_lower for keyword in ["address", "billing address", "shipping address"]):
            return await self._handle_address_request(customer_info, context)
        elif any(keyword in message_lower for keyword in ["order", "orders", "order history", "recent orders", "purchase", "purchases", "bought", "what did i buy", "previous"]):
            return await self._handle_orders_request(customer_info, context)
        elif any(keyword in message_lower for keyword in ["name", "my name", "full name"]):
            return await self._handle_name_request(customer_info, context)
        elif any(keyword in message_lower for keyword in ["account status", "membership", "tier"]):
            return await self._handle_status_request(customer_info, context)
        else:
            # General account information request with proactive suggestions
            return await self._handle_general_account_request(customer_info, context)
    
    async def _handle_greeting_request(self, customer_info: Dict[str, Any], context: AgentContext) -> Dict[str, Any]:
        """Handle greeting with proactive account assistance."""
        first_name = customer_info.get("first_name", "")
        customer_type = customer_info.get("customer_type", "regular")
        total_orders = customer_info.get("total_orders", 0)
        total_spent = customer_info.get("total_spent", 0.0)

        # Create personalized greeting with proactive assistance
        greeting = f"Hello {first_name}!" if first_name else "Hello!"

        if customer_type.lower() == "vip":
            message = f"{greeting} Great to have you back! As a VIP customer with {total_orders} orders, you have priority access to everything. "
        elif customer_type.lower() == "premium":
            message = f"{greeting} Welcome back! As a Premium customer, I'm here to provide you with enhanced support. "
        elif total_orders >= 5:
            message = f"{greeting} Nice to see a returning customer! With {total_orders} orders, you're becoming one of our regulars. "
        elif total_orders > 0:
            message = f"{greeting} Welcome back! I see you've shopped with us before. "
        else:
            message = f"{greeting} Welcome! I'm Angela from customer support, and I'm here to help you with anything you need. "

        # Add proactive suggestions
        if total_orders > 0:
            message += f"I can help you check your recent orders, track a shipment, view your account details, or assist with a new purchase. What would you like to know about?"
        else:
            message += "I can help you with account information, placing an order, or answering any questions about our services. What can I assist you with today?"

        return {
            "found": True,
            "type": "greeting",
            "data": {"customer_type": customer_type, "total_orders": total_orders, "first_name": first_name},
            "message": message
        }

    async def _handle_email_request(self, customer_info: Dict[str, Any], context: AgentContext) -> Dict[str, Any]:
        """Handle email address request with proactive suggestions."""
        email = customer_info.get("email", "Not available")
        customer_type = customer_info.get("customer_type", "regular")
        total_orders = customer_info.get("total_orders", 0)

        # Create proactive, conversational response
        base_message = f"Your email address on file is: {email}"

        # Add proactive suggestions based on customer data with natural currency phrasing
        total_spent = customer_info.get("total_spent", 0.0)
        if total_orders > 10:
            if total_spent > 0:
                spent_text = format_currency_for_speech(total_spent)
                base_message += f". I can see you're one of our valued customers with {total_orders} orders totaling {spent_text}! Would you like me to show you your recent purchases or account status?"
            else:
                base_message += f". I can see you're one of our valued customers with {total_orders} orders! Would you like me to show you your recent purchases or account status?"
        elif total_orders > 0:
            if total_spent > 0:
                spent_text = format_currency_for_speech(total_spent)
                base_message += f". You have {total_orders} orders with us, spending {spent_text} total. Would you like to see your order history or check on a recent purchase?"
            else:
                base_message += f". You have {total_orders} orders with us. Would you like to see your order history or check on a recent purchase?"
        else:
            base_message += ". I notice this might be a new account. Would you like help with placing your first order or learning about our services?"

        return {
            "found": True,
            "type": "email",
            "data": {"email": email, "total_orders": total_orders, "customer_type": customer_type},
            "message": base_message
        }
    
    async def _handle_phone_request(self, customer_info: Dict[str, Any]) -> Dict[str, Any]:
        """Handle phone number request."""
        phone = customer_info.get("phone", "Not available")
        return {
            "found": True,
            "type": "phone",
            "data": {"phone": phone},
            "message": f"Your phone number on file is: {phone}"
        }
    
    async def _handle_address_request(self, customer_info: Dict[str, Any]) -> Dict[str, Any]:
        """Handle address request."""
        billing_address = customer_info.get("billing_address", {})
        if billing_address:
            address_str = f"{billing_address.get('street', '')}, {billing_address.get('city', '')}, {billing_address.get('state', '')} {billing_address.get('zip', '')}"
            return {
                "found": True,
                "type": "address",
                "data": {"billing_address": billing_address},
                "message": f"Your billing address on file is: {address_str}"
            }
        else:
            return {
                "found": True,
                "type": "address",
                "data": {},
                "message": "No billing address found on your account."
            }
    
    async def _handle_orders_request(self, customer_info: Dict[str, Any], context: AgentContext) -> Dict[str, Any]:
        """Handle order history request with detailed, conversational response."""
        recent_orders = customer_info.get("recent_orders", [])
        total_orders = customer_info.get("total_orders", 0)
        total_spent = customer_info.get("total_spent", 0.0)
        customer_type = customer_info.get("customer_type", "regular")

        if recent_orders and total_orders > 0:
            # Create detailed order information
            order_details = []
            for i, order in enumerate(recent_orders[:3]):
                order_amount = format_currency_for_speech(order['total'])
                status_text = order['status'].title()
                order_details.append(f"Order {order['order_id']} for {order_amount} is {status_text}")

            # Create conversational, human-like response
            total_spent_text = format_currency_for_speech(total_spent)

            if customer_type == "premium" or customer_type == "vip":
                message = f"Great to see you again! As a {customer_type} customer, you've placed {total_orders} orders with us, spending {total_spent_text} in total. "
            else:
                message = f"You have {total_orders} orders with us, spending {total_spent_text} total. "

            # Add recent orders in a conversational way
            message += "Let me tell you about your recent orders. "
            for i, order in enumerate(recent_orders[:3]):
                order_amount = format_currency_for_speech(order['total'])
                status_text = order['status']

                if status_text == "processing":
                    message += f"Your order {order['order_id']} for {order_amount} is currently being processed. "
                elif status_text == "delivered":
                    message += f"Your order {order['order_id']} for {order_amount} has been delivered. "
                elif status_text == "shipped":
                    message += f"Your order {order['order_id']} for {order_amount} is on its way to you. "
                else:
                    message += f"Your order {order['order_id']} for {order_amount} is {status_text}. "

            # Add proactive suggestions
            if any(order['status'] == 'processing' for order in recent_orders):
                message += "I notice you have an order currently processing. Would you like tracking information or an estimated delivery date?"
            elif total_orders >= 5:
                message += "You're becoming a regular customer! Would you like to know about our loyalty program or premium benefits?"
            else:
                message += "Is there anything specific about these orders I can help you with, like tracking or returns?"

        else:
            # Handle customers with no orders
            message = f"I don't see any orders on your account yet. Would you like help browsing our products or assistance with placing your first order? I'm here to make your shopping experience smooth and enjoyable!"

        return {
            "found": True,
            "type": "orders",
            "data": {
                "recent_orders": recent_orders,
                "total_orders": total_orders,
                "total_spent": total_spent,
                "customer_type": customer_type
            },
            "message": message
        }
    
    async def _handle_name_request(self, customer_info: Dict[str, Any]) -> Dict[str, Any]:
        """Handle name request."""
        first_name = customer_info.get("first_name", "")
        last_name = customer_info.get("last_name", "")
        full_name = f"{first_name} {last_name}".strip()
        
        return {
            "found": True,
            "type": "name",
            "data": {"first_name": first_name, "last_name": last_name, "full_name": full_name},
            "message": f"Your name on file is: {full_name}"
        }
    
    async def _handle_status_request(self, customer_info: Dict[str, Any]) -> Dict[str, Any]:
        """Handle account status request."""
        customer_type = customer_info.get("customer_type", "regular")
        support_tier = customer_info.get("support_tier", "standard")
        account_status = customer_info.get("account_status", "active")
        
        return {
            "found": True,
            "type": "status",
            "data": {
                "customer_type": customer_type,
                "support_tier": support_tier,
                "account_status": account_status
            },
            "message": f"Your account status: {account_status.title()}, Customer type: {customer_type.title()}, Support tier: {support_tier.title()}"
        }
    
    async def _handle_general_account_request(self, customer_info: Dict[str, Any], context: AgentContext) -> Dict[str, Any]:
        """Handle general account information request with proactive assistance."""
        summary = await customer_db.get_customer_profile_summary(customer_info["customer_id"])

        if summary:
            # Create personalized, conversational response
            name = summary['name']
            customer_type = summary['customer_type'].title()
            total_orders = summary['total_orders']
            total_spent = customer_info.get('total_spent', 0.0)

            message = f"Hi {name}! Here's your account overview: You're a {customer_type} customer with {total_orders} orders"

            if total_spent > 0:
                spent_text = format_currency_for_speech(total_spent)
                message += f" totaling {spent_text}"

            message += f". Your account is {summary['account_status'].title()} with {summary['support_tier'].title()} support. "

            # Add proactive suggestions based on customer data
            if customer_type.lower() == "vip":
                message += "As a VIP customer, you have access to priority support and exclusive offers. Would you like to see your recent orders, update your preferences, or learn about new VIP benefits?"
            elif customer_type.lower() == "premium":
                message += "As a Premium customer, you enjoy enhanced support and special perks. Can I help you with your recent orders, account settings, or show you premium-exclusive features?"
            elif total_orders >= 5:
                message += "You're a valued returning customer! Would you like to see your order history, check your loyalty points, or learn about upgrading to Premium status?"
            elif total_orders > 0:
                message += "Thanks for being a customer! Can I help you track a recent order, update your account information, or assist with a new purchase?"
            else:
                message += "Welcome! I'm here to help you get started. Would you like assistance with placing your first order, setting up your preferences, or learning about our services?"
        else:
            message = "I'm having trouble accessing your account details right now, but I'm still here to help! What specific information or assistance can I provide for you today?"

        return {
            "found": True,
            "type": "general",
            "data": summary or {},
            "message": message
        }
