"""Resolution Agent for generating customer support responses."""

import time
from typing import Dict, Any, List

from .base_agent import BaseAgent
from ..models import Agent<PERSON>ontex<PERSON>, ResolutionResult
from ..config import settings, Priority


class ResolutionAgent(BaseAgent):
    """Agent responsible for generating customer support responses and action plans."""
    
    def __init__(self):
        """Initialize the Resolution Agent."""
        super().__init__(
            agent_name="resolution",
            model_name=settings.vertex_ai_model_resolution
        )
        
        self.max_response_length = settings.resolution_agent_max_length
        self.escalation_threshold = 0.8
    
    async def process(self, context: AgentContext) -> AgentContext:
        """Generate response draft and action plan based on analysis and knowledge.
        
        Args:
            context: Agent context with intake and knowledge results
            
        Returns:
            Context updated with resolution results
        """
        start_time = time.time()
        
        if not context.intake_result or not context.knowledge_result:
            raise ValueError("Intake and knowledge results required for resolution")
        
        # Generate response draft
        response_draft = await self._generate_response_draft(context)
        
        # Create action plan
        action_plan = await self._create_action_plan(context)
        
        # Determine if escalation is needed
        escalation_needed, escalation_reason = self._check_escalation_needed(context)
        
        # Determine response type
        response_type = self._determine_response_type(context)
        
        # Generate follow-up suggestion
        followup_suggestion = await self._generate_followup_suggestion(context)
        
        # Calculate confidence score
        confidence_score = self._calculate_confidence_score(context)
        
        # Create resolution result
        resolution_result = ResolutionResult(
            message_id=context.message_id,
            response_draft=response_draft,
            action_plan=action_plan,
            escalation_recommended=escalation_needed,
            escalation_reason=escalation_reason,
            confidence_score=confidence_score,
            response_type=response_type,
            suggested_followup=followup_suggestion,
            processing_time=time.time() - start_time
        )
        
        # Update context
        context.resolution_result = resolution_result
        
        self.logger.info(
            "Resolution generation completed",
            message_id=context.message_id,
            response_type=response_type,
            escalation_needed=escalation_needed,
            confidence_score=confidence_score
        )
        
        return context
    
    async def _generate_response_draft(self, context: AgentContext) -> str:
        """Generate the initial response draft.
        
        Args:
            context: Agent context with all previous results
            
        Returns:
            Generated response draft
        """
        intake = context.intake_result
        knowledge = context.knowledge_result
        customer_message = context.customer_message.content
        
        # Build context from knowledge results
        knowledge_context = self._build_knowledge_context(knowledge)
        
        prompt = f"""
You are a professional customer service representative. Generate a helpful, empathetic response to this customer message.

Customer Message: "{customer_message}"

Customer Analysis:
- Intent: {intake.intent}
- Sentiment: {intake.sentiment.value}
- Priority: {intake.priority.value}
- Urgency Score: {intake.urgency_score}
- Key Entities: {intake.extracted_entities}

Relevant Knowledge:
{knowledge_context}

Guidelines:
1. Be empathetic and acknowledge the customer's concern
2. Provide a clear, helpful response based on available knowledge
3. Use a professional but friendly tone
4. Keep response under {self.max_response_length} characters
5. If you cannot fully resolve the issue, explain next steps
6. Match the urgency level - be more direct for high priority issues

Generate a complete response that addresses the customer's specific concern.
"""
        
        response = await self._generate_response(
            prompt,
            temperature=0.4,
            max_tokens=800
        )
        
        # Clean and validate response
        return self._clean_response(response)
    
    def _build_knowledge_context(self, knowledge: 'KnowledgeResult') -> str:
        """Build context string from knowledge search results.
        
        Args:
            knowledge: Knowledge search results
            
        Returns:
            Formatted knowledge context
        """
        context_parts = []
        
        # Add relevant documents
        if knowledge.relevant_documents:
            context_parts.append("Relevant Documentation:")
            for doc in knowledge.relevant_documents[:3]:  # Top 3
                context_parts.append(f"- {doc['title']}: {doc['content'][:200]}...")
        
        # Add FAQ matches
        if knowledge.faq_matches:
            context_parts.append("\nRelevant FAQs:")
            for faq in knowledge.faq_matches[:2]:  # Top 2
                context_parts.append(f"- Q: {faq['question']}")
                context_parts.append(f"  A: {faq['answer']}")
        
        # Add similar ticket resolutions
        if knowledge.similar_tickets:
            context_parts.append("\nSimilar Past Resolutions:")
            for ticket in knowledge.similar_tickets[:2]:  # Top 2
                context_parts.append(f"- {ticket['final_response'][:150]}...")
        
        return "\n".join(context_parts) if context_parts else "No specific knowledge found."
    
    async def _create_action_plan(self, context: AgentContext) -> List[str]:
        """Create an action plan based on the customer issue.
        
        Args:
            context: Agent context
            
        Returns:
            List of action items
        """
        intake = context.intake_result
        
        prompt = f"""
Create a specific action plan for resolving this customer support case:

Intent: {intake.intent}
Priority: {intake.priority.value}
Escalation Flags: {intake.escalation_flags}

Generate 3-5 specific action items as a JSON array:
["action1", "action2", "action3", ...]

Focus on concrete, actionable steps that would resolve the customer's issue.
"""
        
        response = await self._generate_response(prompt, temperature=0.3)
        
        try:
            actions = self._extract_json_from_response(response)
            if isinstance(actions, list):
                return actions[:5]  # Limit to 5 actions
        except:
            pass
        
        # Fallback action plan based on intent
        return self._generate_fallback_action_plan(intake.intent, intake.priority)
    
    def _generate_fallback_action_plan(self, intent: str, priority: Priority) -> List[str]:
        """Generate fallback action plan when AI generation fails.
        
        Args:
            intent: Customer intent
            priority: Issue priority
            
        Returns:
            List of fallback actions
        """
        action_templates = {
            "order_issue": [
                "Verify order details in system",
                "Check order status and tracking",
                "Provide resolution or compensation",
                "Send confirmation email to customer"
            ],
            "billing_dispute": [
                "Review billing records",
                "Investigate charge details",
                "Process refund if warranted",
                "Update customer account"
            ],
            "technical_support": [
                "Gather technical details",
                "Provide troubleshooting steps",
                "Escalate to technical team if needed",
                "Follow up on resolution"
            ],
            "refund_request": [
                "Verify refund eligibility",
                "Process refund request",
                "Send refund confirmation",
                "Update order status"
            ]
        }
        
        actions = action_templates.get(intent, [
            "Acknowledge customer concern",
            "Investigate issue details",
            "Provide appropriate resolution",
            "Follow up with customer"
        ])
        
        # Add priority-specific actions
        if priority in [Priority.HIGH, Priority.URGENT]:
            actions.insert(0, "Prioritize for immediate attention")
        
        return actions
    
    def _check_escalation_needed(self, context: AgentContext) -> tuple[bool, str]:
        """Check if the issue needs escalation to human agents.
        
        Args:
            context: Agent context
            
        Returns:
            Tuple of (escalation_needed, reason)
        """
        intake = context.intake_result
        
        # Check escalation flags
        if intake.escalation_flags:
            return True, f"Escalation flags: {', '.join(intake.escalation_flags)}"
        
        # Check priority level
        if intake.priority == Priority.URGENT:
            return True, "Urgent priority level"
        
        # Check sentiment and urgency combination
        if intake.sentiment.value == "very_negative" and intake.urgency_score >= 0.8:
            return True, "Very negative sentiment with high urgency"
        
        # Check confidence score
        if intake.confidence_score < 0.6:
            return True, "Low confidence in analysis"
        
        # Check if no relevant knowledge found
        knowledge = context.knowledge_result
        if knowledge.total_results_found == 0:
            return True, "No relevant knowledge found"
        
        return False, None
    
    def _determine_response_type(self, context: AgentContext) -> str:
        """Determine the type of response needed.
        
        Args:
            context: Agent context
            
        Returns:
            Response type classification
        """
        intake = context.intake_result
        
        if intake.escalation_flags:
            return "escalation"
        elif intake.priority == Priority.URGENT:
            return "urgent"
        elif intake.sentiment.value in ["negative", "very_negative"]:
            return "complaint_resolution"
        elif intake.intent in ["compliment", "positive_feedback"]:
            return "appreciation"
        else:
            return "standard"
    
    async def _generate_followup_suggestion(self, context: AgentContext) -> str:
        """Generate follow-up suggestion.
        
        Args:
            context: Agent context
            
        Returns:
            Follow-up suggestion or None
        """
        intake = context.intake_result
        
        if intake.priority in [Priority.HIGH, Priority.URGENT]:
            return "Follow up within 24 hours to ensure resolution"
        elif intake.sentiment.value in ["negative", "very_negative"]:
            return "Follow up within 48 hours to confirm satisfaction"
        else:
            return "Follow up in 3-5 days if no further contact"
    
    def _calculate_confidence_score(self, context: AgentContext) -> float:
        """Calculate confidence score for the resolution.
        
        Args:
            context: Agent context
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        intake = context.intake_result
        knowledge = context.knowledge_result
        
        # Base confidence from intake analysis
        confidence = intake.confidence_score
        
        # Adjust based on knowledge availability
        if knowledge.total_results_found > 0:
            # Boost confidence if relevant knowledge found
            avg_relevance = sum(knowledge.relevance_scores) / len(knowledge.relevance_scores) if knowledge.relevance_scores else 0
            confidence = min(1.0, confidence + (avg_relevance * 0.2))
        else:
            # Reduce confidence if no knowledge found
            confidence *= 0.7
        
        # Adjust based on escalation flags
        if intake.escalation_flags:
            confidence *= 0.8
        
        return max(0.0, min(1.0, confidence))
    
    def _clean_response(self, response: str) -> str:
        """Clean and validate the generated response.
        
        Args:
            response: Raw generated response
            
        Returns:
            Cleaned response
        """
        # Remove any JSON formatting if present
        if response.startswith('{') or response.startswith('['):
            # Try to extract text from JSON
            try:
                import json
                data = json.loads(response)
                if isinstance(data, dict) and 'response' in data:
                    response = data['response']
                elif isinstance(data, str):
                    response = data
            except:
                pass
        
        # Clean up the response
        response = response.strip()
        
        # Ensure it's not too long
        if len(response) > self.max_response_length:
            # Truncate at last complete sentence
            sentences = response.split('.')
            truncated = ""
            for sentence in sentences:
                if len(truncated + sentence + '.') <= self.max_response_length:
                    truncated += sentence + '.'
                else:
                    break
            response = truncated.strip()
        
        # Ensure minimum length
        if len(response) < 20:
            response = "Thank you for contacting us. We're reviewing your request and will provide a detailed response shortly."
        
        return response
