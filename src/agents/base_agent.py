"""Base agent class for the Smart Customer Support Orchestrator."""

import time
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    import google.generativeai as genai
    VERTEX_AI_AVAILABLE = True
    print("✅ Using Google Generative AI (Gemini) for real AI responses")
except ImportError:
    try:
        import vertexai
        from vertexai.generative_models import GenerativeModel
        VERTEX_AI_AVAILABLE = True
        print("✅ Using Vertex AI for real AI responses")
    except ImportError:
        VERTEX_AI_AVAILABLE = False
        print("⚠️  Warning: AI libraries not available. Using mock responses for demo.")

import structlog

from ..config import settings
from ..models import AgentContext


class BaseAgent(ABC):
    """Abstract base class for all support agents."""
    
    def __init__(self, agent_name: str, model_name: Optional[str] = None):
        """Initialize the base agent.
        
        Args:
            agent_name: Name of the agent (e.g., "intake", "knowledge")
            model_name: Vertex AI model to use (defaults to gemini-1.5-pro)
        """
        self.agent_name = agent_name
        self.model_name = model_name or "gemini-1.5-pro"
        self.logger = structlog.get_logger().bind(agent=agent_name)
        
        # Initialize AI model
        if VERTEX_AI_AVAILABLE:
            try:
                # Try Google Generative AI first (simpler setup)
                if 'google.generativeai' in globals():
                    # For now, we'll use mock responses since we need API key setup
                    self.model = None
                    self.use_genai = True
                else:
                    # Use Vertex AI
                    vertexai.init(
                        project=settings.google_cloud_project,
                        location=settings.vertex_ai_location
                    )
                    self.model = GenerativeModel(self.model_name)
                    self.use_genai = False
            except Exception as e:
                self.logger.warning(f"Failed to initialize AI model: {e}")
                self.model = None
                self.use_genai = False
        else:
            self.model = None
            self.use_genai = False
        
        # Agent metrics
        self.processed_count = 0
        self.total_processing_time = 0.0
        self.error_count = 0
        self.last_processed = None
    
    @abstractmethod
    async def process(self, context: AgentContext) -> AgentContext:
        """Process the customer support context.
        
        Args:
            context: The current agent context with customer message and previous results
            
        Returns:
            Updated context with this agent's results
        """
        pass
    
    async def _generate_response(
        self,
        prompt: str,
        temperature: float = 0.3,
        max_tokens: int = 1000
    ) -> str:
        """Generate a response using Vertex AI or mock response.

        Args:
            prompt: The prompt to send to the model
            temperature: Sampling temperature (0.0 to 1.0)
            max_tokens: Maximum tokens in response

        Returns:
            Generated response text
        """
        # Try Google Generative AI first
        genai_response = self._try_google_genai(prompt, temperature, max_tokens)
        if genai_response:
            return genai_response

        # Try Vertex AI
        if not VERTEX_AI_AVAILABLE or self.model is None:
            # Return mock response for demo purposes
            return self._generate_mock_response(prompt)

        try:
            # Configure generation parameters
            generation_config = {
                "temperature": temperature,
                "max_output_tokens": max_tokens,
                "top_p": 0.8,
                "top_k": 40
            }

            # Generate response
            response = await self.model.generate_content_async(
                prompt,
                generation_config=generation_config
            )

            return response.text

        except Exception as e:
            self.logger.error("Error generating response", error=str(e))
            # Fallback to mock response
            return self._generate_mock_response(prompt)

    def _try_google_genai(self, prompt: str, temperature: float, max_tokens: int) -> str:
        """Try to use Google Generative AI with multiple API keys."""
        try:
            import google.generativeai as genai
            from ..utils.api_key_manager import api_key_manager

            # Get the next available API key
            api_key = api_key_manager.get_next_key()
            if not api_key:
                return None

            genai.configure(api_key=api_key)
            model = genai.GenerativeModel('gemini-1.5-flash')

            generation_config = genai.types.GenerationConfig(
                temperature=temperature,
                max_output_tokens=max_tokens,
                top_p=0.8,
                top_k=40
            )

            response = model.generate_content(
                prompt,
                generation_config=generation_config
            )

            # Mark success
            api_key_manager.mark_success(api_key)
            self.logger.info("Using Google Generative AI for response", key_suffix=api_key[-8:])
            return response.text

        except Exception as e:
            error_str = str(e)

            # Check if it's a rate limit error
            if "429" in error_str or "quota" in error_str.lower() or "rate" in error_str.lower():
                # Extract retry delay if available
                retry_after = 60  # Default
                if "retry_delay" in error_str:
                    try:
                        import re
                        match = re.search(r'seconds: (\d+)', error_str)
                        if match:
                            retry_after = int(match.group(1))
                    except:
                        pass

                api_key_manager.mark_rate_limited(api_key, retry_after)
                self.logger.warning(f"API key rate limited, trying next key", key_suffix=api_key[-8:])

                # Try with next key
                return self._try_google_genai_fallback(prompt, temperature, max_tokens)
            else:
                api_key_manager.mark_error(api_key)
                self.logger.debug(f"Google Generative AI error: {e}")
                return None

    def _try_google_genai_fallback(self, prompt: str, temperature: float, max_tokens: int) -> str:
        """Fallback attempt with different API key."""
        try:
            import google.generativeai as genai
            from ..utils.api_key_manager import api_key_manager

            # Get the best performing key
            api_key = api_key_manager.get_best_key()
            if not api_key:
                return None

            genai.configure(api_key=api_key)
            model = genai.GenerativeModel('gemini-1.5-flash')

            generation_config = genai.types.GenerationConfig(
                temperature=temperature,
                max_output_tokens=max_tokens,
                top_p=0.8,
                top_k=40
            )

            response = model.generate_content(
                prompt,
                generation_config=generation_config
            )

            api_key_manager.mark_success(api_key)
            self.logger.info("Using fallback Google Generative AI", key_suffix=api_key[-8:])
            return response.text

        except Exception as e:
            self.logger.debug(f"Fallback Google Generative AI failed: {e}")
            return None

    def _generate_mock_response(self, prompt: str) -> str:
        """Generate a mock response for demo purposes when Vertex AI is not available."""
        if "intent" in prompt.lower() and "sentiment" in prompt.lower():
            # Mock intake agent response
            return '''
            {
                "intent": "general_inquiry",
                "sentiment": "neutral",
                "urgency_score": 0.5,
                "confidence_score": 0.8,
                "entities": {},
                "escalation_indicators": [],
                "key_phrases": ["help", "question"],
                "summary": "Customer has a general inquiry"
            }
            '''
        elif "search queries" in prompt.lower():
            # Mock knowledge agent response
            return '["customer support", "help", "assistance"]'
        elif "quality" in prompt.lower() and "accuracy" in prompt.lower():
            # Mock quality agent response
            return '''
            {
                "accuracy": 0.85,
                "helpfulness": 0.80,
                "politeness": 0.90,
                "clarity": 0.85,
                "completeness": 0.75,
                "empathy": 0.80
            }
            '''
        else:
            # Mock resolution agent response
            return "Thank you for contacting us. We understand your concern and are here to help. Our team will review your request and provide a detailed response shortly."
    
    def _update_metrics(self, processing_time: float, success: bool = True):
        """Update agent performance metrics.
        
        Args:
            processing_time: Time taken to process in seconds
            success: Whether the processing was successful
        """
        self.processed_count += 1
        self.total_processing_time += processing_time
        self.last_processed = datetime.utcnow()
        
        if not success:
            self.error_count += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current agent performance metrics.
        
        Returns:
            Dictionary containing agent metrics
        """
        avg_processing_time = (
            self.total_processing_time / self.processed_count 
            if self.processed_count > 0 else 0.0
        )
        
        success_rate = (
            (self.processed_count - self.error_count) / self.processed_count
            if self.processed_count > 0 else 0.0
        )
        
        return {
            "agent_name": self.agent_name,
            "total_processed": self.processed_count,
            "average_processing_time": avg_processing_time,
            "success_rate": success_rate,
            "error_count": self.error_count,
            "last_processed": self.last_processed
        }
    
    async def _execute_with_timing(self, context: AgentContext) -> AgentContext:
        """Execute agent processing with timing and error handling.
        
        Args:
            context: Agent context to process
            
        Returns:
            Updated context with results
        """
        start_time = time.time()
        success = True
        
        try:
            self.logger.info("Starting agent processing", message_id=context.message_id)
            
            # Update context with current agent
            context.current_agent = self.agent_name
            context.agent_history.append(self.agent_name)
            
            # Process the context
            result_context = await self.process(context)
            
            processing_time = time.time() - start_time
            result_context.total_processing_time += processing_time
            
            self.logger.info(
                "Agent processing completed",
                message_id=context.message_id,
                processing_time=processing_time
            )
            
            return result_context
            
        except Exception as e:
            success = False
            processing_time = time.time() - start_time
            
            self.logger.error(
                "Agent processing failed",
                message_id=context.message_id,
                error=str(e),
                processing_time=processing_time
            )
            
            raise
        
        finally:
            self._update_metrics(processing_time, success)
    
    def _extract_json_from_response(self, response: str) -> Dict[str, Any]:
        """Extract JSON data from model response.
        
        Args:
            response: Raw response text from the model
            
        Returns:
            Parsed JSON data
        """
        import json
        import re
        
        # Try to find JSON in the response
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            try:
                return json.loads(json_match.group())
            except json.JSONDecodeError:
                pass
        
        # If no JSON found, return empty dict
        self.logger.warning("No valid JSON found in response", response=response[:200])
        return {}
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"{self.__class__.__name__}(name={self.agent_name}, model={self.model_name})"
