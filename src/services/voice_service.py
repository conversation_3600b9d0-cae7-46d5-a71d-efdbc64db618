"""Voice Service for speech-to-text and text-to-speech functionality."""

import asyncio
import base64
import io
import json
from typing import Dict, Any, Optional, Union
import structlog

try:
    from google.cloud import texttospeech
    from google.cloud import speech
    GOOGLE_CLOUD_AVAILABLE = True
except ImportError:
    GOOGLE_CLOUD_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from ..config import settings


class VoiceService:
    """Service for handling voice input/output in customer support."""

    def __init__(self, provider: str = "google"):
        """Initialize voice service.

        Args:
            provider: Voice provider ("google" or "openai")
        """
        self.logger = structlog.get_logger(__name__)
        self.provider = provider

        if provider == "google" and GOOGLE_CLOUD_AVAILABLE:
            try:
                # Use your existing Google Cloud project configuration
                from google.oauth2 import service_account
                import os

                # Try to use your existing service account
                credentials_path = os.path.join(os.path.dirname(__file__), '..', 'vertexai.json')
                if os.path.exists(credentials_path):
                    credentials = service_account.Credentials.from_service_account_file(credentials_path)
                    self.tts_client = texttospeech.TextToSpeechClient(credentials=credentials)
                    self.stt_client = speech.SpeechClient(credentials=credentials)
                    self.logger.info("Google Cloud voice services initialized with service account")
                else:
                    # Fallback to default credentials
                    self.tts_client = texttospeech.TextToSpeechClient()
                    self.stt_client = speech.SpeechClient()
                    self.logger.info("Google Cloud voice services initialized with default credentials")

            except Exception as e:
                self.logger.error(f"Failed to initialize Google Cloud voice services: {e}")
                self.provider = "mock"

        elif provider == "openai" and OPENAI_AVAILABLE:
            try:
                # Use your existing OpenAI configuration
                api_key = settings.openai_api_key
                if api_key:
                    self.openai_client = openai.OpenAI(api_key=api_key)
                    self.logger.info("OpenAI voice services initialized")
                else:
                    self.logger.warning("OpenAI API key not found, using mock service")
                    self.provider = "mock"
            except Exception as e:
                self.logger.error(f"Failed to initialize OpenAI voice services: {e}")
                self.provider = "mock"
        else:
            self.logger.warning(f"Voice provider {provider} not available, using mock service")
            self.provider = "mock"
    
    async def text_to_speech(self, text: str, voice_config: Optional[Dict[str, Any]] = None) -> bytes:
        """Convert text to speech audio.
        
        Args:
            text: Text to convert to speech
            voice_config: Voice configuration options
            
        Returns:
            Audio data as bytes
        """
        if self.provider == "google":
            return await self._google_text_to_speech(text, voice_config)
        elif self.provider == "openai":
            return await self._openai_text_to_speech(text, voice_config)
        else:
            return await self._mock_text_to_speech(text)
    
    async def speech_to_text(self, audio_data: bytes, config: Optional[Dict[str, Any]] = None) -> str:
        """Convert speech audio to text.
        
        Args:
            audio_data: Audio data as bytes
            config: Speech recognition configuration
            
        Returns:
            Transcribed text
        """
        if self.provider == "google":
            return await self._google_speech_to_text(audio_data, config)
        elif self.provider == "openai":
            return await self._openai_speech_to_text(audio_data, config)
        else:
            return await self._mock_speech_to_text(audio_data)
    
    async def _google_text_to_speech(self, text: str, voice_config: Optional[Dict[str, Any]] = None) -> bytes:
        """Google Cloud Text-to-Speech implementation."""
        try:
            # Default to high-quality Journey voice
            voice_config = voice_config or {}

            # Journey voices don't support SSML, use plain text
            voice_name = voice_config.get("voice_name", "en-US-Journey-F")

            if "Journey" in voice_name:
                # Use plain text for Journey voices
                synthesis_input = texttospeech.SynthesisInput(text=text)
            else:
                # Use SSML for other voices
                ssml_text = self._enhance_text_with_ssml(text)
                synthesis_input = texttospeech.SynthesisInput(ssml=ssml_text)
            
            # Use Journey voice for most natural sound (like NotebookLM)
            voice = texttospeech.VoiceSelectionParams(
                language_code=voice_config.get("language_code", "en-US"),
                name=voice_config.get("voice_name", "en-US-Journey-F"),  # Female Journey voice
                ssml_gender=texttospeech.SsmlVoiceGender.FEMALE
            )

            # Configure audio output for maximum quality
            audio_config = texttospeech.AudioConfig(
                audio_encoding=texttospeech.AudioEncoding.MP3,
                speaking_rate=voice_config.get("speaking_rate", 0.95),  # Natural rate for human-like quality
                pitch=voice_config.get("pitch", 0.0),
                volume_gain_db=voice_config.get("volume_gain_db", 2.0)  # Slightly louder for clarity
            )
            
            # Perform the text-to-speech request
            response = self.tts_client.synthesize_speech(
                input=synthesis_input,
                voice=voice,
                audio_config=audio_config
            )
            
            self.logger.info("Text-to-speech completed", provider="google", text_length=len(text))
            return response.audio_content
            
        except Exception as e:
            self.logger.error("Google TTS error", error=str(e))
            return await self._mock_text_to_speech(text)
    
    async def _openai_text_to_speech(self, text: str, voice_config: Optional[Dict[str, Any]] = None) -> bytes:
        """OpenAI Text-to-Speech implementation."""
        try:
            voice_config = voice_config or {}
            
            response = self.openai_client.audio.speech.create(
                model="tts-1-hd",  # High quality model
                voice=voice_config.get("voice", "nova"),  # Nova is very natural
                input=text,
                speed=voice_config.get("speed", 1.0)
            )
            
            self.logger.info("Text-to-speech completed", provider="openai", text_length=len(text))
            return response.content
            
        except Exception as e:
            self.logger.error("OpenAI TTS error", error=str(e))
            return await self._mock_text_to_speech(text)
    
    async def _google_speech_to_text(self, audio_data: bytes, config: Optional[Dict[str, Any]] = None) -> str:
        """Google Cloud Speech-to-Text implementation."""
        try:
            config = config or {}
            
            audio = speech.RecognitionAudio(content=audio_data)
            recognition_config = speech.RecognitionConfig(
                encoding=speech.RecognitionConfig.AudioEncoding.WEBM_OPUS,
                sample_rate_hertz=config.get("sample_rate", 48000),
                language_code=config.get("language_code", "en-US"),
                enable_automatic_punctuation=True,
                enable_word_confidence=True,
                model="latest_long"  # Best accuracy model
            )
            
            response = self.stt_client.recognize(
                config=recognition_config,
                audio=audio
            )
            
            if response.results:
                transcript = response.results[0].alternatives[0].transcript
                confidence = response.results[0].alternatives[0].confidence
                
                self.logger.info("Speech-to-text completed", 
                               provider="google", 
                               confidence=confidence,
                               transcript_length=len(transcript))
                return transcript
            else:
                return ""
                
        except Exception as e:
            self.logger.error("Google STT error", error=str(e))
            return await self._mock_speech_to_text(audio_data)
    
    async def _openai_speech_to_text(self, audio_data: bytes, config: Optional[Dict[str, Any]] = None) -> str:
        """OpenAI Speech-to-Text implementation."""
        try:
            # Create a file-like object from bytes
            audio_file = io.BytesIO(audio_data)
            audio_file.name = "audio.webm"
            
            response = self.openai_client.audio.transcriptions.create(
                model="whisper-1",
                file=audio_file,
                language=config.get("language", "en") if config else "en"
            )
            
            self.logger.info("Speech-to-text completed", 
                           provider="openai", 
                           transcript_length=len(response.text))
            return response.text
            
        except Exception as e:
            self.logger.error("OpenAI STT error", error=str(e))
            return await self._mock_speech_to_text(audio_data)
    
    def _enhance_text_with_ssml(self, text: str) -> str:
        """Enhance text with SSML for more natural speech."""
        # Add natural pauses and emphasis
        enhanced_text = text
        
        # Add pauses after sentences
        enhanced_text = enhanced_text.replace('. ', '.<break time="0.5s"/> ')
        enhanced_text = enhanced_text.replace('! ', '!<break time="0.5s"/> ')
        enhanced_text = enhanced_text.replace('? ', '?<break time="0.5s"/> ')
        
        # Emphasize important words
        enhanced_text = enhanced_text.replace('important', '<emphasis level="strong">important</emphasis>')
        enhanced_text = enhanced_text.replace('urgent', '<emphasis level="strong">urgent</emphasis>')
        
        # Wrap in SSML
        ssml = f'<speak>{enhanced_text}</speak>'
        return ssml
    
    async def _mock_text_to_speech(self, text: str) -> bytes:
        """Mock TTS for testing."""
        self.logger.info("Mock TTS", text=text[:50])
        return b"mock_audio_data"
    
    async def _mock_speech_to_text(self, audio_data: bytes) -> str:
        """Mock STT for testing."""
        self.logger.info("Mock STT", audio_size=len(audio_data))
        return "This is a mock transcription of the audio input."


# Global voice service instance - integrates with your existing Google Cloud setup
voice_service = VoiceService(provider="google")  # Uses your pro-course-433716-v0 project
