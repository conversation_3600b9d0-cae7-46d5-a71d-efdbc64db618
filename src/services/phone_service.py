"""Phone Integration Service for making outbound calls via telephony providers."""

import asyncio
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import structlog

try:
    from twilio.rest import Client as TwilioClient
    from twilio.twiml import VoiceResponse
    TWILIO_AVAILABLE = True
except ImportError:
    TWILIO_AVAILABLE = False

from ..services.voice_service import voice_service
from ..services.conversation_manager import conversation_manager
from ..config import settings

logger = structlog.get_logger(__name__)


class CallEvent:
    """Represents a call event."""
    
    def __init__(self, event_type: str, call_id: str, data: Dict[str, Any]):
        self.event_type = event_type
        self.call_id = call_id
        self.data = data
        self.timestamp = datetime.utcnow()


class PhoneService:
    """Service for making outbound calls and handling telephony."""
    
    def __init__(self):
        """Initialize the phone service."""
        self.logger = structlog.get_logger(__name__)
        
        # Twilio configuration
        self.twilio_client = None
        self.twilio_phone_number = None
        
        if TWILIO_AVAILABLE:
            try:
                # Initialize Twilio client (you'll need to set these in your config)
                account_sid = getattr(settings, 'twilio_account_sid', None)
                auth_token = getattr(settings, 'twilio_auth_token', None)
                self.twilio_phone_number = getattr(settings, 'twilio_phone_number', None)
                
                if account_sid and auth_token:
                    self.twilio_client = TwilioClient(account_sid, auth_token)
                    self.logger.info("Twilio client initialized")
                else:
                    self.logger.warning("Twilio credentials not configured")
            except Exception as e:
                self.logger.error("Failed to initialize Twilio client", error=str(e))
        
        # Call event handlers
        self.event_handlers: Dict[str, List[Callable]] = {}
        
        # Active calls tracking
        self.active_calls: Dict[str, Dict[str, Any]] = {}
    
    async def make_outbound_call(
        self,
        call_id: str,
        phone_number: str,
        customer_id: str,
        purpose: str,
        conversation_script: Optional[str] = None
    ) -> Dict[str, Any]:
        """Make an outbound call to a customer.
        
        Args:
            call_id: Unique call identifier
            phone_number: Customer's phone number
            customer_id: Customer identifier
            purpose: Purpose of the call
            conversation_script: Optional initial script
            
        Returns:
            Call result information
        """
        self.logger.info(
            "Initiating outbound call",
            call_id=call_id,
            phone_number=phone_number,
            customer_id=customer_id,
            purpose=purpose
        )
        
        try:
            if self.twilio_client and self.twilio_phone_number:
                return await self._make_twilio_call(
                    call_id, phone_number, customer_id, purpose, conversation_script
                )
            else:
                return await self._make_mock_call(
                    call_id, phone_number, customer_id, purpose, conversation_script
                )
                
        except Exception as e:
            self.logger.error("Error making outbound call", call_id=call_id, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "call_id": call_id
            }
    
    async def _make_twilio_call(
        self,
        call_id: str,
        phone_number: str,
        customer_id: str,
        purpose: str,
        conversation_script: Optional[str] = None
    ) -> Dict[str, Any]:
        """Make call using Twilio."""
        try:
            # Create webhook URL for handling call events
            webhook_url = f"{settings.api_base_url}/phone/webhook/{call_id}"
            
            # Make the call
            call = self.twilio_client.calls.create(
                to=phone_number,
                from_=self.twilio_phone_number,
                url=webhook_url,
                method='POST',
                status_callback=f"{webhook_url}/status",
                status_callback_event=['initiated', 'ringing', 'answered', 'completed'],
                status_callback_method='POST'
            )
            
            # Store call information
            self.active_calls[call_id] = {
                "twilio_call_sid": call.sid,
                "phone_number": phone_number,
                "customer_id": customer_id,
                "purpose": purpose,
                "conversation_script": conversation_script,
                "started_at": datetime.utcnow(),
                "status": "initiated"
            }
            
            self.logger.info(
                "Twilio call initiated",
                call_id=call_id,
                twilio_sid=call.sid,
                phone_number=phone_number
            )
            
            return {
                "success": True,
                "call_id": call_id,
                "twilio_sid": call.sid,
                "status": "initiated"
            }
            
        except Exception as e:
            self.logger.error("Twilio call failed", call_id=call_id, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "call_id": call_id
            }
    
    async def _make_mock_call(
        self,
        call_id: str,
        phone_number: str,
        customer_id: str,
        purpose: str,
        conversation_script: Optional[str] = None
    ) -> Dict[str, Any]:
        """Make a mock call for testing."""
        self.logger.info("Making mock call", call_id=call_id)
        
        # Simulate call connection
        await asyncio.sleep(2)
        
        # Store call information
        self.active_calls[call_id] = {
            "phone_number": phone_number,
            "customer_id": customer_id,
            "purpose": purpose,
            "conversation_script": conversation_script,
            "started_at": datetime.utcnow(),
            "status": "connected"
        }
        
        # Simulate successful connection (80% success rate)
        import random
        if random.random() < 0.8:
            # Start AI conversation
            await self._start_ai_conversation(call_id, customer_id, purpose, conversation_script)
            
            return {
                "success": True,
                "call_id": call_id,
                "status": "connected"
            }
        else:
            # Simulate call failure
            failure_reasons = ["no_answer", "busy", "invalid_number"]
            failure_reason = random.choice(failure_reasons)
            
            self.active_calls[call_id]["status"] = failure_reason
            
            return {
                "success": False,
                "call_id": call_id,
                "status": failure_reason,
                "error": f"Call failed: {failure_reason}"
            }
    
    async def _start_ai_conversation(
        self,
        call_id: str,
        customer_id: str,
        purpose: str,
        conversation_script: Optional[str] = None
    ):
        """Start AI conversation for the call."""
        try:
            # Start conversation in conversation manager
            conversation_id = await conversation_manager.start_conversation(
                customer_id=customer_id,
                channel="outbound_call"
            )
            
            # Update call info
            self.active_calls[call_id]["conversation_id"] = conversation_id
            
            # Generate initial AI message based on purpose
            initial_message = await self._generate_initial_message(customer_id, purpose, conversation_script)
            
            # Convert to speech
            audio_data = await voice_service.text_to_speech(initial_message)
            
            # In a real implementation, this would play the audio through the phone call
            # For now, we'll just log it
            self.logger.info(
                "AI conversation started",
                call_id=call_id,
                conversation_id=conversation_id,
                initial_message=initial_message[:100]
            )
            
            # Add initial turn to conversation
            await conversation_manager.add_turn(
                conversation_id=conversation_id,
                customer_message="[Call answered]",
                ai_response=initial_message,
                processing_time=1.0,
                quality_score=0.9
            )
            
            # Emit call event
            await self._emit_call_event("conversation_started", call_id, {
                "conversation_id": conversation_id,
                "initial_message": initial_message
            })
            
        except Exception as e:
            self.logger.error("Error starting AI conversation", call_id=call_id, error=str(e))
    
    async def _generate_initial_message(
        self,
        customer_id: str,
        purpose: str,
        conversation_script: Optional[str] = None
    ) -> str:
        """Generate initial AI message for the call."""
        if conversation_script:
            return conversation_script
        
        # Get customer information
        from ..services.customer_database import customer_db
        customer_info = await customer_db.get_customer_info(customer_id)
        customer_name = customer_info.get("first_name", "Customer") if customer_info else "Customer"
        
        # Generate message based on purpose
        purpose_messages = {
            "follow_up": f"Hello {customer_name}, this is Angela from customer support. I'm calling to follow up on your recent interaction with us. How are you doing today?",
            "survey": f"Hi {customer_name}, this is Angela from customer support. I hope you're having a great day! I'm calling to get your feedback on your recent experience with us. Do you have a few minutes to chat?",
            "proactive_support": f"Hello {customer_name}, this is Angela from customer support. I noticed you might need some assistance with your account, so I wanted to reach out proactively. How can I help you today?",
            "order_update": f"Hi {customer_name}, this is Angela calling with an update about your recent order. I have some good news to share with you!",
            "payment_reminder": f"Hello {customer_name}, this is Angela from customer support. I'm calling regarding your account. Don't worry, this is just a friendly reminder. How are you today?",
            "feedback_request": f"Hi {customer_name}, this is Angela from customer support. I hope you're doing well! I'm calling to get your valuable feedback on our services. Do you have a moment to chat?",
            "product_recommendation": f"Hello {customer_name}, this is Angela from customer support. I hope you're having a wonderful day! I'm calling because I have some exciting product recommendations that might interest you.",
            "retention": f"Hi {customer_name}, this is Angela from customer support. I hope you're doing well! I wanted to personally reach out because you're such a valued customer. How has your experience been with us lately?"
        }
        
        return purpose_messages.get(purpose, f"Hello {customer_name}, this is Angela from customer support. How can I help you today?")
    
    async def handle_call_webhook(self, call_id: str, webhook_data: Dict[str, Any]) -> str:
        """Handle webhook from telephony provider."""
        self.logger.info("Received call webhook", call_id=call_id, data=webhook_data)
        
        if call_id not in self.active_calls:
            self.logger.warning("Webhook for unknown call", call_id=call_id)
            return self._generate_empty_twiml()
        
        call_info = self.active_calls[call_id]
        
        # Handle different webhook events
        if webhook_data.get("CallStatus") == "answered":
            # Call was answered, start AI conversation
            await self._start_ai_conversation(
                call_id,
                call_info["customer_id"],
                call_info["purpose"],
                call_info.get("conversation_script")
            )
            
            # Return TwiML to play initial message
            return self._generate_initial_twiml(call_id)
        
        elif webhook_data.get("CallStatus") in ["completed", "busy", "no-answer", "failed"]:
            # Call ended
            call_info["status"] = webhook_data["CallStatus"]
            call_info["ended_at"] = datetime.utcnow()
            
            await self._emit_call_event("call_ended", call_id, {
                "status": webhook_data["CallStatus"],
                "duration": webhook_data.get("CallDuration", 0)
            })
        
        return self._generate_empty_twiml()
    
    def _generate_initial_twiml(self, call_id: str) -> str:
        """Generate TwiML for initial call response."""
        if not TWILIO_AVAILABLE:
            return ""
        
        response = VoiceResponse()
        
        # Play initial message
        response.say("Hello! Please hold while I connect you with our AI assistant.")
        
        # Set up for interactive conversation
        response.gather(
            input='speech',
            action=f'/phone/webhook/{call_id}/speech',
            method='POST',
            speech_timeout='auto'
        )
        
        return str(response)
    
    def _generate_empty_twiml(self) -> str:
        """Generate empty TwiML response."""
        if not TWILIO_AVAILABLE:
            return ""
        
        response = VoiceResponse()
        return str(response)
    
    async def _emit_call_event(self, event_type: str, call_id: str, data: Dict[str, Any]):
        """Emit call event to registered handlers."""
        event = CallEvent(event_type, call_id, data)
        
        handlers = self.event_handlers.get(event_type, [])
        for handler in handlers:
            try:
                await handler(event)
            except Exception as e:
                self.logger.error("Error in call event handler", event_type=event_type, error=str(e))
    
    def register_event_handler(self, event_type: str, handler: Callable):
        """Register an event handler for call events."""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
    
    async def get_call_status(self, call_id: str) -> Optional[Dict[str, Any]]:
        """Get status of an active call."""
        return self.active_calls.get(call_id)
    
    async def end_call(self, call_id: str) -> bool:
        """End an active call."""
        if call_id not in self.active_calls:
            return False
        
        call_info = self.active_calls[call_id]
        
        if self.twilio_client and "twilio_call_sid" in call_info:
            try:
                # End Twilio call
                self.twilio_client.calls(call_info["twilio_call_sid"]).update(status="completed")
                self.logger.info("Twilio call ended", call_id=call_id)
            except Exception as e:
                self.logger.error("Error ending Twilio call", call_id=call_id, error=str(e))
        
        # Update call status
        call_info["status"] = "ended"
        call_info["ended_at"] = datetime.utcnow()
        
        await self._emit_call_event("call_ended", call_id, {"status": "ended"})
        
        return True


# Global phone service instance
phone_service = PhoneService()
