"""
Natural Conversation Flow Service

This service manages the transition from casual, friendly conversation to business-focused
dialogue for tasks like parcel tracking, payment reminders, and order management.
"""

from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass
import random
import structlog
from datetime import datetime, timedelta

from .customer_database import customer_db
from .multilingual_service import multilingual_service, LanguageDetectionResult, TranslationResult

logger = structlog.get_logger(__name__)


class ConversationPhase(Enum):
    """Phases of natural conversation flow."""
    GREETING = "greeting"
    CASUAL_CHAT = "casual_chat"
    TRANSITION = "transition"
    BUSINESS_FOCUS = "business_focus"
    RESOLUTION = "resolution"
    CLOSING = "closing"


class BusinessIntent(Enum):
    """Business intents that the AI should guide towards."""
    PARCEL_TRACKING = "parcel_tracking"
    PAYMENT_REMINDER = "payment_reminder"
    ORDER_MANAGEMENT = "order_management"
    ACCOUNT_INQUIRY = "account_inquiry"
    SUPPORT_ISSUE = "support_issue"
    GENERAL_INQUIRY = "general_inquiry"


@dataclass
class ConversationContext:
    """Context for natural conversation flow."""
    customer_id: str
    conversation_id: str
    current_phase: ConversationPhase
    turns_in_phase: int
    total_turns: int
    detected_business_intent: Optional[BusinessIntent]
    customer_mood: str  # happy, neutral, frustrated, etc.
    transition_readiness: float  # 0.0 to 1.0
    pending_business_tasks: List[str]
    last_business_mention: Optional[datetime]
    # Multilingual support
    customer_language: str = "en"  # Detected or preferred language
    language_confidence: float = 1.0  # Confidence in language detection
    needs_translation: bool = False  # Whether responses need translation
    original_language_messages: List[str] = None  # Store original messages for context


class NaturalConversationFlow:
    """Manages natural conversation flow with smooth business transitions."""
    
    def __init__(self):
        """Initialize the natural conversation flow manager."""
        self.logger = structlog.get_logger(__name__)
        
        # Casual conversation starters and responses
        self.casual_responses = {
            "greeting": [
                "Hello! How are you doing today?",
                "Hi there! I hope you're having a wonderful day!",
                "Good {time_of_day}! How's everything going with you?",
                "Hello! It's great to hear from you. How are things?"
            ],
            "weather": [
                "I hope the weather is treating you well today!",
                "It's always nice when we can enjoy good weather, isn't it?",
                "Weather can really affect our mood - I hope it's pleasant where you are!"
            ],
            "general_wellbeing": [
                "That's wonderful to hear! I'm glad you're doing well.",
                "I'm so happy to hear that! It sounds like things are going great for you.",
                "That's fantastic! It's always a pleasure to chat with someone in good spirits.",
                "I'm sorry to hear that. I hope things improve for you soon."
            ],
            "empathy": [
                "I completely understand how you feel.",
                "That sounds like it's been quite challenging for you.",
                "I can imagine that must be frustrating.",
                "Thank you for sharing that with me."
            ]
        }
        
        # Transition phrases to move from casual to business
        self.transition_phrases = {
            "gentle": [
                "By the way, I wanted to check in with you about something...",
                "While we're chatting, I noticed there might be something I can help you with...",
                "Speaking of which, I have some information that might interest you...",
                "That reminds me, I wanted to follow up on something with you..."
            ],
            "direct": [
                "I'm actually reaching out today because...",
                "The reason I'm calling is to help you with...",
                "I wanted to personally contact you about...",
                "I have some important information regarding..."
            ],
            "helpful": [
                "I'd love to help you with something if you have a moment...",
                "I have some good news I wanted to share with you...",
                "I thought you might appreciate knowing about...",
                "I wanted to make sure you're aware of..."
            ]
        }
        
        # Business task templates
        self.business_templates = {
            BusinessIntent.PARCEL_TRACKING: {
                "introduction": "I wanted to give you an update on your recent order.",
                "questions": [
                    "Would you like me to check the status of your recent shipment?",
                    "I can provide you with tracking information if you'd like.",
                    "Would you like to know when your package is expected to arrive?"
                ]
            },
            BusinessIntent.PAYMENT_REMINDER: {
                "introduction": "I wanted to remind you about your account balance.",
                "questions": [
                    "Would you like me to help you with your payment options?",
                    "I can assist you with updating your payment method if needed.",
                    "Would you prefer to handle this online or over the phone?"
                ]
            },
            BusinessIntent.ORDER_MANAGEMENT: {
                "introduction": "I wanted to check if you need any help with your recent orders.",
                "questions": [
                    "Is there anything about your recent order you'd like to discuss?",
                    "Would you like me to review your order history with you?",
                    "Do you have any questions about your current orders?"
                ]
            }
        }
    
    async def initialize_conversation(
        self,
        customer_id: str,
        conversation_id: str,
        intended_business_purpose: Optional[BusinessIntent] = None,
        customer_language_preference: Optional[str] = None
    ) -> ConversationContext:
        """Initialize a new natural conversation flow with multilingual support."""

        # Get customer info for personalization
        customer_info = await customer_db.get_customer_info(customer_id)

        # Determine customer language preference
        detected_language = customer_language_preference or "en"
        if customer_info and "language_preference" in customer_info:
            detected_language = customer_info["language_preference"]

        # Determine pending business tasks
        pending_tasks = await self._get_pending_business_tasks(customer_id)

        context = ConversationContext(
            customer_id=customer_id,
            conversation_id=conversation_id,
            current_phase=ConversationPhase.GREETING,
            turns_in_phase=0,
            total_turns=0,
            detected_business_intent=intended_business_purpose,
            customer_mood="neutral",
            transition_readiness=0.0,
            pending_business_tasks=pending_tasks,
            last_business_mention=None,
            customer_language=detected_language,
            language_confidence=1.0,
            needs_translation=(detected_language != "en"),
            original_language_messages=[]
        )
        
        self.logger.info(
            "Initialized natural conversation flow",
            customer_id=customer_id,
            conversation_id=conversation_id,
            pending_tasks=len(pending_tasks),
            intended_purpose=intended_business_purpose.value if intended_business_purpose else None
        )
        
        return context
    
    async def generate_response(
        self,
        context: ConversationContext,
        customer_message: str,
        customer_info: Optional[Dict] = None
    ) -> Tuple[str, ConversationContext]:
        """Generate a natural response based on conversation phase and customer input with multilingual support."""

        # Process multilingual message
        multilingual_result = await multilingual_service.process_multilingual_message(
            customer_message,
            context.customer_language
        )

        # Update context with language information
        if multilingual_result["language_detection"].detected_language != context.customer_language:
            context.customer_language = multilingual_result["language_detection"].detected_language
            context.language_confidence = multilingual_result["language_detection"].confidence
            context.needs_translation = multilingual_result["language_detection"].needs_translation

        # Store original message for context
        if context.original_language_messages is None:
            context.original_language_messages = []
        context.original_language_messages.append(customer_message)

        # Use processed (translated) message for analysis
        processed_message = multilingual_result["processed_message"]

        # Update context based on customer message
        updated_context = await self._update_context(context, processed_message, multilingual_result)

        # Generate response based on current phase
        if updated_context.current_phase == ConversationPhase.GREETING:
            response = await self._generate_greeting_response(updated_context, processed_message, customer_info)
        elif updated_context.current_phase == ConversationPhase.CASUAL_CHAT:
            response = await self._generate_casual_response(updated_context, processed_message, customer_info)
        elif updated_context.current_phase == ConversationPhase.TRANSITION:
            response = await self._generate_transition_response(updated_context, processed_message, customer_info)
        elif updated_context.current_phase == ConversationPhase.BUSINESS_FOCUS:
            response = await self._generate_business_response(updated_context, processed_message, customer_info)
        else:
            response = await self._generate_resolution_response(updated_context, processed_message, customer_info)

        # Translate response back to customer's language if needed
        if updated_context.needs_translation and updated_context.customer_language != "en":
            translation_result = await multilingual_service.translate_text(
                response,
                target_language=updated_context.customer_language,
                source_language="en"
            )
            response = translation_result.translated_text

        # Update turn counters
        updated_context.turns_in_phase += 1
        updated_context.total_turns += 1

        return response, updated_context
    
    async def _update_context(
        self,
        context: ConversationContext,
        customer_message: str,
        multilingual_result: Optional[Dict] = None
    ) -> ConversationContext:
        """Update conversation context based on customer message with multilingual support."""

        # Analyze customer mood
        context.customer_mood = self._analyze_customer_mood(customer_message)

        # Check for business intent in customer message (use multilingual detection if available)
        detected_intent = None
        if multilingual_result and multilingual_result.get("business_intent"):
            # Map multilingual business intent to our enum
            intent_mapping = {
                "parcel_tracking": BusinessIntent.PARCEL_TRACKING,
                "payment": BusinessIntent.PAYMENT_REMINDER,
                "order_management": BusinessIntent.ORDER_MANAGEMENT,
                "account_inquiry": BusinessIntent.ACCOUNT_INQUIRY,
                "support_issue": BusinessIntent.SUPPORT_ISSUE
            }
            detected_intent = intent_mapping.get(multilingual_result["business_intent"])

        if not detected_intent:
            detected_intent = self._detect_business_intent(customer_message)

        if detected_intent and not context.detected_business_intent:
            context.detected_business_intent = detected_intent

        # Update transition readiness
        context.transition_readiness = self._calculate_transition_readiness(context, customer_message)

        # Determine if phase should change
        new_phase = self._determine_next_phase(context, customer_message)
        if new_phase != context.current_phase:
            context.current_phase = new_phase
            context.turns_in_phase = 0

        return context
    
    def _analyze_customer_mood(self, message: str) -> str:
        """Analyze customer mood from their message."""
        message_lower = message.lower()
        
        positive_indicators = ["good", "great", "wonderful", "excellent", "happy", "fine", "well"]
        negative_indicators = ["bad", "terrible", "awful", "frustrated", "angry", "upset", "problem"]
        
        if any(word in message_lower for word in positive_indicators):
            return "positive"
        elif any(word in message_lower for word in negative_indicators):
            return "negative"
        else:
            return "neutral"
    
    def _detect_business_intent(self, message: str) -> Optional[BusinessIntent]:
        """Detect business intent from customer message."""
        message_lower = message.lower()

        # Parcel tracking (English + multilingual)
        if any(word in message_lower for word in ["package", "delivery", "shipping", "tracking", "order",
                                                  "pakkie", "aflewering", "volg", "versending",  # Afrikaans
                                                  "iphakeji", "ukulethwa", "ukulandelela"]):  # Zulu/Xhosa
            return BusinessIntent.PARCEL_TRACKING

        # Payment/Balance inquiries (English + multilingual)
        elif any(word in message_lower for word in ["payment", "bill", "charge", "balance", "money", "owe", "debt", "due",
                                                    "skuld", "balans", "verskuldig", "hoeveel skuld", "rekening", "betaling", "geld",  # Afrikaans
                                                    "ityala", "imali", "ukuhlawula",  # Xhosa
                                                    "isikweletu", "ukukhokhela",  # Zulu/Ndebele
                                                    "deni", "pesa", "malipo"]):  # Swahili
            return BusinessIntent.PAYMENT_REMINDER

        # Account inquiries
        elif any(word in message_lower for word in ["account", "profile", "information", "details", "statement"]):
            return BusinessIntent.ACCOUNT_INQUIRY

        # Support issues
        elif any(word in message_lower for word in ["problem", "issue", "help", "support", "trouble"]):
            return BusinessIntent.SUPPORT_ISSUE

        return None
    
    def _calculate_transition_readiness(self, context: ConversationContext, customer_message: str) -> float:
        """Calculate how ready the conversation is to transition to business."""
        readiness = 0.0
        
        # Base readiness increases with turns
        readiness += min(0.3, context.total_turns * 0.1)
        
        # Positive mood increases readiness
        if context.customer_mood == "positive":
            readiness += 0.3
        elif context.customer_mood == "neutral":
            readiness += 0.2
        
        # Business intent detection increases readiness
        if context.detected_business_intent:
            readiness += 0.4
        
        # Pending business tasks increase readiness
        if context.pending_business_tasks:
            readiness += 0.2
        
        # Time in casual chat increases readiness
        if context.current_phase == ConversationPhase.CASUAL_CHAT and context.turns_in_phase >= 2:
            readiness += 0.3
        
        return min(1.0, readiness)
    
    def _determine_next_phase(self, context: ConversationContext, customer_message: str) -> ConversationPhase:
        """Determine the next conversation phase."""
        
        # If customer directly mentions business, jump to business focus
        if self._detect_business_intent(customer_message):
            return ConversationPhase.BUSINESS_FOCUS
        
        # Phase progression logic
        if context.current_phase == ConversationPhase.GREETING:
            return ConversationPhase.CASUAL_CHAT
        
        elif context.current_phase == ConversationPhase.CASUAL_CHAT:
            # Transition when readiness is high enough
            if context.transition_readiness >= 0.7:
                return ConversationPhase.TRANSITION
            return ConversationPhase.CASUAL_CHAT
        
        elif context.current_phase == ConversationPhase.TRANSITION:
            return ConversationPhase.BUSINESS_FOCUS
        
        elif context.current_phase == ConversationPhase.BUSINESS_FOCUS:
            # Stay in business focus until resolution
            if "thank" in customer_message.lower() or "done" in customer_message.lower():
                return ConversationPhase.RESOLUTION
            return ConversationPhase.BUSINESS_FOCUS
        
        return context.current_phase
    
    async def _get_pending_business_tasks(self, customer_id: str) -> List[str]:
        """Get pending business tasks for the customer."""
        # This would integrate with your actual business systems
        # For now, return mock data
        return ["outstanding_payment", "pending_delivery"]
    
    def _get_time_of_day(self) -> str:
        """Get appropriate time of day greeting."""
        hour = datetime.now().hour
        if hour < 12:
            return "morning"
        elif hour < 17:
            return "afternoon"
        else:
            return "evening"

    async def _generate_greeting_response(
        self,
        context: ConversationContext,
        customer_message: str,
        customer_info: Optional[Dict] = None
    ) -> str:
        """Generate a friendly greeting response with multilingual support."""

        # Use multilingual greeting if customer language is not English
        if context.customer_language != "en":
            localized_greeting = multilingual_service.get_localized_greeting(context.customer_language)
            if customer_info and customer_info.get("first_name"):
                name = customer_info["first_name"]
                # Add name to localized greeting (basic approach)
                return f"{localized_greeting} {name}!"
            return localized_greeting

        # English greeting logic
        time_of_day = self._get_time_of_day()
        greeting_template = random.choice(self.casual_responses["greeting"])

        # Personalize with customer name if available
        if customer_info and customer_info.get("first_name"):
            name = customer_info["first_name"]
            greeting = f"Hello {name}! How are you doing today?"
        else:
            greeting = greeting_template.format(time_of_day=time_of_day)

        return greeting

    async def _generate_casual_response(
        self,
        context: ConversationContext,
        customer_message: str,
        customer_info: Optional[Dict] = None
    ) -> str:
        """Generate a casual, friendly response."""
        message_lower = customer_message.lower()

        # Respond to common casual topics
        if any(word in message_lower for word in ["good", "great", "fine", "well"]):
            response = random.choice(self.casual_responses["general_wellbeing"][:3])  # Positive responses
        elif any(word in message_lower for word in ["bad", "not good", "terrible", "awful"]):
            response = random.choice(self.casual_responses["general_wellbeing"][3:])  # Empathetic responses
        elif any(word in message_lower for word in ["weather", "sunny", "rainy", "cold", "hot"]):
            response = random.choice(self.casual_responses["weather"])
        else:
            response = random.choice(self.casual_responses["empathy"])

        # Add a follow-up to keep conversation flowing
        follow_ups = [
            " I hope your day continues to go well!",
            " It's always nice to connect with our customers.",
            " I appreciate you taking the time to chat with me."
        ]

        return response + random.choice(follow_ups)

    async def _generate_transition_response(
        self,
        context: ConversationContext,
        customer_message: str,
        customer_info: Optional[Dict] = None
    ) -> str:
        """Generate a smooth transition from casual to business conversation."""

        # Choose transition style based on customer mood and context
        if context.customer_mood == "positive":
            transition_style = "gentle"
        elif context.pending_business_tasks:
            transition_style = "helpful"
        else:
            transition_style = "direct"

        transition_phrase = random.choice(self.transition_phrases[transition_style])

        # Add specific business context if available
        if context.detected_business_intent and context.detected_business_intent in self.business_templates:
            business_intro = self.business_templates[context.detected_business_intent]["introduction"]
            return f"{transition_phrase} {business_intro}"
        elif context.pending_business_tasks:
            if "outstanding_payment" in context.pending_business_tasks:
                return f"{transition_phrase} I wanted to remind you about your account balance."
            elif "pending_delivery" in context.pending_business_tasks:
                return f"{transition_phrase} I have an update on your recent order."

        return f"{transition_phrase} I wanted to check if there's anything I can help you with today."

    async def _generate_business_response(
        self,
        context: ConversationContext,
        customer_message: str,
        customer_info: Optional[Dict] = None
    ) -> str:
        """Generate a business-focused response."""

        # Handle specific business intents with actual data
        # Check for both enum and string values for compatibility
        if (context.detected_business_intent == BusinessIntent.PAYMENT_REMINDER or
            context.detected_business_intent == "payment_reminder"):
            # Get customer balance information using context customer_id
            customer_id = context.customer_id
            if customer_id:
                from ..services.customer_database import customer_db
                account_info = await customer_db.get_customer_info(customer_id)
                if account_info:
                    balance = account_info.get("outstanding_balance", 0)
                    customer_language = context.customer_language or "en"

                    # Use multilingual service for localized balance response
                    balance_response = multilingual_service.get_localized_balance_response(customer_language, balance)
                    return balance_response

        # If we have a payment-related message, force the balance response
        if any(word in customer_message.lower() for word in ["skuld", "balans", "verskuldig", "hoeveel skuld"]):
            customer_id = context.customer_id
            if customer_id:
                from ..services.customer_database import customer_db
                account_info = await customer_db.get_customer_info(customer_id)
                if account_info:
                    balance = account_info.get("outstanding_balance", 0)
                    customer_language = context.customer_language or "en"
                    balance_response = multilingual_service.get_localized_balance_response(customer_language, balance)
                    return balance_response

        # Fallback to template-based responses
        if context.detected_business_intent and context.detected_business_intent in self.business_templates:
            template = self.business_templates[context.detected_business_intent]
            question = random.choice(template["questions"])
            return question

        # Default business response
        return "How can I assist you with your account or orders today?"

    async def _generate_resolution_response(
        self,
        context: ConversationContext,
        customer_message: str,
        customer_info: Optional[Dict] = None
    ) -> str:
        """Generate a resolution/closing response."""

        closing_responses = [
            "I'm glad I could help you today! Is there anything else you need assistance with?",
            "Thank you for taking the time to chat with me. Have a wonderful rest of your day!",
            "It was great talking with you! Please don't hesitate to reach out if you need anything else.",
            "I hope I was able to help. Have a fantastic day!"
        ]

        return random.choice(closing_responses)

    def should_transition_to_business(self, context: ConversationContext) -> bool:
        """Determine if conversation should transition to business focus."""
        return (
            context.transition_readiness >= 0.7 or
            context.detected_business_intent is not None or
            (context.current_phase == ConversationPhase.CASUAL_CHAT and context.turns_in_phase >= 3)
        )

    def get_conversation_summary(self, context: ConversationContext) -> Dict[str, Any]:
        """Get a summary of the conversation state."""
        return {
            "phase": context.current_phase.value,
            "total_turns": context.total_turns,
            "customer_mood": context.customer_mood,
            "business_intent": context.detected_business_intent.value if context.detected_business_intent else None,
            "transition_readiness": context.transition_readiness,
            "pending_tasks": context.pending_business_tasks
        }
