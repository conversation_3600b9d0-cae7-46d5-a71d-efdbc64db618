"""
Empathetic Response Generator
Generates emotionally intelligent responses based on sentiment analysis
"""

from typing import Dict, List, Optional
import structlog
from .sentiment_analyzer import SentimentAnalysis, EmotionalState, EscalationLevel


class EmpatheticResponseGenerator:
    """Generates responses with appropriate emotional tone and empathy."""
    
    def __init__(self):
        self.logger = structlog.get_logger().bind(component="empathetic_response_generator")
        
        # Response templates by tone adaptation
        self.response_templates = {
            "apologetic_de_escalation": {
                "opening": [
                    "I sincerely apologize for this frustrating experience, and I completely understand your anger.",
                    "I'm truly sorry this has happened, and I can hear how upset you are about this situation.",
                    "Please accept my heartfelt apology - I can see how this has caused you significant frustration."
                ],
                "empathy": [
                    "Your frustration is completely justified, and I want to make this right immediately.",
                    "I would be just as upset if I were in your position, and you have every right to feel this way.",
                    "This is absolutely not the experience we want any of our valued customers to have."
                ],
                "action": [
                    "Let me personally ensure we resolve this right now and prevent it from happening again.",
                    "I'm going to take immediate action to fix this and make sure you're completely satisfied.",
                    "I'm escalating this to our senior team to ensure we provide you with the best possible resolution."
                ]
            },
            "empathetic_solution_focused": {
                "opening": [
                    "I understand your concern and I'm here to help resolve this for you.",
                    "I can see this is important to you, and I want to make sure we address it properly.",
                    "Thank you for bringing this to our attention - let's work together to fix this."
                ],
                "empathy": [
                    "I appreciate your patience as we work through this together.",
                    "Your experience matters to us, and I want to ensure we get this resolved.",
                    "I can understand why this would be concerning, and I'm committed to helping."
                ],
                "action": [
                    "Here's what I can do to resolve this for you right away:",
                    "Let me check our options and find the best solution for your situation:",
                    "I have several ways we can address this - let me walk you through them:"
                ]
            },
            "understanding_action_oriented": {
                "opening": [
                    "I can see you've been dealing with this issue, and I want to help get it sorted out.",
                    "I understand this has been frustrating for you, and I'm here to make it right.",
                    "Thank you for your patience - let's get this resolved quickly."
                ],
                "empathy": [
                    "I know how inconvenient this must be for you.",
                    "I appreciate you taking the time to explain the situation to me.",
                    "Your time is valuable, and I want to resolve this efficiently."
                ],
                "action": [
                    "Here are the immediate steps I'm taking to fix this:",
                    "Let me get this resolved for you right now:",
                    "I'm going to take care of this immediately:"
                ]
            },
            "apologetic_reassuring": {
                "opening": [
                    "I'm sorry to hear you're disappointed with your experience.",
                    "I understand this wasn't what you were expecting, and I apologize.",
                    "Thank you for giving us the opportunity to make this better."
                ],
                "empathy": [
                    "Your expectations are important to us, and we want to exceed them.",
                    "I can understand why this would be disappointing given your past experiences with us.",
                    "We value your loyalty and want to restore your confidence in our service."
                ],
                "action": [
                    "Here's how I'm going to make this right for you:",
                    "Let me show you how we can turn this experience around:",
                    "I have some options that I think will address your concerns:"
                ]
            },
            "positive_reinforcing": {
                "opening": [
                    "Thank you so much for your wonderful feedback!",
                    "It's fantastic to hear you're happy with our service!",
                    "Your positive experience truly makes our day!"
                ],
                "empathy": [
                    "Customers like you are the reason we love what we do.",
                    "Your satisfaction is our greatest reward.",
                    "It's customers like you who inspire us to keep improving."
                ],
                "action": [
                    "Is there anything else I can help you with today?",
                    "We're always here if you need any additional assistance.",
                    "Please don't hesitate to reach out if you need anything else."
                ]
            },
            "professional_helpful": {
                "opening": [
                    "Thank you for contacting us today.",
                    "I'm here to help you with your inquiry.",
                    "How can I assist you today?"
                ],
                "empathy": [
                    "I want to make sure we address all your questions.",
                    "Your satisfaction is important to us.",
                    "I'm committed to providing you with the best possible service."
                ],
                "action": [
                    "Let me help you with that right away:",
                    "Here's what I can do for you:",
                    "I'll be happy to assist you with:"
                ]
            },
            "neutral_professional": {
                "opening": [
                    "Thank you for reaching out to us.",
                    "I'm here to assist you with your inquiry.",
                    "How may I help you today?"
                ],
                "empathy": [
                    "I want to ensure we provide you with accurate information.",
                    "I'm here to help resolve any questions you may have.",
                    "Let me make sure I understand your needs correctly."
                ],
                "action": [
                    "Here's the information you requested:",
                    "Let me provide you with the details:",
                    "I can help you with the following:"
                ]
            }
        }
        
        # Escalation messages
        self.escalation_messages = {
            EscalationLevel.MONITOR: "I'm making a note of your concern to ensure we track this properly.",
            EscalationLevel.ESCALATE: "I'm connecting you with a senior specialist who can provide additional assistance.",
            EscalationLevel.URGENT_ESCALATE: "I'm immediately escalating this to our management team for priority handling."
        }
    
    async def generate_empathetic_response(
        self,
        sentiment_analysis: SentimentAnalysis,
        customer_message: str,
        customer_info: Optional[Dict] = None,
        context: Optional[str] = None
    ) -> Dict[str, str]:
        """
        Generate an empathetic response based on sentiment analysis.
        
        Args:
            sentiment_analysis: Result from sentiment analyzer
            customer_message: Original customer message
            customer_info: Customer information for personalization
            context: Additional context for the response
            
        Returns:
            Dictionary with response components
        """
        tone = sentiment_analysis.tone_adaptation
        templates = self.response_templates.get(tone, self.response_templates["neutral_professional"])
        
        # Select appropriate templates based on intensity
        opening_idx = min(int(sentiment_analysis.intensity * len(templates["opening"])), len(templates["opening"]) - 1)
        empathy_idx = min(int(sentiment_analysis.empathy_level_needed * len(templates["empathy"])), len(templates["empathy"]) - 1)
        action_idx = 0  # Default to first action template
        
        response_parts = {
            "opening": templates["opening"][opening_idx],
            "empathy": templates["empathy"][empathy_idx],
            "action_intro": templates["action"][action_idx],
            "escalation_note": "",
            "tone_adaptation": tone,
            "empathy_level": sentiment_analysis.empathy_level_needed
        }
        
        # Add escalation message if needed
        if sentiment_analysis.escalation_level != EscalationLevel.NONE:
            response_parts["escalation_note"] = self.escalation_messages[sentiment_analysis.escalation_level]
        
        # Personalize with customer name if available
        if customer_info and customer_info.get("first_name"):
            name = customer_info["first_name"]
            response_parts["opening"] = f"{name}, {response_parts['opening'].lower()}"
        
        self.logger.info(
            "Empathetic response generated",
            tone=tone,
            empathy_level=sentiment_analysis.empathy_level_needed,
            escalation_level=sentiment_analysis.escalation_level.value
        )
        
        return response_parts
    
    def format_complete_response(self, response_parts: Dict[str, str], solution_content: str = "") -> str:
        """
        Format the complete response from parts.
        
        Args:
            response_parts: Response components from generate_empathetic_response
            solution_content: Specific solution or information content
            
        Returns:
            Complete formatted response
        """
        parts = [
            response_parts["opening"],
            response_parts["empathy"]
        ]
        
        if solution_content:
            parts.append(f"{response_parts['action_intro']} {solution_content}")
        else:
            parts.append(response_parts["action_intro"])
        
        if response_parts["escalation_note"]:
            parts.append(response_parts["escalation_note"])
        
        return " ".join(parts)
    
    def get_de_escalation_phrases(self, intensity: float) -> List[str]:
        """Get appropriate de-escalation phrases based on intensity."""
        if intensity > 0.8:
            return [
                "I completely understand your frustration",
                "Your concerns are absolutely valid",
                "I sincerely apologize for this experience",
                "Let me personally ensure this gets resolved immediately"
            ]
        elif intensity > 0.6:
            return [
                "I can see why this would be frustrating",
                "I appreciate you bringing this to our attention",
                "Let me work on resolving this for you right away",
                "I want to make sure we address this properly"
            ]
        else:
            return [
                "I understand your concern",
                "Thank you for your patience",
                "I'm here to help resolve this",
                "Let me see what I can do for you"
            ]
