"""
Action Link Generator
Creates direct action links and quick buttons for common customer needs
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import structlog


class ActionType(Enum):
    """Types of customer actions."""
    TRACK_ORDER = "track_order"
    REQUEST_REFUND = "request_refund"
    RETURN_ITEM = "return_item"
    CANCEL_ORDER = "cancel_order"
    UPDATE_ADDRESS = "update_address"
    CONTACT_SUPPORT = "contact_support"
    ESCALATE_ISSUE = "escalate_issue"
    VIEW_ACCOUNT = "view_account"
    REORDER = "reorder"
    LEAVE_REVIEW = "leave_review"


@dataclass
class ActionLink:
    """Represents an actionable link or button."""
    action_type: ActionType
    label: str
    url: str
    description: str
    priority: int  # 1-5, 1 being highest priority
    requires_auth: bool = True
    estimated_time: str = "2-3 minutes"
    icon: str = "🔗"


class ActionLinkGenerator:
    """Generates contextual action links based on customer needs."""
    
    def __init__(self, base_url: str = "https://support.example.com"):
        self.logger = structlog.get_logger().bind(component="action_link_generator")
        self.base_url = base_url.rstrip('/')
        
        # Action templates
        self.action_templates = {
            ActionType.TRACK_ORDER: {
                "label": "Track Your Order",
                "description": "Get real-time updates on your order status and delivery",
                "icon": "📦",
                "estimated_time": "1 minute",
                "priority": 1
            },
            ActionType.REQUEST_REFUND: {
                "label": "Request Refund",
                "description": "Start a refund request for your recent purchase",
                "icon": "💰",
                "estimated_time": "3-5 minutes",
                "priority": 2
            },
            ActionType.RETURN_ITEM: {
                "label": "Return Item",
                "description": "Initiate a return for your purchase with prepaid shipping",
                "icon": "📮",
                "estimated_time": "5 minutes",
                "priority": 2
            },
            ActionType.CANCEL_ORDER: {
                "label": "Cancel Order",
                "description": "Cancel your order if it hasn't shipped yet",
                "icon": "❌",
                "estimated_time": "2 minutes",
                "priority": 1
            },
            ActionType.UPDATE_ADDRESS: {
                "label": "Update Delivery Address",
                "description": "Change your delivery address for pending orders",
                "icon": "🏠",
                "estimated_time": "3 minutes",
                "priority": 3
            },
            ActionType.CONTACT_SUPPORT: {
                "label": "Contact Support",
                "description": "Speak with a customer service representative",
                "icon": "💬",
                "estimated_time": "5-10 minutes",
                "priority": 4
            },
            ActionType.ESCALATE_ISSUE: {
                "label": "Escalate to Manager",
                "description": "Request to speak with a supervisor or manager",
                "icon": "⬆️",
                "estimated_time": "10-15 minutes",
                "priority": 1
            },
            ActionType.VIEW_ACCOUNT: {
                "label": "View Account",
                "description": "Access your account dashboard and order history",
                "icon": "👤",
                "estimated_time": "1 minute",
                "priority": 5
            },
            ActionType.REORDER: {
                "label": "Reorder Items",
                "description": "Quickly reorder items from your previous purchases",
                "icon": "🔄",
                "estimated_time": "2 minutes",
                "priority": 3
            },
            ActionType.LEAVE_REVIEW: {
                "label": "Leave Review",
                "description": "Share your experience and help other customers",
                "icon": "⭐",
                "estimated_time": "3 minutes",
                "priority": 5
            }
        }
    
    def generate_contextual_actions(
        self,
        customer_message: str,
        customer_info: Optional[Dict] = None,
        sentiment_analysis: Optional[Any] = None,
        max_actions: int = 4
    ) -> List[ActionLink]:
        """
        Generate contextual action links based on customer message and context.
        
        Args:
            customer_message: Customer's message
            customer_info: Customer information
            sentiment_analysis: Sentiment analysis result
            max_actions: Maximum number of actions to return
            
        Returns:
            List of relevant action links
        """
        message_lower = customer_message.lower()
        actions = []
        
        # Analyze message for action keywords
        action_keywords = {
            ActionType.TRACK_ORDER: ['track', 'tracking', 'where is', 'status', 'shipped', 'delivery'],
            ActionType.REQUEST_REFUND: ['refund', 'money back', 'return money', 'get refund'],
            ActionType.RETURN_ITEM: ['return', 'send back', 'don\'t want', 'wrong item'],
            ActionType.CANCEL_ORDER: ['cancel', 'don\'t want', 'stop order', 'cancel order'],
            ActionType.UPDATE_ADDRESS: ['address', 'wrong address', 'move', 'delivery address'],
            ActionType.CONTACT_SUPPORT: ['speak to', 'talk to', 'contact', 'help'],
            ActionType.ESCALATE_ISSUE: ['manager', 'supervisor', 'escalate', 'complaint'],
            ActionType.VIEW_ACCOUNT: ['account', 'profile', 'history', 'orders'],
            ActionType.REORDER: ['reorder', 'order again', 'buy again', 'same order'],
            ActionType.LEAVE_REVIEW: ['review', 'feedback', 'rate', 'experience']
        }
        
        # Score actions based on message content
        action_scores = {}
        for action_type, keywords in action_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                action_scores[action_type] = score
        
        # Add high-priority actions based on sentiment
        if sentiment_analysis:
            if hasattr(sentiment_analysis, 'escalation_level'):
                from .sentiment_analyzer import EscalationLevel
                if sentiment_analysis.escalation_level in [EscalationLevel.ESCALATE, EscalationLevel.URGENT_ESCALATE]:
                    action_scores[ActionType.ESCALATE_ISSUE] = action_scores.get(ActionType.ESCALATE_ISSUE, 0) + 3
                    action_scores[ActionType.CONTACT_SUPPORT] = action_scores.get(ActionType.CONTACT_SUPPORT, 0) + 2
            
            if hasattr(sentiment_analysis, 'primary_emotion'):
                from .sentiment_analyzer import EmotionalState
                if sentiment_analysis.primary_emotion in [EmotionalState.ANGRY, EmotionalState.FRUSTRATED]:
                    action_scores[ActionType.REQUEST_REFUND] = action_scores.get(ActionType.REQUEST_REFUND, 0) + 1
                    action_scores[ActionType.RETURN_ITEM] = action_scores.get(ActionType.RETURN_ITEM, 0) + 1
        
        # Add default actions based on customer info
        if customer_info:
            recent_orders = customer_info.get('recent_orders', [])
            if recent_orders:
                # Add tracking for recent orders
                for order in recent_orders[:2]:  # Check last 2 orders
                    if order.get('status') in ['processing', 'shipped']:
                        action_scores[ActionType.TRACK_ORDER] = action_scores.get(ActionType.TRACK_ORDER, 0) + 2
                    elif order.get('status') == 'delivered':
                        action_scores[ActionType.LEAVE_REVIEW] = action_scores.get(ActionType.LEAVE_REVIEW, 0) + 1
                        action_scores[ActionType.REORDER] = action_scores.get(ActionType.REORDER, 0) + 1
        
        # Generate action links for scored actions
        for action_type, score in sorted(action_scores.items(), key=lambda x: x[1], reverse=True):
            if len(actions) >= max_actions:
                break
            
            action_link = self._create_action_link(action_type, customer_info)
            if action_link:
                actions.append(action_link)
        
        # Add default actions if we don't have enough
        if len(actions) < 2:
            default_actions = [ActionType.VIEW_ACCOUNT, ActionType.CONTACT_SUPPORT]
            for action_type in default_actions:
                if action_type not in [a.action_type for a in actions] and len(actions) < max_actions:
                    action_link = self._create_action_link(action_type, customer_info)
                    if action_link:
                        actions.append(action_link)
        
        self.logger.info(
            "Generated contextual actions",
            action_count=len(actions),
            action_types=[a.action_type.value for a in actions]
        )
        
        return actions
    
    def _create_action_link(self, action_type: ActionType, customer_info: Optional[Dict] = None) -> Optional[ActionLink]:
        """Create an action link for the specified action type."""
        if action_type not in self.action_templates:
            return None
        
        template = self.action_templates[action_type]
        customer_id = customer_info.get('customer_id', 'guest') if customer_info else 'guest'
        
        # Generate URL based on action type
        url_mapping = {
            ActionType.TRACK_ORDER: f"{self.base_url}/track-order?customer_id={customer_id}",
            ActionType.REQUEST_REFUND: f"{self.base_url}/refund-request?customer_id={customer_id}",
            ActionType.RETURN_ITEM: f"{self.base_url}/return-item?customer_id={customer_id}",
            ActionType.CANCEL_ORDER: f"{self.base_url}/cancel-order?customer_id={customer_id}",
            ActionType.UPDATE_ADDRESS: f"{self.base_url}/update-address?customer_id={customer_id}",
            ActionType.CONTACT_SUPPORT: f"{self.base_url}/contact-support?customer_id={customer_id}",
            ActionType.ESCALATE_ISSUE: f"{self.base_url}/escalate?customer_id={customer_id}",
            ActionType.VIEW_ACCOUNT: f"{self.base_url}/account?customer_id={customer_id}",
            ActionType.REORDER: f"{self.base_url}/reorder?customer_id={customer_id}",
            ActionType.LEAVE_REVIEW: f"{self.base_url}/leave-review?customer_id={customer_id}"
        }
        
        # Add order-specific parameters if available
        if customer_info and customer_info.get('recent_orders'):
            recent_order = customer_info['recent_orders'][0]
            order_id = recent_order.get('order_id')
            
            if order_id and action_type in [ActionType.TRACK_ORDER, ActionType.RETURN_ITEM, ActionType.CANCEL_ORDER]:
                url_mapping[action_type] += f"&order_id={order_id}"
        
        return ActionLink(
            action_type=action_type,
            label=template["label"],
            url=url_mapping.get(action_type, f"{self.base_url}/support"),
            description=template["description"],
            priority=template["priority"],
            icon=template["icon"],
            estimated_time=template["estimated_time"]
        )
    
    def format_actions_for_response(self, actions: List[ActionLink]) -> str:
        """Format action links for inclusion in response text."""
        if not actions:
            return ""
        
        formatted_actions = []
        for action in actions:
            formatted_actions.append(
                f"{action.icon} **{action.label}**: {action.description} "
                f"[Click here]({action.url}) (Takes about {action.estimated_time})"
            )
        
        return "\n\n**Quick Actions:**\n" + "\n".join(formatted_actions)
    
    def get_action_buttons_html(self, actions: List[ActionLink]) -> str:
        """Generate HTML for action buttons (for web interface)."""
        if not actions:
            return ""
        
        buttons_html = ['<div class="action-buttons">']
        
        for action in actions:
            button_class = f"action-btn priority-{action.priority}"
            buttons_html.append(
                f'<a href="{action.url}" class="{button_class}" title="{action.description}">'
                f'{action.icon} {action.label}</a>'
            )
        
        buttons_html.append('</div>')
        
        return "\n".join(buttons_html)
