"""
Multilingual Service for Natural Conversation Flow

This service provides language detection, translation, and multilingual
conversation support including Shona and other African languages.
"""

import re
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import structlog

try:
    from google.cloud import translate_v2 as translate
    from langdetect import detect, detect_langs
    TRANSLATION_AVAILABLE = True
except ImportError:
    TRANSLATION_AVAILABLE = False


class SupportedLanguage(Enum):
    """Supported languages for multilingual conversation."""
    ENGLISH = "en"
    SHONA = "sn"
    NDEBELE = "nd"
    AFRIKAANS = "af"
    ZULU = "zu"
    XHOSA = "xh"
    SWAHILI = "sw"
    FRENCH = "fr"
    PORTUGUESE = "pt"
    SPANISH = "es"
    GERMAN = "de"
    ITALIAN = "it"
    DUTCH = "nl"
    ARABIC = "ar"
    CHINESE = "zh"
    JAPANESE = "ja"
    KOREAN = "ko"
    HINDI = "hi"
    RUSSIAN = "ru"


@dataclass
class LanguageDetectionResult:
    """Result of language detection."""
    detected_language: str
    confidence: float
    is_supported: bool
    needs_translation: bool
    alternative_languages: List[Tuple[str, float]]


@dataclass
class TranslationResult:
    """Result of text translation."""
    original_text: str
    translated_text: str
    source_language: str
    target_language: str
    confidence: float
    translation_method: str


class MultilingualService:
    """Service for handling multilingual conversations."""
    
    def __init__(self):
        """Initialize multilingual service."""
        self.logger = structlog.get_logger().bind(component="multilingual_service")
        
        # Initialize translation client
        if TRANSLATION_AVAILABLE:
            try:
                self.translate_client = translate.Client()
                self.logger.info("Google Translate client initialized")
            except Exception as e:
                self.translate_client = None
                self.logger.warning("Google Translate not available", error=str(e))
        else:
            self.translate_client = None
            self.logger.warning("Translation libraries not available")
        
        # Language patterns for basic detection
        self.language_patterns = {
            "sn": [  # Shona patterns
                r'\b(mhoro|mangwanani|masikati|manheru)\b',  # greetings
                r'\b(ndiri|uri|ari|tiri|muri|vari)\b',  # to be verbs
                r'\b(kana|asi|nekuti|saka)\b',  # conjunctions
                r'\b(ndoda|ndinoda|tinoda)\b',  # want/need
                r'\b(zvakanaka|zvisina|hapana)\b',  # good/bad/nothing
                r'\b(mukoma|mukadzi|mwana)\b',  # family terms
                r'\b(basa|mari|chikafu)\b',  # work/money/food
            ],
            "nd": [  # Ndebele patterns
                r'\b(sawubona|kunjani|ngiyabonga)\b',  # greetings/thanks
                r'\b(ngiya|uya|siya|baya)\b',  # verb patterns
                r'\b(kodwa|futhi|noma)\b',  # conjunctions
            ],
            "zu": [  # Zulu patterns
                r'\b(sawubona|sanibonani|ngiyabonga)\b',
                r'\b(ngiya|uya|siya|baya)\b',
                r'\b(kodwa|futhi|noma|ukuthi)\b',
            ],
            "xh": [  # Xhosa patterns
                r'\b(molo|molweni|enkosi)\b',
                r'\b(ndiya|uya|siya|baya)\b',
                r'\b(kodwa|kwaye|okanye)\b',
            ],
            "sw": [  # Swahili patterns
                r'\b(hujambo|habari|asante|karibu)\b',
                r'\b(nina|una|ana|tuna|mna|wana)\b',
                r'\b(lakini|na|au|kwa)\b',
            ],
            "af": [  # Afrikaans patterns
                r'\b(hallo|goeiemore|dankie|totsiens)\b',
                r'\b(is|was|het|sal|kan|moet)\b',
                r'\b(en|of|maar|want)\b',
                r'\b(hoeveel|wat|waar|wanneer|hoe|kan|wil)\b',  # question words
                r'\b(skuld|ek|jy|ons|hulle)\b',  # debt and pronouns
            ]
        }
        
        # Multilingual response templates
        self.greeting_templates = {
            "en": [
                "Hello! How can I help you today?",
                "Hi there! I'm here to assist you.",
                "Good day! What can I do for you?"
            ],
            "sn": [
                "Mhoro! Ndingakubatsira sei nhasi?",
                "Mangwanani! Ndiri pano kuti ndikubatsire.",
                "Zuva rakanaka! Chii chandinogona kukuitira?"
            ],
            "nd": [
                "Sawubona! Ngingakusiza njani namuhla?",
                "Kunjani! Ngilapha ukukusiza.",
                "Ilanga elihle! Yini engingayenzela wena?"
            ],
            "zu": [
                "Sawubona! Ngingakusiza kanjani namuhla?",
                "Sanibonani! Ngilapha ukukusiza.",
                "Usuku oluhle! Yini engingayenzela wena?"
            ],
            "xh": [
                "Molo! Ndingakunceda njani namhlanje?",
                "Molweni! Ndilapha ukukunceda.",
                "Usuku oluhle! Yintoni endinokuyenzela wena?"
            ],
            "sw": [
                "Hujambo! Ninaweza kukusaidia vipi leo?",
                "Habari! Nipo hapa kukusaidia.",
                "Siku njema! Ni nini ninaweza kukufanyia?"
            ],
            "af": [
                "Hallo! Hoe kan ek jou vandag help?",
                "Goeiemore! Ek is hier om jou te help.",
                "Goeie dag! Wat kan ek vir jou doen?"
            ],
            "fr": [
                "Bonjour! Comment puis-je vous aider aujourd'hui?",
                "Salut! Je suis là pour vous assister.",
                "Bonne journée! Que puis-je faire pour vous?"
            ],
            "pt": [
                "Olá! Como posso ajudá-lo hoje?",
                "Oi! Estou aqui para ajudá-lo.",
                "Bom dia! O que posso fazer por você?"
            ],
            "es": [
                "¡Hola! ¿Cómo puedo ayudarte hoy?",
                "¡Hola! Estoy aquí para asistirte.",
                "¡Buen día! ¿Qué puedo hacer por ti?"
            ]
        }
        
        # Language-specific empathy phrases
        self.empathy_phrases = {
            "en": [
                "I understand how frustrating this must be.",
                "I'm sorry you're experiencing this issue.",
                "I can see why this would be concerning."
            ],
            "sn": [
                "Ndinonzwisisa kuti izvi zvinogona kunge zvichitsamwisa.",
                "Ndine urombo nekuti uri kuona dambudziko iri.",
                "Ndinoona kuti izvi zvinogona kunge zvichikufungaidza."
            ],
            "nd": [
                "Ngiyaqonda ukuthi lokhu kungaba yikucasula.",
                "Ngiyaxolisa ngalenkinga oyihlangabezanayo.",
                "Ngiyabona ukuthi lokhu kungaba yinto ekhathazayo."
            ],
            "zu": [
                "Ngiyaqonda ukuthi lokhu kungaba yikucasula.",
                "Ngiyaxolisa ngalenkinga oyihlangabezanayo.",
                "Ngiyabona ukuthi lokhu kungaba yinto ekhathazayo."
            ],
            "xh": [
                "Ndiyaqonda ukuba oku kunokuba yinto ecaphukisayo.",
                "Ndiyaxolisa ngale ngxaki oyifumanayo.",
                "Ndiyabona ukuba oku kunokuba yinto exhalabisayo."
            ],
            "sw": [
                "Naelewa jinsi hii inavyoweza kuwa ya kuchukiza.",
                "Naomba radhi kwa tatizo hili unalokabiliana nalo.",
                "Naona jinsi hii inavyoweza kuwa ya kutatanisha."
            ],
            "af": [
                "Ek verstaan hoe frustrerend dit moet wees.",
                "Ek is jammer jy ervaar hierdie probleem.",
                "Ek kan sien hoekom dit kommerwekkend sou wees."
            ]
        }
        
        # Business intent phrases in different languages
        self.business_phrases = {
            "parcel_tracking": {
                "en": ["track", "package", "delivery", "shipment", "order"],
                "sn": ["kutevera", "chikwama", "kuendesa", "kurodha", "order"],
                "nd": ["ukulandelela", "iphakeji", "ukulethwa", "ukuthunyelwa"],
                "zu": ["ukulandelela", "iphakeji", "ukulethwa", "ukuthunyelwa"],
                "xh": ["ukulandelela", "iphakeji", "ukuhanjiswa", "ukuthunyelwa"],
                "sw": ["kufuatilia", "kifurushi", "utoaji", "utumaji"],
                "af": ["volg", "pakkie", "aflewering", "versending"]
            },
            "payment": {
                "en": ["payment", "bill", "charge", "money", "cost", "pay", "invoice"],
                "sn": ["kubhadhara", "bhiri", "mari", "mutengo"],
                "nd": ["ukukhokhela", "ibhili", "imali", "intengo"],
                "zu": ["ukukhokhela", "ibhili", "imali", "intengo"],
                "xh": ["ukuhlawula", "ityala", "imali", "ixabiso"],
                "sw": ["malipo", "bili", "pesa", "gharama"],
                "af": ["betaling", "rekening", "geld", "koste", "betaal"]
            },
            "account_inquiry": {
                "en": ["balance", "owe", "debt", "due", "account", "statement", "how much"],
                "sn": ["chikwereti", "mari", "account", "zvakakwana"],
                "nd": ["isikweletu", "imali", "i-account", "okwanele"],
                "zu": ["isikweletu", "imali", "i-account", "okwanele"],
                "xh": ["ityala", "imali", "i-account", "okwanele"],
                "sw": ["deni", "pesa", "akaunti", "kiasi"],
                "af": ["skuld", "balans", "verskuldig", "hoeveel skuld", "rekening", "staat", "hoeveel"]
            }
        }

        # Balance/debt response templates in different languages
        self.balance_response_templates = {
            "en": {
                "with_balance": "You owe R{amount:.2f}.",
                "no_balance": "You have no outstanding balance.",
                "error": "I couldn't retrieve your balance information right now."
            },
            "af": {
                "with_balance": "Jy skuld R{amount:.2f}.",
                "no_balance": "Jy het geen uitstaande skuld nie.",
                "error": "Ek kon nie jou balans inligting kry nie."
            },
            "zu": {
                "with_balance": "Unesikweletu se-R{amount:.2f}.",
                "no_balance": "Awunaso isikweletu esisele.",
                "error": "Angikwazanga ukuthola ulwazi lwakho lwesikweletu."
            },
            "xh": {
                "with_balance": "Unetyala le-R{amount:.2f}.",
                "no_balance": "Awunalo ityala liseleyo.",
                "error": "Andikwazanga ukufumana ulwazi lwakho lwetyala."
            }
        }
    
    async def detect_language(self, text: str) -> LanguageDetectionResult:
        """Detect the language of input text."""
        try:
            # First try pattern-based detection for African languages
            pattern_result = self._detect_by_patterns(text)
            if pattern_result:
                return pattern_result
            
            # Use langdetect library if available
            if TRANSLATION_AVAILABLE:
                try:
                    detected_langs = detect_langs(text)
                    primary_lang = detected_langs[0]
                    
                    alternatives = [(lang.lang, lang.prob) for lang in detected_langs[1:3]]
                    
                    is_supported = primary_lang.lang in [lang.value for lang in SupportedLanguage]
                    needs_translation = primary_lang.lang != "en"
                    
                    return LanguageDetectionResult(
                        detected_language=primary_lang.lang,
                        confidence=primary_lang.prob,
                        is_supported=is_supported,
                        needs_translation=needs_translation,
                        alternative_languages=alternatives
                    )
                except Exception as e:
                    self.logger.warning("Language detection failed", error=str(e))
            
            # Fallback to English
            return LanguageDetectionResult(
                detected_language="en",
                confidence=0.5,
                is_supported=True,
                needs_translation=False,
                alternative_languages=[]
            )
            
        except Exception as e:
            self.logger.error("Language detection error", error=str(e))
            return LanguageDetectionResult(
                detected_language="en",
                confidence=0.1,
                is_supported=True,
                needs_translation=False,
                alternative_languages=[]
            )
    
    def _detect_by_patterns(self, text: str) -> Optional[LanguageDetectionResult]:
        """Detect language using pattern matching for African languages."""
        text_lower = text.lower()
        language_scores = {}
        
        for lang_code, patterns in self.language_patterns.items():
            score = 0
            matches = 0
            
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    matches += 1
                    score += 1
            
            if matches > 0:
                # Calculate confidence based on matches and text length
                confidence = min(0.95, (matches * 0.3) + (score / len(text.split())) * 0.7)
                language_scores[lang_code] = confidence
        
        if language_scores:
            # Get the language with highest score
            best_lang = max(language_scores.items(), key=lambda x: x[1])
            
            if best_lang[1] > 0.3:  # Minimum confidence threshold
                alternatives = [(lang, score) for lang, score in language_scores.items() 
                              if lang != best_lang[0]][:2]
                
                return LanguageDetectionResult(
                    detected_language=best_lang[0],
                    confidence=best_lang[1],
                    is_supported=True,
                    needs_translation=True,
                    alternative_languages=alternatives
                )
        
        return None
    
    async def translate_text(
        self, 
        text: str, 
        target_language: str = "en", 
        source_language: Optional[str] = None
    ) -> TranslationResult:
        """Translate text to target language."""
        try:
            if self.translate_client and TRANSLATION_AVAILABLE:
                # Use Google Translate
                result = self.translate_client.translate(
                    text,
                    target_language=target_language,
                    source_language=source_language
                )
                
                return TranslationResult(
                    original_text=text,
                    translated_text=result['translatedText'],
                    source_language=result.get('detectedSourceLanguage', source_language or 'unknown'),
                    target_language=target_language,
                    confidence=0.9,
                    translation_method="google_translate"
                )
            else:
                # Fallback to basic phrase translation
                return await self._basic_phrase_translation(text, target_language, source_language)
                
        except Exception as e:
            self.logger.error("Translation failed", error=str(e))
            return TranslationResult(
                original_text=text,
                translated_text=text,  # Return original if translation fails
                source_language=source_language or "unknown",
                target_language=target_language,
                confidence=0.1,
                translation_method="fallback"
            )
    
    async def _basic_phrase_translation(
        self, 
        text: str, 
        target_language: str, 
        source_language: Optional[str]
    ) -> TranslationResult:
        """Basic phrase-based translation for common expressions."""
        # Simple phrase mapping for common greetings and expressions
        phrase_mappings = {
            ("sn", "en"): {
                "mhoro": "hello",
                "mangwanani": "good morning",
                "masikati": "good afternoon",
                "manheru": "good evening",
                "zvakanaka": "good/fine",
                "ndoda": "I want",
                "ndinoda": "I need",
                "ndine dambudziko": "I have a problem",
                "ndiri kutsvaga": "I am looking for"
            },
            ("en", "sn"): {
                "hello": "mhoro",
                "good morning": "mangwanani",
                "good afternoon": "masikati",
                "good evening": "manheru",
                "thank you": "maita basa",
                "please": "ndapota",
                "I understand": "ndinonzwisisa",
                "how can I help": "ndingakubatsira sei"
            }
        }
        
        if source_language and (source_language, target_language) in phrase_mappings:
            mapping = phrase_mappings[(source_language, target_language)]
            translated = text.lower()
            
            for source_phrase, target_phrase in mapping.items():
                translated = translated.replace(source_phrase, target_phrase)
            
            return TranslationResult(
                original_text=text,
                translated_text=translated,
                source_language=source_language,
                target_language=target_language,
                confidence=0.7,
                translation_method="phrase_mapping"
            )
        
        return TranslationResult(
            original_text=text,
            translated_text=text,
            source_language=source_language or "unknown",
            target_language=target_language,
            confidence=0.1,
            translation_method="no_translation"
        )
    
    def get_localized_greeting(self, language: str) -> str:
        """Get a greeting in the specified language."""
        templates = self.greeting_templates.get(language, self.greeting_templates["en"])
        return templates[0]  # Return first template
    
    def get_localized_empathy(self, language: str) -> str:
        """Get an empathy phrase in the specified language."""
        phrases = self.empathy_phrases.get(language, self.empathy_phrases["en"])
        return phrases[0]  # Return first phrase

    def get_localized_balance_response(self, language: str, balance_amount: float) -> str:
        """Get a balance response in the specified language."""
        templates = self.balance_response_templates.get(language, self.balance_response_templates["en"])

        if balance_amount > 0:
            return templates["with_balance"].format(amount=balance_amount)
        else:
            return templates["no_balance"]
    
    def detect_business_intent_multilingual(self, text: str, language: str) -> Optional[str]:
        """Detect business intent in multiple languages."""
        text_lower = text.lower()
        
        for intent, lang_phrases in self.business_phrases.items():
            phrases = lang_phrases.get(language, lang_phrases.get("en", []))
            
            for phrase in phrases:
                if phrase in text_lower:
                    return intent
        
        return None
    
    async def process_multilingual_message(
        self, 
        message: str, 
        customer_language_preference: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process a message with full multilingual support."""
        # Detect language
        detection_result = await self.detect_language(message)
        
        # Translate to English for processing if needed
        translation_result = None
        processed_message = message
        
        if detection_result.needs_translation:
            translation_result = await self.translate_text(
                message, 
                target_language="en",
                source_language=detection_result.detected_language
            )
            processed_message = translation_result.translated_text
        
        # Detect business intent in original language
        business_intent = self.detect_business_intent_multilingual(
            message, 
            detection_result.detected_language
        )
        
        return {
            "original_message": message,
            "processed_message": processed_message,
            "language_detection": detection_result,
            "translation": translation_result,
            "business_intent": business_intent,
            "response_language": customer_language_preference or detection_result.detected_language
        }


# Global multilingual service instance
multilingual_service = MultilingualService()
