"""
Conversation Phase Detection Service

This service analyzes customer messages and conversation context to determine
the appropriate conversation phase and when to transition between phases.
"""

from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import re
import structlog
from datetime import datetime

from .natural_conversation_flow import ConversationPhase, BusinessIntent, ConversationContext

logger = structlog.get_logger(__name__)


class MessageType(Enum):
    """Types of customer messages."""
    GREETING = "greeting"
    CASUAL_RESPONSE = "casual_response"
    BUSINESS_INQUIRY = "business_inquiry"
    COMPLAINT = "complaint"
    COMPLIMENT = "compliment"
    QUESTION = "question"
    CONFIRMATION = "confirmation"
    GOODBYE = "goodbye"


class ConversationPhaseDetector:
    """Detects conversation phases and transition points."""
    
    def __init__(self):
        """Initialize the conversation phase detector."""
        self.logger = structlog.get_logger(__name__)
        
        # Message classification patterns
        self.message_patterns = {
            MessageType.GREETING: [
                r'\b(hello|hi|hey|good\s+(morning|afternoon|evening)|greetings)\b',
                r'\bhow\s+are\s+you\b',
                r'\bnice\s+to\s+(meet|talk|speak)\b'
            ],
            MessageType.CASUAL_RESPONSE: [
                r'\b(good|great|fine|well|okay|alright|not\s+bad)\b',
                r'\b(thanks|thank\s+you|appreciate)\b',
                r'\b(weather|day|weekend|week)\b',
                r'\b(busy|tired|excited|happy|sad)\b'
            ],
            MessageType.BUSINESS_INQUIRY: [
                r'\b(order|package|delivery|shipping|tracking)\b',
                r'\b(payment|bill|charge|refund|money|cost|price)\b',
                r'\b(account|profile|subscription|service)\b',
                r'\b(status|update|information|details)\b',
                # Multilingual patterns for business inquiries
                r'\b(skuld|balans|verskuldig|hoeveel)\b',  # Afrikaans debt/balance
                r'\b(rekening|betaling|geld)\b',  # Afrikaans payment terms
                r'\b(ityala|imali|ukuhlawula)\b',  # Xhosa debt/money/payment
                r'\b(isikweletu|ukukhokhela)\b',  # Zulu/Ndebele debt/payment
            ],
            MessageType.COMPLAINT: [
                r'\b(problem|issue|trouble|error|wrong|broken)\b',
                r'\b(angry|frustrated|upset|disappointed|terrible)\b',
                r'\b(not\s+working|doesn\'t\s+work|failed|failure)\b',
                r'\b(complaint|complain|dissatisfied)\b'
            ],
            MessageType.COMPLIMENT: [
                r'\b(excellent|amazing|wonderful|fantastic|great\s+job)\b',
                r'\b(love|like|satisfied|happy\s+with|pleased)\b',
                r'\b(thank\s+you|thanks|appreciate|grateful)\b'
            ],
            MessageType.QUESTION: [
                r'\b(what|when|where|why|how|can\s+you|could\s+you|would\s+you)\b',
                r'\b(is\s+it|are\s+you|do\s+you|will\s+you)\b',
                r'\?'
            ],
            MessageType.CONFIRMATION: [
                r'\b(yes|yeah|yep|sure|okay|ok|alright|correct|right)\b',
                r'\b(no|nope|not\s+really|don\'t\s+think\s+so)\b',
                r'\b(that\'s\s+right|exactly|precisely)\b'
            ],
            MessageType.GOODBYE: [
                r'\b(bye|goodbye|see\s+you|talk\s+later|have\s+a\s+good)\b',
                r'\b(thanks\s+for\s+your\s+help|that\'s\s+all|nothing\s+else)\b'
            ]
        }
        
        # Business intent patterns
        self.business_intent_patterns = {
            BusinessIntent.PARCEL_TRACKING: [
                r'\b(track|tracking|package|delivery|shipment|shipping)\b',
                r'\b(where\s+is\s+my|when\s+will\s+it\s+arrive|delivery\s+status)\b'
            ],
            BusinessIntent.PAYMENT_REMINDER: [
                r'\b(payment|bill|invoice|balance|owe|due|overdue)\b',
                r'\b(pay|charge|charged|cost|price|fee)\b',
                # Multilingual debt/balance inquiry patterns
                r'\b(skuld|balans|verskuldig|hoeveel skuld)\b',  # Afrikaans
                r'\b(ityala|imali|ukuhlawula)\b',  # Xhosa
                r'\b(isikweletu|ukukhokhela)\b',  # Zulu/Ndebele
                r'\b(deni|pesa|malipo)\b',  # Swahili
            ],
            BusinessIntent.ORDER_MANAGEMENT: [
                r'\b(order|purchase|bought|buy|item|product)\b',
                r'\b(cancel|change|modify|update|return)\b'
            ],
            BusinessIntent.ACCOUNT_INQUIRY: [
                r'\b(account|profile|login|password|settings)\b',
                r'\b(information|details|update|change)\b'
            ],
            BusinessIntent.SUPPORT_ISSUE: [
                r'\b(help|support|problem|issue|trouble|error)\b',
                r'\b(not\s+working|broken|failed|wrong)\b'
            ]
        }
        
        # Sentiment indicators
        self.sentiment_patterns = {
            "positive": [
                r'\b(good|great|excellent|wonderful|amazing|fantastic|love|like|happy|pleased|satisfied)\b'
            ],
            "negative": [
                r'\b(bad|terrible|awful|horrible|hate|angry|frustrated|upset|disappointed|problem|issue)\b'
            ],
            "neutral": [
                r'\b(okay|ok|fine|alright|normal|average)\b'
            ]
        }
    
    def classify_message(self, message: str) -> List[MessageType]:
        """Classify a customer message into one or more types."""
        message_lower = message.lower()
        classifications = []
        
        for message_type, patterns in self.message_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message_lower, re.IGNORECASE):
                    classifications.append(message_type)
                    break
        
        # If no classification found, default to casual response
        if not classifications:
            classifications.append(MessageType.CASUAL_RESPONSE)
        
        return classifications
    
    def detect_business_intent(self, message: str) -> Optional[BusinessIntent]:
        """Detect business intent from customer message."""
        message_lower = message.lower()
        
        for intent, patterns in self.business_intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message_lower, re.IGNORECASE):
                    return intent
        
        return None
    
    def analyze_sentiment(self, message: str) -> str:
        """Analyze sentiment of customer message."""
        message_lower = message.lower()
        
        for sentiment, patterns in self.sentiment_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message_lower, re.IGNORECASE):
                    return sentiment
        
        return "neutral"
    
    def should_transition_phase(
        self,
        current_context: ConversationContext,
        message_classifications: List[MessageType],
        detected_intent: Optional[BusinessIntent]
    ) -> Tuple[bool, Optional[ConversationPhase]]:
        """Determine if conversation should transition to a new phase."""
        
        current_phase = current_context.current_phase
        
        # Direct business inquiry should immediately transition to business focus
        if MessageType.BUSINESS_INQUIRY in message_classifications or detected_intent:
            if current_phase != ConversationPhase.BUSINESS_FOCUS:
                return True, ConversationPhase.BUSINESS_FOCUS
        
        # Complaint should transition to business focus with empathy
        if MessageType.COMPLAINT in message_classifications:
            if current_phase != ConversationPhase.BUSINESS_FOCUS:
                return True, ConversationPhase.BUSINESS_FOCUS
        
        # Goodbye should transition to closing
        if MessageType.GOODBYE in message_classifications:
            return True, ConversationPhase.CLOSING
        
        # Phase-specific transition logic
        if current_phase == ConversationPhase.GREETING:
            # Always move to casual chat after greeting
            return True, ConversationPhase.CASUAL_CHAT
        
        elif current_phase == ConversationPhase.CASUAL_CHAT:
            # Transition to business after enough casual turns or high readiness
            if (current_context.turns_in_phase >= 2 and current_context.transition_readiness >= 0.7) or \
               (current_context.turns_in_phase >= 3):
                return True, ConversationPhase.TRANSITION
        
        elif current_phase == ConversationPhase.TRANSITION:
            # Always move to business focus after transition
            return True, ConversationPhase.BUSINESS_FOCUS
        
        elif current_phase == ConversationPhase.BUSINESS_FOCUS:
            # Stay in business focus unless customer indicates completion
            if MessageType.CONFIRMATION in message_classifications and \
               any(word in current_context.pending_business_tasks for word in ["completed", "resolved"]):
                return True, ConversationPhase.RESOLUTION
        
        return False, None
    
    def calculate_transition_readiness(
        self,
        context: ConversationContext,
        message: str,
        message_classifications: List[MessageType]
    ) -> float:
        """Calculate how ready the conversation is to transition to business."""
        readiness = 0.0
        
        # Base readiness from turn count
        readiness += min(0.3, context.total_turns * 0.1)
        
        # Positive sentiment increases readiness
        sentiment = self.analyze_sentiment(message)
        if sentiment == "positive":
            readiness += 0.3
        elif sentiment == "neutral":
            readiness += 0.2
        
        # Business-related messages increase readiness
        if MessageType.BUSINESS_INQUIRY in message_classifications:
            readiness += 0.5
        elif MessageType.QUESTION in message_classifications:
            readiness += 0.2
        
        # Pending business tasks increase readiness
        if context.pending_business_tasks:
            readiness += 0.2
        
        # Time in current phase affects readiness
        if context.current_phase == ConversationPhase.CASUAL_CHAT:
            readiness += min(0.3, context.turns_in_phase * 0.15)
        
        return min(1.0, readiness)
    
    def get_recommended_response_style(
        self,
        context: ConversationContext,
        message_classifications: List[MessageType],
        sentiment: str
    ) -> Dict[str, Any]:
        """Get recommended response style based on analysis."""
        
        style = {
            "tone": "friendly",
            "empathy_level": 0.5,
            "formality": "casual",
            "urgency": "low"
        }
        
        # Adjust based on message type
        if MessageType.COMPLAINT in message_classifications:
            style.update({
                "tone": "empathetic",
                "empathy_level": 0.9,
                "formality": "professional",
                "urgency": "high"
            })
        elif MessageType.COMPLIMENT in message_classifications:
            style.update({
                "tone": "appreciative",
                "empathy_level": 0.7,
                "formality": "warm"
            })
        elif MessageType.BUSINESS_INQUIRY in message_classifications:
            style.update({
                "tone": "helpful",
                "formality": "professional",
                "urgency": "medium"
            })
        
        # Adjust based on sentiment
        if sentiment == "negative":
            style["empathy_level"] = min(1.0, style["empathy_level"] + 0.3)
            style["urgency"] = "high"
        elif sentiment == "positive":
            style["tone"] = "enthusiastic"
        
        # Adjust based on conversation phase
        if context.current_phase == ConversationPhase.BUSINESS_FOCUS:
            style["formality"] = "professional"
        elif context.current_phase == ConversationPhase.CASUAL_CHAT:
            style["formality"] = "casual"
        
        return style
    
    def analyze_conversation_turn(
        self,
        context: ConversationContext,
        customer_message: str
    ) -> Dict[str, Any]:
        """Perform comprehensive analysis of a conversation turn."""
        
        # Classify message
        classifications = self.classify_message(customer_message)
        
        # Detect business intent
        business_intent = self.detect_business_intent(customer_message)
        
        # Analyze sentiment
        sentiment = self.analyze_sentiment(customer_message)
        
        # Calculate transition readiness
        transition_readiness = self.calculate_transition_readiness(
            context, customer_message, classifications
        )
        
        # Check if phase should transition
        should_transition, new_phase = self.should_transition_phase(
            context, classifications, business_intent
        )
        
        # Get response style recommendations
        response_style = self.get_recommended_response_style(
            context, classifications, sentiment
        )
        
        return {
            "message_classifications": [c.value for c in classifications],
            "business_intent": business_intent.value if business_intent else None,
            "sentiment": sentiment,
            "transition_readiness": transition_readiness,
            "should_transition": should_transition,
            "recommended_phase": new_phase.value if new_phase else None,
            "response_style": response_style,
            "analysis_timestamp": datetime.utcnow().isoformat()
        }
