"""
Enhanced Response Generator with Natural Conversation Flow

This service generates responses that incorporate natural conversation patterns,
smooth transitions from casual to business dialogue, and context-aware responses.
"""

from typing import Dict, List, Optional, Any, Tuple
import structlog
from datetime import datetime

from .natural_conversation_flow import NaturalConversationFlow, ConversationContext, BusinessIntent
from .conversation_phase_detector import ConversationPhaseDetector
from .sentiment_analyzer import Sentiment<PERSON>nal<PERSON><PERSON>
from .empathetic_response_generator import EmpatheticResponseGenerator
from .order_history_summarizer import OrderHistorySummarizer
from .action_link_generator import ActionLinkGenerator
from .customer_database import customer_db

logger = structlog.get_logger(__name__)


class EnhancedResponseGenerator:
    """Generates enhanced responses with natural conversation flow."""
    
    def __init__(self):
        """Initialize the enhanced response generator."""
        self.logger = structlog.get_logger(__name__)
        
        # Initialize component services
        self.natural_flow = NaturalConversationFlow()
        self.phase_detector = ConversationPhaseDetector()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.empathetic_generator = EmpatheticResponseGenerator()
        self.order_summarizer = OrderHistorySummarizer()
        self.action_generator = ActionLinkGenerator()
    
    async def generate_natural_response(
        self,
        customer_message: str,
        customer_info: Optional[Dict] = None,
        conversation_context: Optional[ConversationContext] = None,
        conversation_id: Optional[str] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Generate a natural response that follows conversational flow patterns.
        
        Args:
            customer_message: The customer's message
            customer_info: Customer information from database
            conversation_context: Current conversation context
            conversation_id: Conversation identifier
            
        Returns:
            Tuple of (response_text, response_metadata)
        """
        
        # Initialize conversation context if not provided
        if not conversation_context and conversation_id:
            conversation_context = await self.natural_flow.initialize_conversation(
                customer_id=customer_info.get("customer_id", "unknown") if customer_info else "unknown",
                conversation_id=conversation_id
            )
        
        # Analyze the conversation turn
        turn_analysis = self.phase_detector.analyze_conversation_turn(
            conversation_context, customer_message
        )
        
        # Determine response strategy based on analysis
        response_strategy = self._determine_response_strategy(
            conversation_context, turn_analysis, customer_info
        )
        
        # Generate response based on strategy
        if response_strategy["use_natural_flow"]:
            response_text = await self._generate_natural_flow_response(
                conversation_context, customer_message, customer_info, turn_analysis
            )
        else:
            response_text = await self._generate_business_focused_response(
                customer_message, customer_info, turn_analysis
            )
        
        # Add empathetic elements if needed
        if turn_analysis["sentiment"] == "negative" or "complaint" in turn_analysis["message_classifications"]:
            response_text = await self._add_empathetic_elements(
                response_text, customer_message, customer_info, turn_analysis
            )
        
        # Generate action links if appropriate
        action_links = []
        if conversation_context and conversation_context.current_phase.value in ["business_focus", "resolution"]:
            action_links = self.action_generator.generate_contextual_actions(
                customer_message, customer_info, turn_analysis, max_actions=3
            )
        
        # Prepare response metadata
        response_metadata = {
            "conversation_phase": conversation_context.current_phase.value if conversation_context else "unknown",
            "turn_analysis": turn_analysis,
            "response_strategy": response_strategy,
            "action_links": action_links,
            "empathy_applied": turn_analysis["response_style"]["empathy_level"],
            "business_intent_detected": turn_analysis["business_intent"],
            "transition_occurred": turn_analysis["should_transition"]
        }
        
        self.logger.info(
            "Generated natural response",
            conversation_id=conversation_id,
            phase=conversation_context.current_phase.value if conversation_context else "unknown",
            strategy=response_strategy["type"],
            sentiment=turn_analysis["sentiment"]
        )
        
        return response_text, response_metadata
    
    def _determine_response_strategy(
        self,
        context: Optional[ConversationContext],
        turn_analysis: Dict[str, Any],
        customer_info: Optional[Dict]
    ) -> Dict[str, Any]:
        """Determine the appropriate response strategy."""
        
        strategy = {
            "type": "natural_flow",
            "use_natural_flow": True,
            "include_business_info": False,
            "empathy_level": turn_analysis["response_style"]["empathy_level"],
            "formality": turn_analysis["response_style"]["formality"]
        }
        
        # Use natural flow with business info for direct business inquiries
        if turn_analysis["business_intent"] or "business_inquiry" in turn_analysis["message_classifications"]:
            strategy.update({
                "type": "business_focused",
                "use_natural_flow": True,  # Keep using natural flow for multilingual support
                "include_business_info": True,
                "formality": "professional"
            })
        
        # Use empathetic strategy for complaints
        if "complaint" in turn_analysis["message_classifications"] or turn_analysis["sentiment"] == "negative":
            strategy.update({
                "type": "empathetic_business",
                "empathy_level": 0.9,
                "formality": "professional"
            })
        
        # Use casual strategy for greetings and casual responses
        if context and context.current_phase.value in ["greeting", "casual_chat"]:
            strategy.update({
                "type": "casual_friendly",
                "formality": "casual"
            })
        
        return strategy
    
    async def _generate_natural_flow_response(
        self,
        context: ConversationContext,
        customer_message: str,
        customer_info: Optional[Dict],
        turn_analysis: Dict[str, Any]
    ) -> str:
        """Generate response using natural conversation flow."""
        
        # Use the natural flow generator
        response, updated_context = await self.natural_flow.generate_response(
            context=context,
            customer_message=customer_message,
            customer_info=customer_info
        )
        
        # Enhance response based on turn analysis
        if turn_analysis["business_intent"] and context.current_phase.value == "business_focus":
            # Skip adding business info if natural flow already handled it (avoid duplication)
            if "R" not in response and "skuld" not in response.lower():
                # Add specific business information
                business_info = await self._get_business_specific_info(
                    turn_analysis["business_intent"], customer_info
                )
                if business_info:
                    response += f" {business_info}"
        
        return response
    
    async def _generate_business_focused_response(
        self,
        customer_message: str,
        customer_info: Optional[Dict],
        turn_analysis: Dict[str, Any]
    ) -> str:
        """Generate a business-focused response."""
        
        business_intent = turn_analysis["business_intent"]
        
        if business_intent == "parcel_tracking":
            return await self._handle_parcel_tracking(customer_message, customer_info)
        elif business_intent == "payment_reminder":
            return await self._handle_payment_inquiry(customer_message, customer_info)
        elif business_intent == "order_management":
            return await self._handle_order_management(customer_message, customer_info)
        elif business_intent == "account_inquiry":
            return await self._handle_account_inquiry(customer_message, customer_info)
        elif business_intent == "support_issue":
            return await self._handle_support_issue(customer_message, customer_info)
        else:
            return "I'd be happy to help you with that. Could you please provide more details about what you need assistance with?"
    
    async def _add_empathetic_elements(
        self,
        base_response: str,
        customer_message: str,
        customer_info: Optional[Dict],
        turn_analysis: Dict[str, Any]
    ) -> str:
        """Add empathetic elements to the response."""
        
        # Analyze sentiment for empathetic response
        sentiment_analysis = await self.sentiment_analyzer.analyze_sentiment(customer_message)
        
        # Generate empathetic response components
        empathetic_response = await self.empathetic_generator.generate_empathetic_response(
            sentiment_analysis=sentiment_analysis,
            customer_message=customer_message,
            customer_info=customer_info
        )
        
        # Combine empathy with base response
        if empathetic_response.get("opening"):
            return f"{empathetic_response['opening']} {base_response}"
        
        return base_response
    
    async def _get_business_specific_info(
        self,
        business_intent: str,
        customer_info: Optional[Dict]
    ) -> Optional[str]:
        """Get specific business information based on intent."""
        
        if not customer_info:
            return None
        
        customer_id = customer_info.get("customer_id")
        if not customer_id:
            return None
        
        try:
            if business_intent == "parcel_tracking":
                # Get recent order/shipping info
                orders = await customer_db.get_customer_orders(customer_id)
                if orders:
                    recent_order = orders[0]  # Most recent
                    return f"I can see your recent order #{recent_order.get('order_id', 'N/A')} is currently {recent_order.get('status', 'being processed')}."
            
            elif business_intent == "payment_reminder":
                # Get account balance info
                account_info = await customer_db.get_customer_info(customer_id)
                if account_info:
                    balance = account_info.get("outstanding_balance", 0)
                    customer_language = customer_info.get("language_preference", "en") if customer_info else "en"

                    # Import multilingual service for localized response
                    from .multilingual_service import multilingual_service
                    return multilingual_service.get_localized_balance_response(customer_language, balance)
            
            elif business_intent == "order_management":
                # Get order history summary
                order_summary = await self.order_summarizer.summarize_order_history(customer_info)
                return order_summary
        
        except Exception as e:
            self.logger.error("Error getting business specific info", error=str(e), intent=business_intent)
        
        return None
    
    async def _handle_parcel_tracking(self, message: str, customer_info: Optional[Dict]) -> str:
        """Handle parcel tracking inquiries."""
        if customer_info:
            customer_name = customer_info.get("first_name", "")
            return f"I'd be happy to help you track your package{', ' + customer_name if customer_name else ''}. Let me check the status of your recent orders for you."
        return "I can help you track your package. Could you please provide your order number or tracking information?"
    
    async def _handle_payment_inquiry(self, message: str, customer_info: Optional[Dict]) -> str:
        """Handle payment-related inquiries."""
        if customer_info:
            customer_name = customer_info.get("first_name", "")
            return f"I can help you with your payment inquiry{', ' + customer_name if customer_name else ''}. Let me review your account details."
        return "I'd be happy to help you with your payment inquiry. Could you please provide your account information?"
    
    async def _handle_order_management(self, message: str, customer_info: Optional[Dict]) -> str:
        """Handle order management requests."""
        if customer_info:
            customer_name = customer_info.get("first_name", "")
            return f"I can assist you with your order{', ' + customer_name if customer_name else ''}. Let me pull up your order history."
        return "I can help you manage your orders. Could you please provide your order number or account details?"
    
    async def _handle_account_inquiry(self, message: str, customer_info: Optional[Dict]) -> str:
        """Handle account-related inquiries."""
        if customer_info:
            customer_name = customer_info.get("first_name", "")
            return f"I can help you with your account{', ' + customer_name if customer_name else ''}. What specific information do you need?"
        return "I'd be happy to help you with your account. Could you please verify your account information first?"
    
    async def _handle_support_issue(self, message: str, customer_info: Optional[Dict]) -> str:
        """Handle general support issues."""
        if customer_info:
            customer_name = customer_info.get("first_name", "")
            return f"I'm here to help resolve your issue{', ' + customer_name if customer_name else ''}. Could you please describe the problem you're experiencing?"
        return "I'm sorry to hear you're experiencing an issue. Could you please provide more details so I can assist you better?"
