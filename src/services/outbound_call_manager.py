"""Outbound Call Management System for AI-initiated customer calls."""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import structlog

from ..models.data_models import CustomerMessage
from ..services.customer_database import customer_db
from ..services.conversation_manager import conversation_manager
from ..config import settings

logger = structlog.get_logger(__name__)


class CallStatus(Enum):
    """Call status enumeration."""
    SCHEDULED = "scheduled"
    QUEUED = "queued"
    CALLING = "calling"
    CONNECTED = "connected"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    NO_ANSWER = "no_answer"
    BUSY = "busy"
    CANCELLED = "cancelled"


class CallPurpose(Enum):
    """Call purpose enumeration."""
    FOLLOW_UP = "follow_up"
    SURVEY = "survey"
    PROACTIVE_SUPPORT = "proactive_support"
    ORDER_UPDATE = "order_update"
    PAYMENT_REMINDER = "payment_reminder"
    FEEDBACK_REQUEST = "feedback_request"
    PRODUCT_RECOMMENDATION = "product_recommendation"
    RETENTION = "retention"


class OutboundCall:
    """Represents an outbound call to a customer."""
    
    def __init__(
        self,
        call_id: str,
        customer_id: str,
        phone_number: str,
        purpose: CallPurpose,
        scheduled_time: datetime,
        priority: int = 5,
        max_attempts: int = 3,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.call_id = call_id
        self.customer_id = customer_id
        self.phone_number = phone_number
        self.purpose = purpose
        self.scheduled_time = scheduled_time
        self.priority = priority  # 1-10, 1 being highest priority
        self.max_attempts = max_attempts
        self.metadata = metadata or {}
        
        # Call tracking
        self.status = CallStatus.SCHEDULED
        self.attempts = 0
        self.created_at = datetime.utcnow()
        self.started_at: Optional[datetime] = None
        self.ended_at: Optional[datetime] = None
        self.duration_seconds: Optional[int] = None
        self.conversation_id: Optional[str] = None
        self.failure_reason: Optional[str] = None
        self.notes: List[str] = []
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert call to dictionary."""
        return {
            "call_id": self.call_id,
            "customer_id": self.customer_id,
            "phone_number": self.phone_number,
            "purpose": self.purpose.value,
            "scheduled_time": self.scheduled_time.isoformat(),
            "priority": self.priority,
            "max_attempts": self.max_attempts,
            "metadata": self.metadata,
            "status": self.status.value,
            "attempts": self.attempts,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "ended_at": self.ended_at.isoformat() if self.ended_at else None,
            "duration_seconds": self.duration_seconds,
            "conversation_id": self.conversation_id,
            "failure_reason": self.failure_reason,
            "notes": self.notes
        }


class OutboundCallManager:
    """Manages outbound calls to customers."""
    
    def __init__(self):
        """Initialize the outbound call manager."""
        self.logger = structlog.get_logger(__name__)
        
        # Call storage (in production, use database)
        self.scheduled_calls: Dict[str, OutboundCall] = {}
        self.active_calls: Dict[str, OutboundCall] = {}
        self.completed_calls: Dict[str, OutboundCall] = {}
        
        # Configuration
        self.max_concurrent_calls = 5
        self.call_retry_delay_minutes = 30
        self.business_hours_start = 9  # 9 AM
        self.business_hours_end = 18   # 6 PM
        
        # Call processing
        self._call_processor_task = None
        self._is_processing = False
    
    async def schedule_call(
        self,
        customer_id: str,
        purpose: CallPurpose,
        scheduled_time: Optional[datetime] = None,
        priority: int = 5,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Schedule an outbound call to a customer.
        
        Args:
            customer_id: Customer identifier
            purpose: Purpose of the call
            scheduled_time: When to make the call (defaults to now)
            priority: Call priority (1-10, 1 being highest)
            metadata: Additional call metadata
            
        Returns:
            Call ID
        """
        # Get customer information
        customer_info = await customer_db.get_customer_info(customer_id)
        if not customer_info:
            raise ValueError(f"Customer {customer_id} not found")
        
        phone_number = customer_info.get("phone")
        if not phone_number:
            raise ValueError(f"No phone number found for customer {customer_id}")
        
        # Generate call ID
        call_id = f"call_{uuid.uuid4().hex[:12]}"
        
        # Default to immediate scheduling if no time specified
        if scheduled_time is None:
            scheduled_time = datetime.utcnow()
        
        # Adjust for business hours if needed
        scheduled_time = self._adjust_for_business_hours(scheduled_time)
        
        # Create call
        call = OutboundCall(
            call_id=call_id,
            customer_id=customer_id,
            phone_number=phone_number,
            purpose=purpose,
            scheduled_time=scheduled_time,
            priority=priority,
            metadata=metadata or {}
        )
        
        # Store call
        self.scheduled_calls[call_id] = call
        
        # Start call processor if not running
        if not self._is_processing:
            self._call_processor_task = asyncio.create_task(self._process_calls())
        
        self.logger.info(
            "Call scheduled",
            call_id=call_id,
            customer_id=customer_id,
            purpose=purpose.value,
            scheduled_time=scheduled_time.isoformat(),
            priority=priority
        )
        
        return call_id
    
    async def get_call_status(self, call_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific call."""
        # Check all call stores
        for call_store in [self.scheduled_calls, self.active_calls, self.completed_calls]:
            if call_id in call_store:
                return call_store[call_id].to_dict()
        return None
    
    async def cancel_call(self, call_id: str) -> bool:
        """Cancel a scheduled call."""
        if call_id in self.scheduled_calls:
            call = self.scheduled_calls[call_id]
            call.status = CallStatus.CANCELLED
            call.ended_at = datetime.utcnow()
            
            # Move to completed
            self.completed_calls[call_id] = call
            del self.scheduled_calls[call_id]
            
            self.logger.info("Call cancelled", call_id=call_id)
            return True
        return False
    
    async def get_call_queue(self) -> List[Dict[str, Any]]:
        """Get current call queue."""
        queue = []
        
        # Sort scheduled calls by priority and scheduled time
        sorted_calls = sorted(
            self.scheduled_calls.values(),
            key=lambda c: (c.priority, c.scheduled_time)
        )
        
        for call in sorted_calls:
            queue.append(call.to_dict())
        
        return queue
    
    async def get_active_calls(self) -> List[Dict[str, Any]]:
        """Get currently active calls."""
        return [call.to_dict() for call in self.active_calls.values()]
    
    def _adjust_for_business_hours(self, scheduled_time: datetime) -> datetime:
        """Adjust scheduled time to fall within business hours."""
        # If it's outside business hours, move to next business day
        hour = scheduled_time.hour
        
        if hour < self.business_hours_start:
            # Too early, move to business hours start
            return scheduled_time.replace(hour=self.business_hours_start, minute=0, second=0)
        elif hour >= self.business_hours_end:
            # Too late, move to next day business hours start
            next_day = scheduled_time + timedelta(days=1)
            return next_day.replace(hour=self.business_hours_start, minute=0, second=0)
        
        return scheduled_time
    
    async def _process_calls(self):
        """Background task to process scheduled calls."""
        self._is_processing = True
        
        try:
            while self.scheduled_calls or self.active_calls:
                current_time = datetime.utcnow()
                
                # Process scheduled calls that are ready
                ready_calls = [
                    call for call in self.scheduled_calls.values()
                    if call.scheduled_time <= current_time and len(self.active_calls) < self.max_concurrent_calls
                ]
                
                # Sort by priority
                ready_calls.sort(key=lambda c: c.priority)
                
                for call in ready_calls:
                    if len(self.active_calls) >= self.max_concurrent_calls:
                        break
                    
                    # Move to active calls
                    self.active_calls[call.call_id] = call
                    del self.scheduled_calls[call.call_id]
                    
                    # Start the call
                    asyncio.create_task(self._make_call(call))
                
                # Wait before next processing cycle
                await asyncio.sleep(5)
                
        except Exception as e:
            self.logger.error("Error in call processor", error=str(e))
        finally:
            self._is_processing = False
    
    async def _make_call(self, call: OutboundCall):
        """Make an actual outbound call."""
        call.attempts += 1
        call.started_at = datetime.utcnow()
        call.status = CallStatus.CALLING
        
        self.logger.info(
            "Starting outbound call",
            call_id=call.call_id,
            customer_id=call.customer_id,
            purpose=call.purpose.value,
            attempt=call.attempts
        )
        
        try:
            # This would integrate with actual telephony service (Twilio, etc.)
            # For now, we'll simulate the call process
            success = await self._simulate_call(call)
            
            if success:
                call.status = CallStatus.COMPLETED
                call.ended_at = datetime.utcnow()
                call.duration_seconds = int((call.ended_at - call.started_at).total_seconds())
                
                self.logger.info(
                    "Call completed successfully",
                    call_id=call.call_id,
                    duration=call.duration_seconds
                )
            else:
                await self._handle_call_failure(call)
                
        except Exception as e:
            call.failure_reason = str(e)
            await self._handle_call_failure(call)
        
        # Move to completed calls
        self.completed_calls[call.call_id] = call
        if call.call_id in self.active_calls:
            del self.active_calls[call.call_id]
    
    async def _simulate_call(self, call: OutboundCall) -> bool:
        """Simulate making a call (replace with real telephony integration)."""
        # Simulate call connection time
        await asyncio.sleep(2)
        
        # Simulate 80% success rate
        import random
        if random.random() < 0.8:
            call.status = CallStatus.CONNECTED
            
            # Start conversation
            conversation_id = await conversation_manager.start_conversation(
                customer_id=call.customer_id,
                channel="outbound_call",
                initial_message=f"Outbound call for {call.purpose.value}"
            )
            call.conversation_id = conversation_id
            
            # Simulate conversation duration
            await asyncio.sleep(random.randint(30, 120))
            
            return True
        else:
            # Simulate different failure types
            failure_types = [CallStatus.NO_ANSWER, CallStatus.BUSY, CallStatus.FAILED]
            call.status = random.choice(failure_types)
            call.failure_reason = f"Call {call.status.value}"
            return False
    
    async def _handle_call_failure(self, call: OutboundCall):
        """Handle call failure and potential retry."""
        if call.attempts < call.max_attempts:
            # Schedule retry
            retry_time = datetime.utcnow() + timedelta(minutes=self.call_retry_delay_minutes)
            retry_time = self._adjust_for_business_hours(retry_time)
            
            call.scheduled_time = retry_time
            call.status = CallStatus.SCHEDULED
            
            # Move back to scheduled calls
            self.scheduled_calls[call.call_id] = call
            if call.call_id in self.active_calls:
                del self.active_calls[call.call_id]
            
            self.logger.info(
                "Call failed, scheduling retry",
                call_id=call.call_id,
                attempt=call.attempts,
                retry_time=retry_time.isoformat()
            )
        else:
            call.status = CallStatus.FAILED
            call.ended_at = datetime.utcnow()
            
            self.logger.warning(
                "Call failed after max attempts",
                call_id=call.call_id,
                attempts=call.attempts,
                failure_reason=call.failure_reason
            )


# Global outbound call manager instance
outbound_call_manager = OutboundCallManager()
