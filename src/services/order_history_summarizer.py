"""
Order History Summarizer
Intelligently summarizes customer order history instead of asking for more information
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import structlog


@dataclass
class OrderSummary:
    """Summary of customer order history."""
    total_orders: int
    total_spent: float
    recent_orders_count: int
    most_recent_order: Optional[Dict]
    frequent_categories: List[str]
    average_order_value: float
    customer_since: Optional[str]
    order_frequency: str  # "frequent", "regular", "occasional", "new"
    recent_issues: List[Dict]
    loyalty_tier: str
    summary_text: str


class OrderHistorySummarizer:
    """Summarizes customer order history intelligently."""
    
    def __init__(self):
        self.logger = structlog.get_logger().bind(component="order_history_summarizer")
    
    async def summarize_order_history(
        self, 
        customer_info: Dict[str, Any], 
        detailed_orders: Optional[List[Dict]] = None
    ) -> OrderSummary:
        """
        Create an intelligent summary of customer order history.
        
        Args:
            customer_info: Customer information including basic order stats
            detailed_orders: Optional detailed order history
            
        Returns:
            Comprehensive order summary
        """
        # Extract basic information
        total_orders = customer_info.get('total_orders', 0)
        total_spent = customer_info.get('total_spent', 0.0)
        recent_orders = customer_info.get('recent_orders', [])
        customer_since = customer_info.get('registration_date')
        
        # Determine order frequency
        order_frequency = self._determine_order_frequency(total_orders, customer_since)
        
        # Calculate average order value
        avg_order_value = total_spent / max(total_orders, 1)
        
        # Get most recent order
        most_recent_order = recent_orders[0] if recent_orders else None
        
        # Analyze recent orders (last 3 months)
        recent_orders_count = len([
            order for order in recent_orders 
            if self._is_recent_order(order.get('date'))
        ])
        
        # Extract frequent categories (mock implementation)
        frequent_categories = self._extract_frequent_categories(recent_orders)
        
        # Identify recent issues
        recent_issues = self._identify_recent_issues(recent_orders, customer_info)
        
        # Determine loyalty tier
        loyalty_tier = self._determine_loyalty_tier(total_orders, total_spent, customer_since)
        
        # Generate summary text
        summary_text = self._generate_summary_text(
            total_orders, total_spent, avg_order_value, order_frequency,
            most_recent_order, recent_issues, loyalty_tier
        )
        
        summary = OrderSummary(
            total_orders=total_orders,
            total_spent=total_spent,
            recent_orders_count=recent_orders_count,
            most_recent_order=most_recent_order,
            frequent_categories=frequent_categories,
            average_order_value=avg_order_value,
            customer_since=customer_since,
            order_frequency=order_frequency,
            recent_issues=recent_issues,
            loyalty_tier=loyalty_tier,
            summary_text=summary_text
        )
        
        self.logger.info(
            "Order history summarized",
            customer_id=customer_info.get('customer_id'),
            total_orders=total_orders,
            loyalty_tier=loyalty_tier,
            order_frequency=order_frequency
        )
        
        return summary
    
    def _determine_order_frequency(self, total_orders: int, customer_since: Optional[str]) -> str:
        """Determine customer order frequency."""
        if total_orders == 0:
            return "new"
        
        if not customer_since:
            # Fallback based on order count only
            if total_orders >= 20:
                return "frequent"
            elif total_orders >= 5:
                return "regular"
            else:
                return "occasional"
        
        try:
            # Calculate months since registration
            registration_date = datetime.fromisoformat(customer_since.replace('Z', '+00:00'))
            months_active = (datetime.now() - registration_date.replace(tzinfo=None)).days / 30.44
            
            if months_active < 1:
                return "new"
            
            orders_per_month = total_orders / max(months_active, 1)
            
            if orders_per_month >= 2:
                return "frequent"
            elif orders_per_month >= 0.5:
                return "regular"
            else:
                return "occasional"
                
        except (ValueError, AttributeError):
            # Fallback to order count based classification
            if total_orders >= 20:
                return "frequent"
            elif total_orders >= 5:
                return "regular"
            else:
                return "occasional"
    
    def _is_recent_order(self, order_date: Optional[str]) -> bool:
        """Check if an order is from the last 3 months."""
        if not order_date:
            return False
        
        try:
            order_datetime = datetime.fromisoformat(order_date.replace('Z', '+00:00'))
            three_months_ago = datetime.now() - timedelta(days=90)
            return order_datetime.replace(tzinfo=None) > three_months_ago
        except (ValueError, AttributeError):
            return False
    
    def _extract_frequent_categories(self, recent_orders: List[Dict]) -> List[str]:
        """Extract frequently ordered categories (mock implementation)."""
        # In a real implementation, this would analyze actual product categories
        categories = []
        
        if len(recent_orders) >= 3:
            categories.append("Electronics")
        if len(recent_orders) >= 5:
            categories.append("Home & Garden")
        if any(order.get('total', 0) > 500 for order in recent_orders):
            categories.append("Premium Items")
        
        return categories[:3]  # Limit to top 3 categories
    
    def _identify_recent_issues(self, recent_orders: List[Dict], customer_info: Dict) -> List[Dict]:
        """Identify recent issues from order history and customer info."""
        issues = []
        
        # Check for recent returns or refunds (mock implementation)
        for order in recent_orders[:3]:  # Check last 3 orders
            if order.get('status') in ['returned', 'refunded', 'cancelled']:
                issues.append({
                    'type': 'order_issue',
                    'order_id': order.get('order_id'),
                    'status': order.get('status'),
                    'date': order.get('date')
                })
        
        # Check escalation history
        escalation_history = customer_info.get('escalation_history', 0)
        if escalation_history > 0:
            issues.append({
                'type': 'escalation_history',
                'count': escalation_history,
                'description': f"Customer has {escalation_history} previous escalation(s)"
            })
        
        return issues
    
    def _determine_loyalty_tier(self, total_orders: int, total_spent: float, customer_since: Optional[str]) -> str:
        """Determine customer loyalty tier."""
        # VIP customers
        if total_orders >= 50 or total_spent >= 10000:
            return "VIP"
        
        # Premium customers
        if total_orders >= 20 or total_spent >= 5000:
            return "Premium"
        
        # Regular customers
        if total_orders >= 5 or total_spent >= 1000:
            return "Regular"
        
        # New customers
        return "New"
    
    def _generate_summary_text(
        self,
        total_orders: int,
        total_spent: float,
        avg_order_value: float,
        order_frequency: str,
        most_recent_order: Optional[Dict],
        recent_issues: List[Dict],
        loyalty_tier: str
    ) -> str:
        """Generate human-readable summary text."""
        
        # Start with basic customer profile
        summary_parts = []
        
        # Customer tier and frequency
        if loyalty_tier == "VIP":
            summary_parts.append(f"This is a VIP customer with {total_orders} orders totaling ${total_spent:,.2f}")
        elif loyalty_tier == "Premium":
            summary_parts.append(f"This is a premium customer with {total_orders} orders totaling ${total_spent:,.2f}")
        elif total_orders > 0:
            summary_parts.append(f"This customer has placed {total_orders} orders totaling ${total_spent:,.2f}")
        else:
            summary_parts.append("This is a new customer with no previous orders")
        
        # Order frequency and value
        if total_orders > 0:
            frequency_desc = {
                "frequent": "shops frequently with us",
                "regular": "is a regular customer",
                "occasional": "shops occasionally",
                "new": "is new to our service"
            }
            summary_parts.append(f"They {frequency_desc[order_frequency]} with an average order value of ${avg_order_value:.2f}")
        
        # Most recent order
        if most_recent_order:
            recent_date = most_recent_order.get('date', 'recently')
            recent_status = most_recent_order.get('status', 'unknown')
            recent_total = most_recent_order.get('total', 0)
            
            if recent_date != 'recently':
                try:
                    date_obj = datetime.fromisoformat(recent_date.replace('Z', '+00:00'))
                    days_ago = (datetime.now() - date_obj.replace(tzinfo=None)).days
                    if days_ago < 7:
                        time_desc = f"{days_ago} days ago"
                    elif days_ago < 30:
                        time_desc = f"{days_ago // 7} weeks ago"
                    else:
                        time_desc = f"{days_ago // 30} months ago"
                except:
                    time_desc = "recently"
            else:
                time_desc = "recently"
            
            summary_parts.append(f"Their most recent order was {time_desc} for ${recent_total:.2f} (status: {recent_status})")
        
        # Recent issues
        if recent_issues:
            issue_descriptions = []
            for issue in recent_issues:
                if issue['type'] == 'order_issue':
                    issue_descriptions.append(f"recent {issue['status']} order")
                elif issue['type'] == 'escalation_history':
                    issue_descriptions.append(f"{issue['count']} previous escalation(s)")
            
            if issue_descriptions:
                summary_parts.append(f"Note: Customer has {', '.join(issue_descriptions)}")
        
        return ". ".join(summary_parts) + "."
    
    def should_summarize_instead_of_asking(self, customer_info: Dict[str, Any]) -> bool:
        """
        Determine if we should provide a summary instead of asking for more info.
        
        Args:
            customer_info: Customer information
            
        Returns:
            True if we should summarize, False if we should ask for more info
        """
        total_orders = customer_info.get('total_orders', 0)
        
        # Summarize for customers with significant order history
        if total_orders >= 5:
            return True
        
        # Summarize for customers with high total spend
        total_spent = customer_info.get('total_spent', 0)
        if total_spent >= 1000:
            return True
        
        # Summarize for customers with recent orders
        recent_orders = customer_info.get('recent_orders', [])
        if len(recent_orders) >= 2:
            return True
        
        return False
