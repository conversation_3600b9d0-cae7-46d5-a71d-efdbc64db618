"""Conversation Management Service for handling multi-turn conversations."""

import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
import structlog

from ..models.data_models import Conversation, ConversationTurn, CustomerMessage
from ..services.customer_database import customer_db
from ..config import settings
from .natural_conversation_flow import NaturalConversationFlow, ConversationContext, BusinessIntent

logger = structlog.get_logger(__name__)


class ConversationManager:
    """Manages multi-turn conversations with context persistence and database integration."""

    def __init__(self):
        """Initialize the conversation manager."""
        self.logger = structlog.get_logger(__name__)

        # In-memory conversation storage (in production, use Redis or database)
        self.active_conversations: Dict[str, Conversation] = {}
        self.conversation_timeouts: Dict[str, datetime] = {}

        # Natural conversation flow contexts
        self.conversation_contexts: Dict[str, ConversationContext] = {}

        # Configuration
        self.conversation_timeout_minutes = 30  # Conversations expire after 30 minutes of inactivity
        self.max_turns_per_conversation = 50   # Maximum turns before forcing new conversation

        # Cleanup task will be started when needed
        self._cleanup_task = None

        # Initialize natural conversation flow manager
        self.natural_flow = NaturalConversationFlow()
    
    async def start_conversation(
        self,
        customer_id: str,
        channel: str = "voice",
        initial_message: Optional[str] = None
    ) -> str:
        """Start a new conversation.

        Args:
            customer_id: Customer identifier
            channel: Communication channel (voice, web, chat, etc.)
            initial_message: Optional initial customer message

        Returns:
            Conversation ID
        """
        # Start cleanup task if not already running
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_conversations())

        conversation_id = f"conv_{uuid.uuid4().hex[:12]}_{int(datetime.now().timestamp())}"
        
        # Get customer information for context
        customer_info = await customer_db.get_customer_info(customer_id)
        customer_name = ""
        if customer_info:
            customer_name = customer_info.get("first_name", "")
        
        # Create conversation
        conversation = Conversation(
            conversation_id=conversation_id,
            customer_id=customer_id,
            channel=channel,
            turns=[],
            status="active",
            started_at=datetime.utcnow(),
            last_activity=datetime.utcnow(),
            context_summary=f"New conversation with {customer_name or customer_id} via {channel}",
            resolved=False
        )
        
        # Add initial turn if provided
        if initial_message:
            turn = ConversationTurn(
                turn_id=f"turn_{uuid.uuid4().hex[:8]}",
                customer_message=initial_message,
                ai_response="",  # Will be filled by the response
                timestamp=datetime.utcnow(),
                quality_score=0.0,
                processing_time=0.0,
                agent_emotions={},
                follow_up_questions=[]
            )
            conversation.turns.append(turn)
        
        # Store conversation
        self.active_conversations[conversation_id] = conversation
        self.conversation_timeouts[conversation_id] = datetime.utcnow() + timedelta(minutes=self.conversation_timeout_minutes)
        
        self.logger.info(
            "New conversation started",
            conversation_id=conversation_id,
            customer_id=customer_id,
            channel=channel,
            customer_name=customer_name
        )
        
        return conversation_id
    
    async def add_turn(
        self,
        conversation_id: str,
        customer_message: str,
        ai_response: str,
        processing_time: float = 0.0,
        quality_score: float = 0.0,
        agent_emotions: Optional[Dict[str, float]] = None,
        follow_up_questions: Optional[List[str]] = None
    ) -> ConversationTurn:
        """Add a new turn to an existing conversation.
        
        Args:
            conversation_id: Conversation identifier
            customer_message: Customer's message
            ai_response: AI agent's response
            processing_time: Time taken to process the message
            quality_score: Quality score of the response
            agent_emotions: Emotional analysis results
            follow_up_questions: Suggested follow-up questions
            
        Returns:
            The created conversation turn
        """
        conversation = self.active_conversations.get(conversation_id)
        if not conversation:
            raise ValueError(f"Conversation {conversation_id} not found or expired")
        
        # Create new turn
        turn = ConversationTurn(
            turn_id=f"turn_{uuid.uuid4().hex[:8]}",
            customer_message=customer_message,
            ai_response=ai_response,
            timestamp=datetime.utcnow(),
            quality_score=quality_score,
            processing_time=processing_time,
            agent_emotions=agent_emotions or {},
            follow_up_questions=follow_up_questions or []
        )
        
        # Add turn to conversation
        conversation.turns.append(turn)
        conversation.last_activity = datetime.utcnow()
        
        # Update conversation context summary
        await self._update_context_summary(conversation)
        
        # Extend timeout
        self.conversation_timeouts[conversation_id] = datetime.utcnow() + timedelta(minutes=self.conversation_timeout_minutes)
        
        self.logger.info(
            "Turn added to conversation",
            conversation_id=conversation_id,
            turn_id=turn.turn_id,
            customer_id=conversation.customer_id,
            turn_count=len(conversation.turns)
        )
        
        return turn
    
    async def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Get conversation by ID.
        
        Args:
            conversation_id: Conversation identifier
            
        Returns:
            Conversation object or None if not found
        """
        return self.active_conversations.get(conversation_id)
    
    async def get_conversation_context(self, conversation_id: str) -> Dict[str, Any]:
        """Get conversation context for AI processing.
        
        Args:
            conversation_id: Conversation identifier
            
        Returns:
            Dictionary containing conversation context
        """
        conversation = self.active_conversations.get(conversation_id)
        if not conversation:
            return {}
        
        # Get customer information
        customer_info = await customer_db.get_customer_info(conversation.customer_id)
        
        # Build context
        context = {
            "conversation_id": conversation_id,
            "customer_id": conversation.customer_id,
            "customer_info": customer_info,
            "channel": conversation.channel,
            "turn_count": len(conversation.turns),
            "context_summary": conversation.context_summary,
            "last_activity": conversation.last_activity.isoformat(),
            "resolved": conversation.resolved,
            "recent_turns": []
        }
        
        # Add recent turns (last 5 for context)
        recent_turns = conversation.turns[-5:] if len(conversation.turns) > 5 else conversation.turns
        for turn in recent_turns:
            context["recent_turns"].append({
                "customer_message": turn.customer_message,
                "ai_response": turn.ai_response,
                "timestamp": turn.timestamp.isoformat(),
                "follow_up_questions": turn.follow_up_questions
            })
        
        return context
    
    async def end_conversation(self, conversation_id: str, resolved: bool = True) -> bool:
        """End a conversation.
        
        Args:
            conversation_id: Conversation identifier
            resolved: Whether the conversation was resolved
            
        Returns:
            True if conversation was ended, False if not found
        """
        conversation = self.active_conversations.get(conversation_id)
        if not conversation:
            return False
        
        conversation.status = "ended"
        conversation.resolved = resolved
        conversation.last_activity = datetime.utcnow()
        
        # Remove from active conversations
        del self.active_conversations[conversation_id]
        if conversation_id in self.conversation_timeouts:
            del self.conversation_timeouts[conversation_id]
        
        self.logger.info(
            "Conversation ended",
            conversation_id=conversation_id,
            customer_id=conversation.customer_id,
            resolved=resolved,
            turn_count=len(conversation.turns)
        )
        
        return True
    
    async def _update_context_summary(self, conversation: Conversation) -> None:
        """Update conversation context summary based on recent turns."""
        if not conversation.turns:
            return
        
        # Simple context summary based on recent messages
        recent_messages = []
        for turn in conversation.turns[-3:]:  # Last 3 turns
            if turn.customer_message:
                recent_messages.append(f"Customer: {turn.customer_message[:100]}")
            if turn.ai_response:
                recent_messages.append(f"AI: {turn.ai_response[:100]}")
        
        conversation.context_summary = " | ".join(recent_messages)
    
    async def _cleanup_expired_conversations(self) -> None:
        """Background task to clean up expired conversations."""
        while True:
            try:
                current_time = datetime.utcnow()
                expired_conversations = []
                
                for conv_id, timeout_time in self.conversation_timeouts.items():
                    if current_time > timeout_time:
                        expired_conversations.append(conv_id)
                
                for conv_id in expired_conversations:
                    await self.end_conversation(conv_id, resolved=False)
                    self.logger.info("Expired conversation cleaned up", conversation_id=conv_id)
                
                # Sleep for 5 minutes before next cleanup
                await asyncio.sleep(300)
                
            except Exception as e:
                self.logger.error("Error in conversation cleanup", error=str(e))
                await asyncio.sleep(60)  # Shorter sleep on error

    async def start_natural_conversation(
        self,
        customer_id: str,
        conversation_id: str,
        intended_business_purpose: Optional[BusinessIntent] = None
    ) -> ConversationContext:
        """Start a natural conversation flow for the given conversation."""

        context = await self.natural_flow.initialize_conversation(
            customer_id=customer_id,
            conversation_id=conversation_id,
            intended_business_purpose=intended_business_purpose
        )

        self.conversation_contexts[conversation_id] = context

        self.logger.info(
            "Started natural conversation flow",
            conversation_id=conversation_id,
            customer_id=customer_id,
            intended_purpose=intended_business_purpose.value if intended_business_purpose else None
        )

        return context

    async def process_natural_conversation_turn(
        self,
        conversation_id: str,
        customer_message: str,
        customer_info: Optional[Dict] = None
    ) -> Tuple[str, ConversationContext]:
        """Process a turn in the natural conversation flow."""

        context = self.conversation_contexts.get(conversation_id)
        if not context:
            # Initialize if not exists
            context = await self.start_natural_conversation(
                customer_id=customer_info.get("customer_id", "unknown") if customer_info else "unknown",
                conversation_id=conversation_id
            )

        # Generate natural response
        response, updated_context = await self.natural_flow.generate_response(
            context=context,
            customer_message=customer_message,
            customer_info=customer_info
        )

        # Update stored context
        self.conversation_contexts[conversation_id] = updated_context

        self.logger.info(
            "Processed natural conversation turn",
            conversation_id=conversation_id,
            phase=updated_context.current_phase.value,
            turns=updated_context.total_turns,
            transition_readiness=updated_context.transition_readiness
        )

        return response, updated_context

    def get_natural_conversation_context(self, conversation_id: str) -> Optional[ConversationContext]:
        """Get the natural conversation context for a conversation."""
        return self.conversation_contexts.get(conversation_id)

    def should_use_natural_flow(self, conversation_id: str, customer_message: str) -> bool:
        """Determine if natural conversation flow should be used."""

        # Use natural flow for new conversations or when context exists
        if conversation_id in self.conversation_contexts:
            return True

        # Use natural flow for casual greetings or when no specific business intent is detected
        casual_indicators = ["hello", "hi", "hey", "good morning", "good afternoon", "how are you"]
        message_lower = customer_message.lower()

        if any(indicator in message_lower for indicator in casual_indicators):
            return True

        # Don't use natural flow for direct business requests
        business_indicators = ["order", "payment", "refund", "problem", "issue", "help", "support"]
        if any(indicator in message_lower for indicator in business_indicators):
            return False

        return True

    async def end_natural_conversation(self, conversation_id: str):
        """End the natural conversation flow for a conversation."""
        if conversation_id in self.conversation_contexts:
            context = self.conversation_contexts[conversation_id]

            self.logger.info(
                "Ended natural conversation flow",
                conversation_id=conversation_id,
                total_turns=context.total_turns,
                final_phase=context.current_phase.value,
                business_intent=context.detected_business_intent.value if context.detected_business_intent else None
            )

            del self.conversation_contexts[conversation_id]


# Global conversation manager instance
conversation_manager = ConversationManager()
