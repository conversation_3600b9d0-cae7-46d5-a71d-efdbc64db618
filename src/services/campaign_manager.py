"""Call Campaign Management System for organizing and tracking outbound call campaigns."""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from enum import Enum
import structlog

from ..services.outbound_call_manager import outbound_call_manager, CallPurpose
from ..services.customer_database import customer_db
from ..config import settings

logger = structlog.get_logger(__name__)


class CampaignStatus(Enum):
    """Campaign status enumeration."""
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class CampaignType(Enum):
    """Campaign type enumeration."""
    FOLLOW_UP = "follow_up"
    SURVEY = "survey"
    PROACTIVE_SUPPORT = "proactive_support"
    RETENTION = "retention"
    PRODUCT_PROMOTION = "product_promotion"
    PAYMENT_REMINDER = "payment_reminder"
    FEEDBACK_COLLECTION = "feedback_collection"


class CallCampaign:
    """Represents a call campaign."""
    
    def __init__(
        self,
        campaign_id: str,
        name: str,
        campaign_type: CampaignType,
        purpose: CallPurpose,
        target_customers: List[str],
        start_time: datetime,
        end_time: Optional[datetime] = None,
        max_calls_per_hour: int = 10,
        business_hours_only: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.campaign_id = campaign_id
        self.name = name
        self.campaign_type = campaign_type
        self.purpose = purpose
        self.target_customers = target_customers
        self.start_time = start_time
        self.end_time = end_time
        self.max_calls_per_hour = max_calls_per_hour
        self.business_hours_only = business_hours_only
        self.metadata = metadata or {}
        
        # Campaign tracking
        self.status = CampaignStatus.DRAFT
        self.created_at = datetime.utcnow()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        
        # Call tracking
        self.scheduled_calls: Set[str] = set()  # call_ids
        self.completed_calls: Set[str] = set()
        self.failed_calls: Set[str] = set()
        self.successful_calls: Set[str] = set()
        
        # Statistics
        self.total_calls_scheduled = 0
        self.total_calls_completed = 0
        self.total_calls_successful = 0
        self.average_call_duration = 0.0
        self.success_rate = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert campaign to dictionary."""
        return {
            "campaign_id": self.campaign_id,
            "name": self.name,
            "campaign_type": self.campaign_type.value,
            "purpose": self.purpose.value,
            "target_customers": self.target_customers,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "max_calls_per_hour": self.max_calls_per_hour,
            "business_hours_only": self.business_hours_only,
            "metadata": self.metadata,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "total_calls_scheduled": self.total_calls_scheduled,
            "total_calls_completed": self.total_calls_completed,
            "total_calls_successful": self.total_calls_successful,
            "average_call_duration": self.average_call_duration,
            "success_rate": self.success_rate
        }


class CampaignManager:
    """Manages call campaigns."""
    
    def __init__(self):
        """Initialize campaign manager."""
        self.logger = structlog.get_logger(__name__)
        
        # Campaign storage (in production, use database)
        self.campaigns: Dict[str, CallCampaign] = {}
        
        # Campaign processing
        self._campaign_processor_task = None
        self._is_processing = False
    
    async def create_campaign(
        self,
        name: str,
        campaign_type: CampaignType,
        purpose: CallPurpose,
        target_criteria: Dict[str, Any],
        start_time: datetime,
        end_time: Optional[datetime] = None,
        max_calls_per_hour: int = 10,
        business_hours_only: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new call campaign.
        
        Args:
            name: Campaign name
            campaign_type: Type of campaign
            purpose: Purpose of calls
            target_criteria: Criteria for selecting target customers
            start_time: When to start the campaign
            end_time: When to end the campaign (optional)
            max_calls_per_hour: Maximum calls per hour
            business_hours_only: Whether to only call during business hours
            metadata: Additional campaign metadata
            
        Returns:
            Campaign ID
        """
        # Generate campaign ID
        campaign_id = f"campaign_{uuid.uuid4().hex[:12]}"
        
        # Get target customers based on criteria
        target_customers = await self._get_target_customers(target_criteria)
        
        if not target_customers:
            raise ValueError("No customers match the target criteria")
        
        # Create campaign
        campaign = CallCampaign(
            campaign_id=campaign_id,
            name=name,
            campaign_type=campaign_type,
            purpose=purpose,
            target_customers=target_customers,
            start_time=start_time,
            end_time=end_time,
            max_calls_per_hour=max_calls_per_hour,
            business_hours_only=business_hours_only,
            metadata=metadata
        )
        
        # Store campaign
        self.campaigns[campaign_id] = campaign
        
        self.logger.info(
            "Campaign created",
            campaign_id=campaign_id,
            name=name,
            target_count=len(target_customers),
            start_time=start_time.isoformat()
        )
        
        return campaign_id
    
    async def start_campaign(self, campaign_id: str) -> bool:
        """Start a campaign."""
        if campaign_id not in self.campaigns:
            return False
        
        campaign = self.campaigns[campaign_id]
        
        if campaign.status != CampaignStatus.DRAFT:
            return False
        
        campaign.status = CampaignStatus.SCHEDULED
        campaign.started_at = datetime.utcnow()
        
        # Start campaign processor if not running
        if not self._is_processing:
            self._campaign_processor_task = asyncio.create_task(self._process_campaigns())
        
        self.logger.info("Campaign started", campaign_id=campaign_id)
        return True
    
    async def pause_campaign(self, campaign_id: str) -> bool:
        """Pause a campaign."""
        if campaign_id not in self.campaigns:
            return False
        
        campaign = self.campaigns[campaign_id]
        
        if campaign.status == CampaignStatus.ACTIVE:
            campaign.status = CampaignStatus.PAUSED
            self.logger.info("Campaign paused", campaign_id=campaign_id)
            return True
        
        return False
    
    async def resume_campaign(self, campaign_id: str) -> bool:
        """Resume a paused campaign."""
        if campaign_id not in self.campaigns:
            return False
        
        campaign = self.campaigns[campaign_id]
        
        if campaign.status == CampaignStatus.PAUSED:
            campaign.status = CampaignStatus.ACTIVE
            self.logger.info("Campaign resumed", campaign_id=campaign_id)
            return True
        
        return False
    
    async def cancel_campaign(self, campaign_id: str) -> bool:
        """Cancel a campaign."""
        if campaign_id not in self.campaigns:
            return False
        
        campaign = self.campaigns[campaign_id]
        campaign.status = CampaignStatus.CANCELLED
        campaign.completed_at = datetime.utcnow()
        
        self.logger.info("Campaign cancelled", campaign_id=campaign_id)
        return True
    
    async def get_campaign(self, campaign_id: str) -> Optional[Dict[str, Any]]:
        """Get campaign details."""
        if campaign_id not in self.campaigns:
            return None
        
        return self.campaigns[campaign_id].to_dict()
    
    async def list_campaigns(self, status: Optional[CampaignStatus] = None) -> List[Dict[str, Any]]:
        """List campaigns, optionally filtered by status."""
        campaigns = []
        
        for campaign in self.campaigns.values():
            if status is None or campaign.status == status:
                campaigns.append(campaign.to_dict())
        
        return campaigns
    
    async def _get_target_customers(self, criteria: Dict[str, Any]) -> List[str]:
        """Get target customers based on criteria."""
        # This is a simplified implementation
        # In production, this would query the database with complex criteria
        
        target_customers = []
        
        # Get all customers from mock data for demo
        for customer_id, customer_info in customer_db._mock_customers.items():
            if self._customer_matches_criteria(customer_info, criteria):
                target_customers.append(customer_id)
        
        return target_customers
    
    def _customer_matches_criteria(self, customer_info: Dict[str, Any], criteria: Dict[str, Any]) -> bool:
        """Check if customer matches campaign criteria."""
        # Customer type filter
        if "customer_type" in criteria:
            if customer_info.get("customer_type") not in criteria["customer_type"]:
                return False
        
        # Support tier filter
        if "support_tier" in criteria:
            if customer_info.get("support_tier") not in criteria["support_tier"]:
                return False
        
        # Order count filter
        if "min_orders" in criteria:
            if customer_info.get("total_orders", 0) < criteria["min_orders"]:
                return False
        
        if "max_orders" in criteria:
            if customer_info.get("total_orders", 0) > criteria["max_orders"]:
                return False
        
        # Spending filter
        if "min_spent" in criteria:
            if customer_info.get("total_spent", 0) < criteria["min_spent"]:
                return False
        
        if "max_spent" in criteria:
            if customer_info.get("total_spent", 0) > criteria["max_spent"]:
                return False
        
        # Registration date filter
        if "registered_after" in criteria:
            reg_date = datetime.fromisoformat(customer_info.get("registration_date", "2020-01-01T00:00:00Z").replace("Z", "+00:00"))
            if reg_date < criteria["registered_after"]:
                return False
        
        return True
    
    async def _process_campaigns(self):
        """Background task to process active campaigns."""
        self._is_processing = True
        
        try:
            while any(c.status in [CampaignStatus.SCHEDULED, CampaignStatus.ACTIVE] for c in self.campaigns.values()):
                current_time = datetime.utcnow()
                
                for campaign in self.campaigns.values():
                    if campaign.status == CampaignStatus.SCHEDULED and campaign.start_time <= current_time:
                        campaign.status = CampaignStatus.ACTIVE
                        await self._schedule_campaign_calls(campaign)
                    
                    elif campaign.status == CampaignStatus.ACTIVE:
                        await self._process_active_campaign(campaign, current_time)
                
                # Wait before next processing cycle
                await asyncio.sleep(60)  # Check every minute
                
        except Exception as e:
            self.logger.error("Error in campaign processor", error=str(e))
        finally:
            self._is_processing = False
    
    async def _schedule_campaign_calls(self, campaign: CallCampaign):
        """Schedule calls for a campaign."""
        self.logger.info("Scheduling calls for campaign", campaign_id=campaign.campaign_id)
        
        # Calculate call scheduling intervals
        interval_minutes = 60 / campaign.max_calls_per_hour
        
        current_time = datetime.utcnow()
        
        for i, customer_id in enumerate(campaign.target_customers):
            # Calculate scheduled time for this call
            scheduled_time = current_time + timedelta(minutes=i * interval_minutes)
            
            # Schedule the call
            try:
                call_id = await outbound_call_manager.schedule_call(
                    customer_id=customer_id,
                    purpose=campaign.purpose,
                    scheduled_time=scheduled_time,
                    priority=5,
                    metadata={
                        "campaign_id": campaign.campaign_id,
                        "campaign_name": campaign.name,
                        "campaign_type": campaign.campaign_type.value
                    }
                )
                
                campaign.scheduled_calls.add(call_id)
                campaign.total_calls_scheduled += 1
                
            except Exception as e:
                self.logger.error(
                    "Error scheduling call for campaign",
                    campaign_id=campaign.campaign_id,
                    customer_id=customer_id,
                    error=str(e)
                )
    
    async def _process_active_campaign(self, campaign: CallCampaign, current_time: datetime):
        """Process an active campaign."""
        # Check if campaign should end
        if campaign.end_time and current_time >= campaign.end_time:
            campaign.status = CampaignStatus.COMPLETED
            campaign.completed_at = current_time
            self.logger.info("Campaign completed", campaign_id=campaign.campaign_id)
            return
        
        # Update campaign statistics
        await self._update_campaign_stats(campaign)
    
    async def _update_campaign_stats(self, campaign: CallCampaign):
        """Update campaign statistics."""
        # This would typically query the call results from the outbound call manager
        # For now, we'll use placeholder logic
        
        # Calculate success rate
        if campaign.total_calls_scheduled > 0:
            campaign.success_rate = campaign.total_calls_successful / campaign.total_calls_scheduled
        
        # Update other statistics as needed
        pass


# Global campaign manager instance
campaign_manager = CampaignManager()
