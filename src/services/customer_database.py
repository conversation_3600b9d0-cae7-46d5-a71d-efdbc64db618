"""Customer Database Service for account information retrieval."""

import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import structlog

from ..config import settings

try:
    from google.cloud import bigquery
    BIGQUERY_AVAILABLE = True
except ImportError:
    BIGQUERY_AVAILABLE = False


class CustomerDatabaseService:
    """Service for retrieving customer account information."""

    def __init__(self):
        """Initialize the customer database service."""
        self.logger = structlog.get_logger(__name__)

        # Initialize BigQuery client if available
        self.bigquery_client = None
        if BIGQUERY_AVAILABLE:
            try:
                # Use existing service account credentials
                credentials_path = os.path.join(os.path.dirname(__file__), '..', 'vertexai.json')
                if os.path.exists(credentials_path):
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path

                self.bigquery_client = bigquery.Client(project=settings.google_cloud_project)
                self.logger.info("BigQuery client initialized for customer database")
            except Exception as e:
                self.logger.warning(f"Could not initialize BigQuery client: {e}")

        # Customer ID mapping (demo IDs to BigQuery IDs)
        self.id_mapping = {
            "CUST_001": "cust_001",
            "CUST_002": "cust_002",
            "CUST_003": "cust_003"
        }

        # Mock customer database for demo purposes (fallback)
        # In production, this would connect to BigQuery or other database
        self._mock_customers = {
            "CUST_001": {
                "customer_id": "CUST_001",
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Smith",
                "phone": "+27-11-555-0123",
                "customer_type": "premium",
                "registration_date": "2023-01-15T10:30:00Z",
                "total_orders": 12,
                "total_spent": 22450.75,  # Changed to Rand amounts
                "outstanding_balance": 25.99,  # Amount owed
                "support_tier": "priority",
                "preferred_channel": "email",
                "language_preference": "af",  # Afrikaans preference
                "timezone": "Africa/Johannesburg",
                "last_contact": "2024-01-20T14:22:00Z",
                "satisfaction_avg": 4.2,
                "escalation_history": 1,
                "account_status": "active",
                "billing_address": {
                    "street": "123 Main St",
                    "city": "New York",
                    "state": "NY",
                    "zip": "10001",
                    "country": "USA"
                },
                "recent_orders": [
                    {
                        "order_id": "ORD_12345",
                        "date": "2024-01-18T09:15:00Z",
                        "status": "delivered",
                        "total": 1599.99  # Changed to Rand amounts
                    },
                    {
                        "order_id": "ORD_12344",
                        "date": "2024-01-10T16:45:00Z",
                        "status": "delivered",
                        "total": 2789.50  # Changed to Rand amounts
                    }
                ]
            },
            "CUST_002": {
                "customer_id": "CUST_002",
                "email": "<EMAIL>",
                "first_name": "Sarah",
                "last_name": "Wilson",
                "phone": "+27-21-555-0456",
                "customer_type": "regular",
                "registration_date": "2023-06-20T14:15:00Z",
                "total_orders": 5,
                "total_spent": 425.30,
                "outstanding_balance": 150.75,  # Amount owed
                "support_tier": "standard",
                "preferred_channel": "chat",
                "language_preference": "af",  # Afrikaans preference
                "timezone": "Africa/Cape_Town",
                "last_contact": "2024-01-15T11:30:00Z",
                "satisfaction_avg": 4.5,
                "escalation_history": 0,
                "account_status": "active",
                "billing_address": {
                    "street": "456 Oak Ave",
                    "city": "Los Angeles",
                    "state": "CA",
                    "zip": "90210",
                    "country": "USA"
                },
                "recent_orders": [
                    {
                        "order_id": "ORD_12346",
                        "date": "2024-01-12T13:20:00Z",
                        "status": "shipped",
                        "total": 75.25
                    }
                ]
            },
            "CUST_003": {
                "customer_id": "CUST_003",
                "email": "<EMAIL>",
                "first_name": "Mike",
                "last_name": "Johnson",
                "phone": "+27-31-555-0789",
                "customer_type": "vip",
                "registration_date": "2022-03-10T08:45:00Z",
                "total_orders": 45,
                "total_spent": 5670.25,
                "outstanding_balance": 89.50,  # Amount owed
                "support_tier": "vip",
                "preferred_channel": "phone",
                "language_preference": "af",  # Afrikaans preference
                "timezone": "Africa/Johannesburg",
                "last_contact": "2024-01-22T10:15:00Z",
                "satisfaction_avg": 4.8,
                "escalation_history": 0,
                "account_status": "active",
                "billing_address": {
                    "street": "789 Business Blvd",
                    "city": "Chicago",
                    "state": "IL",
                    "zip": "60601",
                    "country": "USA"
                },
                "recent_orders": [
                    {
                        "order_id": "ORD_12347",
                        "date": "2024-01-21T15:30:00Z",
                        "status": "processing",
                        "total": 299.99
                    },
                    {
                        "order_id": "ORD_12348",
                        "date": "2024-01-19T11:45:00Z",
                        "status": "delivered",
                        "total": 189.75
                    }
                ]
            }
        }
    
    async def get_customer_info(self, customer_id: str) -> Optional[Dict[str, Any]]:
        """Get complete customer information by customer ID.

        Args:
            customer_id: The customer's unique identifier

        Returns:
            Customer information dictionary or None if not found
        """
        try:
            # For demo purposes, use mock data directly
            # In production, this would first try BigQuery
            # if self.bigquery_client:
            #     bigquery_customer = await self._get_customer_from_bigquery(customer_id)
            #     if bigquery_customer:
            #         return bigquery_customer

            # Use mock data
            customer_info = self._mock_customers.get(customer_id)

            if customer_info:
                self.logger.info(
                    "Customer information retrieved from mock data",
                    customer_id=customer_id,
                    customer_type=customer_info.get("customer_type"),
                    support_tier=customer_info.get("support_tier")
                )
                return customer_info.copy()  # Return a copy to prevent modification
            else:
                self.logger.warning("Customer not found", customer_id=customer_id)
                return None

        except Exception as e:
            self.logger.error("Error retrieving customer info", customer_id=customer_id, error=str(e))
            return None

    async def _get_customer_from_bigquery(self, customer_id: str) -> Optional[Dict[str, Any]]:
        """Get customer information from BigQuery.

        Args:
            customer_id: The customer's unique identifier

        Returns:
            Customer information dictionary or None if not found
        """
        try:
            # Map demo customer ID to BigQuery ID
            bigquery_id = self.id_mapping.get(customer_id, customer_id.lower())

            query = f"""
            SELECT
                customer_id,
                email,
                customer_type,
                registration_date,
                total_orders,
                total_spent,
                outstanding_balance,
                support_tier,
                preferred_channel,
                language_preference,
                timezone,
                last_contact,
                satisfaction_avg,
                escalation_history
            FROM `{settings.google_cloud_project}.{settings.bigquery_dataset}.customer_profiles`
            WHERE customer_id = @customer_id
            LIMIT 1
            """

            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("customer_id", "STRING", bigquery_id)
                ]
            )

            query_job = self.bigquery_client.query(query, job_config=job_config)
            results = query_job.result()

            for row in results:
                # Convert BigQuery row to customer info format
                customer_info = {
                    "customer_id": customer_id,  # Use original ID format
                    "email": row.email,
                    "first_name": row.email.split('@')[0].split('.')[0].title() if '.' in row.email.split('@')[0] else row.email.split('@')[0].title(),
                    "last_name": row.email.split('@')[0].split('.')[1].title() if '.' in row.email.split('@')[0] and len(row.email.split('@')[0].split('.')) > 1 else "Customer",
                    "phone": "******-0123",  # Default phone for demo
                    "customer_type": row.customer_type,
                    "registration_date": row.registration_date.isoformat() if row.registration_date else None,
                    "total_orders": row.total_orders or 0,
                    "total_spent": float(row.total_spent) if row.total_spent else 0.0,
                    "support_tier": row.support_tier,
                    "preferred_channel": row.preferred_channel,
                    "language_preference": row.language_preference,
                    "timezone": row.timezone,
                    "last_contact": row.last_contact.isoformat() if row.last_contact else None,
                    "satisfaction_avg": float(row.satisfaction_avg) if row.satisfaction_avg else 0.0,
                    "escalation_history": row.escalation_history or 0,
                    "account_status": "active",  # Default for demo
                    "billing_address": {
                        "street": "123 Main St",
                        "city": "New York",
                        "state": "NY",
                        "zip": "10001",
                        "country": "USA"
                    },
                    "recent_orders": [
                        {
                            "order_id": f"ORD_{12345 + i}",
                            "date": (datetime.now() - timedelta(days=i*7)).isoformat(),
                            "status": "delivered" if i > 0 else "processing",
                            "total": round(1599.99 + (i * 189.50), 2)  # Changed to Rand amounts
                        }
                        for i in range(min(3, row.total_orders or 1))
                    ]
                }

                self.logger.info(
                    "Customer information retrieved from BigQuery",
                    customer_id=customer_id,
                    bigquery_id=bigquery_id,
                    customer_type=customer_info.get("customer_type"),
                    support_tier=customer_info.get("support_tier")
                )

                return customer_info

            # No customer found
            return None

        except Exception as e:
            self.logger.error("Error querying BigQuery for customer", customer_id=customer_id, error=str(e))
            return None
    
    async def get_customer_email(self, customer_id: str) -> Optional[str]:
        """Get customer's email address.
        
        Args:
            customer_id: The customer's unique identifier
            
        Returns:
            Customer's email address or None if not found
        """
        customer_info = await self.get_customer_info(customer_id)
        return customer_info.get("email") if customer_info else None
    
    async def get_customer_orders(self, customer_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get customer's recent orders.

        Args:
            customer_id: The customer's unique identifier
            limit: Maximum number of orders to return

        Returns:
            List of recent orders
        """
        try:
            customer_info = await self.get_customer_info(customer_id)
            if customer_info and "recent_orders" in customer_info:
                return customer_info["recent_orders"][:limit]

            # If no orders in customer_info, return mock data for demo
            mock_orders = [
                {
                    "order_id": "ORD-2024-001",
                    "status": "Delivered",
                    "items": "Premium Wireless Headphones, Laptop Stand",
                    "total": 299.99,
                    "order_date": "2024-12-15",
                    "tracking_number": "TRK123456789"
                },
                {
                    "order_id": "ORD-2024-002",
                    "status": "In Transit",
                    "items": "Ergonomic Office Chair",
                    "total": 449.99,
                    "order_date": "2024-12-18",
                    "tracking_number": "TRK789456123"
                },
                {
                    "order_id": "ORD-2024-003",
                    "status": "Processing",
                    "items": "Standing Desk Converter",
                    "total": 199.99,
                    "order_date": "2024-12-19",
                    "tracking_number": None
                }
            ]

            self.logger.info(
                "Mock customer orders returned",
                customer_id=customer_id,
                order_count=len(mock_orders)
            )

            return mock_orders[:limit]

        except Exception as e:
            self.logger.error(f"Error getting customer orders: {e}", customer_id=customer_id)
            return []
    
    async def get_customer_profile_summary(self, customer_id: str) -> Optional[Dict[str, Any]]:
        """Get a summary of customer profile for agent use.
        
        Args:
            customer_id: The customer's unique identifier
            
        Returns:
            Customer profile summary
        """
        customer_info = await self.get_customer_info(customer_id)
        if not customer_info:
            return None
        
        return {
            "customer_id": customer_info["customer_id"],
            "name": f"{customer_info['first_name']} {customer_info['last_name']}",
            "email": customer_info["email"],
            "customer_type": customer_info["customer_type"],
            "support_tier": customer_info["support_tier"],
            "total_orders": customer_info["total_orders"],
            "total_spent": customer_info["total_spent"],
            "satisfaction_avg": customer_info["satisfaction_avg"],
            "account_status": customer_info["account_status"],
            "preferred_channel": customer_info["preferred_channel"]
        }

    async def get_customer_billing_info(self, customer_id: str) -> Optional[Dict[str, Any]]:
        """Get customer billing information.

        Args:
            customer_id: Customer identifier

        Returns:
            Customer billing information or None if not found
        """
        try:
            # Get customer info to get actual outstanding balance
            customer_info = await self.get_customer_info(customer_id)
            outstanding_balance = 0.00
            if customer_info:
                outstanding_balance = customer_info.get("outstanding_balance", 0.00)

            # For demo purposes, return mock billing data with actual balance
            # In production, this would query the actual billing table
            mock_billing = {
                "customer_id": customer_id,
                "current_balance": outstanding_balance,  # Use actual outstanding balance
                "last_payment_date": "2024-12-15",
                "last_payment_amount": 299.99,
                "payment_method": "Credit Card (**** 1234)",
                "billing_address": "123 Main St, City, State 12345",
                "next_billing_date": "2025-01-15"
            }

            self.logger.info(
                "Customer billing info retrieved",
                customer_id=customer_id,
                balance=mock_billing["current_balance"]
            )

            return mock_billing

        except Exception as e:
            self.logger.error(f"Error getting customer billing info: {e}", customer_id=customer_id)
            return None

    async def search_customer_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Search for customer by email address.
        
        Args:
            email: Customer's email address
            
        Returns:
            Customer information or None if not found
        """
        try:
            for customer_id, customer_info in self._mock_customers.items():
                if customer_info.get("email", "").lower() == email.lower():
                    self.logger.info("Customer found by email", email=email, customer_id=customer_id)
                    return customer_info.copy()
            
            self.logger.warning("Customer not found by email", email=email)
            return None
            
        except Exception as e:
            self.logger.error("Error searching customer by email", email=email, error=str(e))
            return None


# Global instance
customer_db = CustomerDatabaseService()
