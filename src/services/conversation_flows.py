"""AI-Initiated Conversation Flows for different types of outbound calls."""

from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import structlog

from ..services.customer_database import customer_db

logger = structlog.get_logger(__name__)


class ConversationStage(Enum):
    """Conversation stage enumeration."""
    GREETING = "greeting"
    PURPOSE_EXPLANATION = "purpose_explanation"
    INFORMATION_GATHERING = "information_gathering"
    MAIN_DISCUSSION = "main_discussion"
    RESOLUTION = "resolution"
    CLOSING = "closing"
    FOLLOW_UP_SCHEDULING = "follow_up_scheduling"


class ConversationFlow:
    """Represents a conversation flow template."""
    
    def __init__(
        self,
        flow_id: str,
        purpose: str,
        stages: List[ConversationStage],
        templates: Dict[str, Dict[str, str]],
        expected_duration_minutes: int = 5
    ):
        self.flow_id = flow_id
        self.purpose = purpose
        self.stages = stages
        self.templates = templates  # stage -> customer_type -> template
        self.expected_duration_minutes = expected_duration_minutes


class ConversationFlowManager:
    """Manages AI conversation flows for outbound calls."""
    
    def __init__(self):
        """Initialize conversation flow manager."""
        self.logger = structlog.get_logger(__name__)
        self.flows = self._initialize_flows()
    
    def _initialize_flows(self) -> Dict[str, ConversationFlow]:
        """Initialize predefined conversation flows."""
        flows = {}
        
        # Follow-up call flow
        flows["follow_up"] = ConversationFlow(
            flow_id="follow_up",
            purpose="follow_up",
            stages=[
                ConversationStage.GREETING,
                ConversationStage.PURPOSE_EXPLANATION,
                ConversationStage.INFORMATION_GATHERING,
                ConversationStage.RESOLUTION,
                ConversationStage.CLOSING
            ],
            templates={
                ConversationStage.GREETING.value: {
                    "premium": "Hello {customer_name}, this is Angela from our premium support team. I hope you're having a wonderful day!",
                    "vip": "Good {time_of_day} {customer_name}, this is Angela from your dedicated VIP support team. It's always a pleasure to speak with you!",
                    "regular": "Hi {customer_name}, this is Angela from customer support. I hope you're doing well today!"
                },
                ConversationStage.PURPOSE_EXPLANATION.value: {
                    "premium": "I'm calling to personally follow up on your recent interaction with us. As a premium customer, your experience is extremely important to us.",
                    "vip": "I wanted to personally reach out to ensure your recent experience with us met your expectations. Your satisfaction is our top priority.",
                    "regular": "I'm calling to follow up on your recent interaction with us and make sure everything was resolved to your satisfaction."
                },
                ConversationStage.INFORMATION_GATHERING.value: {
                    "all": "Could you tell me how your experience was? Was everything resolved the way you expected?"
                },
                ConversationStage.RESOLUTION.value: {
                    "all": "I'm {sentiment_response} to hear that. {resolution_action}"
                },
                ConversationStage.CLOSING.value: {
                    "premium": "Thank you for being such a valued premium customer. Is there anything else I can help you with today?",
                    "vip": "It's always a pleasure speaking with you. As your VIP support representative, please don't hesitate to reach out anytime.",
                    "regular": "Thank you for your time today. Is there anything else I can assist you with?"
                }
            },
            expected_duration_minutes=3
        )
        
        # Customer satisfaction survey flow
        flows["survey"] = ConversationFlow(
            flow_id="survey",
            purpose="survey",
            stages=[
                ConversationStage.GREETING,
                ConversationStage.PURPOSE_EXPLANATION,
                ConversationStage.INFORMATION_GATHERING,
                ConversationStage.MAIN_DISCUSSION,
                ConversationStage.CLOSING
            ],
            templates={
                ConversationStage.GREETING.value: {
                    "all": "Hello {customer_name}, this is Angela from customer support. I hope you're having a great {time_of_day}!"
                },
                ConversationStage.PURPOSE_EXPLANATION.value: {
                    "premium": "I'm calling to get your valuable feedback on your recent experience with us. As a premium customer, your insights help us maintain our high standards.",
                    "vip": "I wanted to personally get your feedback on our services. Your opinion as a VIP customer is incredibly valuable to us.",
                    "regular": "I'm calling to get your feedback on your recent experience with us. Your opinion helps us improve our services."
                },
                ConversationStage.INFORMATION_GATHERING.value: {
                    "all": "On a scale of 1 to 10, how would you rate your overall experience with us recently?"
                },
                ConversationStage.MAIN_DISCUSSION.value: {
                    "positive": "That's wonderful to hear! What specifically did you like most about your experience?",
                    "neutral": "Thank you for that feedback. What could we have done better to improve your experience?",
                    "negative": "I appreciate your honest feedback. Could you tell me more about what didn't meet your expectations?"
                },
                ConversationStage.CLOSING.value: {
                    "all": "Thank you so much for taking the time to share your feedback. It really helps us improve. Have a wonderful day!"
                }
            },
            expected_duration_minutes=5
        )
        
        # Proactive support flow
        flows["proactive_support"] = ConversationFlow(
            flow_id="proactive_support",
            purpose="proactive_support",
            stages=[
                ConversationStage.GREETING,
                ConversationStage.PURPOSE_EXPLANATION,
                ConversationStage.MAIN_DISCUSSION,
                ConversationStage.RESOLUTION,
                ConversationStage.FOLLOW_UP_SCHEDULING,
                ConversationStage.CLOSING
            ],
            templates={
                ConversationStage.GREETING.value: {
                    "premium": "Hello {customer_name}, this is Angela from your premium support team.",
                    "vip": "Good {time_of_day} {customer_name}, this is Angela from your dedicated VIP support.",
                    "regular": "Hi {customer_name}, this is Angela from customer support."
                },
                ConversationStage.PURPOSE_EXPLANATION.value: {
                    "all": "I noticed some activity on your account that suggests you might need assistance, so I wanted to reach out proactively. How are things going with your {product_service}?"
                },
                ConversationStage.MAIN_DISCUSSION.value: {
                    "issue_detected": "I see that {issue_description}. Let me help you resolve this right away.",
                    "no_issue": "That's great to hear! I'm glad everything is working well for you. Is there anything else I can help optimize for you?"
                },
                ConversationStage.RESOLUTION.value: {
                    "all": "I've {resolution_action}. You should see the improvement {timeframe}. Does that work for you?"
                },
                ConversationStage.FOLLOW_UP_SCHEDULING.value: {
                    "all": "I'd like to follow up with you in {follow_up_timeframe} to make sure everything is working perfectly. Would that be okay?"
                },
                ConversationStage.CLOSING.value: {
                    "all": "Perfect! I'll reach out then. Thank you for being such a valued customer!"
                }
            },
            expected_duration_minutes=7
        )
        
        # Order update flow
        flows["order_update"] = ConversationFlow(
            flow_id="order_update",
            purpose="order_update",
            stages=[
                ConversationStage.GREETING,
                ConversationStage.PURPOSE_EXPLANATION,
                ConversationStage.MAIN_DISCUSSION,
                ConversationStage.CLOSING
            ],
            templates={
                ConversationStage.GREETING.value: {
                    "all": "Hi {customer_name}, this is Angela calling from customer support with great news about your order!"
                },
                ConversationStage.PURPOSE_EXPLANATION.value: {
                    "all": "I wanted to personally let you know that your order #{order_number} has been {order_status}."
                },
                ConversationStage.MAIN_DISCUSSION.value: {
                    "shipped": "Your order is on its way and should arrive by {delivery_date}. You can track it using tracking number {tracking_number}.",
                    "delivered": "Your order was successfully delivered! I hope you're happy with your purchase.",
                    "delayed": "Unfortunately, there's been a slight delay with your order. It will now arrive by {new_delivery_date}. I sincerely apologize for any inconvenience.",
                    "ready_pickup": "Your order is ready for pickup at our {location} location. We're open {hours}."
                },
                ConversationStage.CLOSING.value: {
                    "all": "Is there anything else I can help you with regarding your order or anything else?"
                }
            },
            expected_duration_minutes=2
        )
        
        # Retention call flow
        flows["retention"] = ConversationFlow(
            flow_id="retention",
            purpose="retention",
            stages=[
                ConversationStage.GREETING,
                ConversationStage.PURPOSE_EXPLANATION,
                ConversationStage.INFORMATION_GATHERING,
                ConversationStage.MAIN_DISCUSSION,
                ConversationStage.RESOLUTION,
                ConversationStage.CLOSING
            ],
            templates={
                ConversationStage.GREETING.value: {
                    "all": "Hello {customer_name}, this is Angela from customer support. I hope you're having a wonderful day!"
                },
                ConversationStage.PURPOSE_EXPLANATION.value: {
                    "premium": "I'm calling because you're such an important premium customer to us, and I wanted to personally check in on your experience.",
                    "vip": "As one of our most valued VIP customers, I wanted to reach out personally to see how we can continue to serve you better.",
                    "regular": "You're a valued customer, and I wanted to personally reach out to see how your experience has been with us lately."
                },
                ConversationStage.INFORMATION_GATHERING.value: {
                    "all": "How has your overall experience been with us? Is there anything we could be doing better?"
                },
                ConversationStage.MAIN_DISCUSSION.value: {
                    "satisfied": "I'm so glad to hear that! What do you value most about our service?",
                    "concerns": "I really appreciate you sharing that with me. Let me see how we can address those concerns.",
                    "considering_leaving": "I understand your concerns, and I'd love to work with you to make things right. What would it take to keep you as a happy customer?"
                },
                ConversationStage.RESOLUTION.value: {
                    "offer_made": "I'd like to offer you {retention_offer} as a token of our appreciation for your loyalty. Does that sound good?",
                    "issue_resolved": "I've taken care of {resolution_details} for you. You should see the changes {timeframe}.",
                    "escalation": "I want to make sure this gets resolved properly. I'm going to have our {specialist_type} reach out to you within {timeframe}."
                },
                ConversationStage.CLOSING.value: {
                    "all": "Thank you for being such a loyal customer. We truly value your business and look forward to serving you for years to come!"
                }
            },
            expected_duration_minutes=8
        )
        
        return flows
    
    async def get_conversation_script(
        self,
        purpose: str,
        customer_id: str,
        stage: ConversationStage,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Get conversation script for a specific stage and customer.
        
        Args:
            purpose: Purpose of the call
            customer_id: Customer identifier
            stage: Current conversation stage
            context: Additional context for template variables
            
        Returns:
            Personalized conversation script
        """
        if purpose not in self.flows:
            return f"Hello, this is Angela from customer support. How can I help you today?"
        
        flow = self.flows[purpose]
        stage_templates = flow.templates.get(stage.value, {})
        
        # Get customer information
        customer_info = await customer_db.get_customer_info(customer_id)
        if not customer_info:
            customer_type = "regular"
            customer_name = "Customer"
        else:
            customer_type = customer_info.get("customer_type", "regular")
            customer_name = customer_info.get("first_name", "Customer")
        
        # Get appropriate template
        template = stage_templates.get(customer_type) or stage_templates.get("all", "")
        
        # Prepare template variables
        template_vars = {
            "customer_name": customer_name,
            "customer_type": customer_type,
            "time_of_day": self._get_time_of_day(),
        }
        
        # Add customer-specific variables
        if customer_info:
            template_vars.update({
                "total_orders": customer_info.get("total_orders", 0),
                "support_tier": customer_info.get("support_tier", "standard"),
                "product_service": "services"  # Could be more specific based on customer data
            })
        
        # Add context variables
        if context:
            template_vars.update(context)
        
        # Format template
        try:
            return template.format(**template_vars)
        except KeyError as e:
            self.logger.warning(f"Missing template variable: {e}", purpose=purpose, stage=stage.value)
            return template
    
    def get_flow_stages(self, purpose: str) -> List[ConversationStage]:
        """Get conversation stages for a purpose."""
        if purpose not in self.flows:
            return [ConversationStage.GREETING, ConversationStage.MAIN_DISCUSSION, ConversationStage.CLOSING]
        return self.flows[purpose].stages
    
    def get_expected_duration(self, purpose: str) -> int:
        """Get expected duration for a conversation purpose."""
        if purpose not in self.flows:
            return 5
        return self.flows[purpose].expected_duration_minutes
    
    def _get_time_of_day(self) -> str:
        """Get appropriate time of day greeting."""
        from datetime import datetime
        hour = datetime.now().hour
        
        if hour < 12:
            return "morning"
        elif hour < 17:
            return "afternoon"
        else:
            return "evening"
    
    async def get_next_stage_script(
        self,
        purpose: str,
        customer_id: str,
        current_stage: ConversationStage,
        customer_response: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[ConversationStage, str]:
        """Get next conversation stage and script based on customer response.
        
        Args:
            purpose: Purpose of the call
            customer_id: Customer identifier
            current_stage: Current conversation stage
            customer_response: Customer's response
            context: Additional context
            
        Returns:
            Tuple of (next_stage, script)
        """
        if purpose not in self.flows:
            return ConversationStage.CLOSING, "Thank you for your time. Have a great day!"
        
        flow = self.flows[purpose]
        current_index = flow.stages.index(current_stage)
        
        # Determine next stage
        if current_index < len(flow.stages) - 1:
            next_stage = flow.stages[current_index + 1]
        else:
            next_stage = ConversationStage.CLOSING
        
        # Analyze customer response for context
        response_context = self._analyze_customer_response(customer_response)
        if context:
            response_context.update(context)
        
        # Get script for next stage
        script = await self.get_conversation_script(purpose, customer_id, next_stage, response_context)
        
        return next_stage, script
    
    def _analyze_customer_response(self, response: str) -> Dict[str, Any]:
        """Analyze customer response to determine context for next stage."""
        response_lower = response.lower()
        context = {}
        
        # Sentiment analysis (simple keyword-based)
        positive_words = ["good", "great", "excellent", "satisfied", "happy", "pleased", "wonderful"]
        negative_words = ["bad", "terrible", "awful", "disappointed", "frustrated", "angry", "upset"]
        
        positive_count = sum(1 for word in positive_words if word in response_lower)
        negative_count = sum(1 for word in negative_words if word in response_lower)
        
        if positive_count > negative_count:
            context["sentiment_response"] = "glad"
            context["sentiment"] = "positive"
        elif negative_count > positive_count:
            context["sentiment_response"] = "sorry"
            context["sentiment"] = "negative"
        else:
            context["sentiment_response"] = "pleased"
            context["sentiment"] = "neutral"
        
        # Specific response patterns
        if any(word in response_lower for word in ["yes", "sure", "okay", "fine"]):
            context["agreement"] = True
        elif any(word in response_lower for word in ["no", "not really", "don't think so"]):
            context["agreement"] = False
        
        return context


# Global conversation flow manager instance
conversation_flow_manager = ConversationFlowManager()
