"""
Enhanced Sentiment Analysis Service for Customer Support
Provides detailed emotional analysis and escalation detection
"""

import re
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import structlog


class EmotionalState(Enum):
    """Emotional states for customer messages."""
    ANGRY = "angry"
    FRUSTRATED = "frustrated"
    THREATENING = "threatening"
    DISAPPOINTED = "disappointed"
    NEUTRAL = "neutral"
    SATISFIED = "satisfied"
    HAPPY = "happy"
    GRATEFUL = "grateful"


class EscalationLevel(Enum):
    """Escalation levels based on message analysis."""
    NONE = "none"
    MONITOR = "monitor"
    ESCALATE = "escalate"
    URGENT_ESCALATE = "urgent_escalate"


@dataclass
class SentimentAnalysis:
    """Detailed sentiment analysis result."""
    primary_emotion: EmotionalState
    intensity: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    escalation_level: EscalationLevel
    escalation_reason: Optional[str]
    empathy_level_needed: float  # 0.0 to 1.0
    tone_adaptation: str  # suggested tone for response
    detected_keywords: List[str]
    threat_indicators: List[str]


class SentimentAnalyzer:
    """Enhanced sentiment analyzer with escalation detection."""
    
    def __init__(self):
        self.logger = structlog.get_logger().bind(component="sentiment_analyzer")
        
        # Emotional keyword patterns
        self.emotion_patterns = {
            EmotionalState.ANGRY: [
                r'\b(furious|enraged|livid|outraged|pissed|mad|angry|rage)\b',
                r'\b(hate|despise|disgusted|appalled)\b',
                r'\b(unacceptable|ridiculous|absurd|pathetic)\b'
            ],
            EmotionalState.FRUSTRATED: [
                r'\b(frustrated|annoyed|irritated|fed up|sick of)\b',
                r'\b(why can\'t|this is impossible|nothing works)\b',
                r'\b(waste of time|pointless|useless)\b'
            ],
            EmotionalState.THREATENING: [
                r'\b(sue|lawsuit|legal action|lawyer|attorney)\b',
                r'\b(report you|complain|better business bureau|bbb)\b',
                r'\b(cancel|close account|never again|boycott)\b',
                r'\b(social media|twitter|facebook|review)\b'
            ],
            EmotionalState.DISAPPOINTED: [
                r'\b(disappointed|let down|expected better|thought you were)\b',
                r'\b(used to be good|not what it used to be)\b'
            ],
            EmotionalState.SATISFIED: [
                r'\b(satisfied|content|okay|fine|acceptable)\b',
                r'\b(works|resolved|fixed|sorted)\b'
            ],
            EmotionalState.HAPPY: [
                r'\b(happy|pleased|delighted|thrilled|excited)\b',
                r'\b(love|amazing|fantastic|wonderful|excellent)\b'
            ],
            EmotionalState.GRATEFUL: [
                r'\b(thank you|thanks|grateful|appreciate|thankful)\b',
                r'\b(helpful|kind|patient|understanding)\b'
            ]
        }
        
        # Intensity modifiers
        self.intensity_modifiers = {
            'very': 1.3,
            'extremely': 1.5,
            'really': 1.2,
            'so': 1.2,
            'absolutely': 1.4,
            'completely': 1.4,
            'totally': 1.3,
            'utterly': 1.5
        }
        
        # Threat indicators
        self.threat_indicators = [
            r'\b(idiot|stupid|moron|incompetent)\b',
            r'\b(fire|fired|job|manager|supervisor)\b',
            r'\b(corporate|headquarters|ceo|president)\b',
            r'\b(media|news|expose|public)\b'
        ]
        
        # Profanity patterns (mild detection)
        self.profanity_patterns = [
            r'\b(damn|hell|crap|shit|fuck|ass|bitch)\b',
            r'\*{2,}',  # Censored profanity like ****
            r'[a-z]\*+[a-z]'  # Partially censored like s**t
        ]
    
    async def analyze_sentiment(self, message: str, customer_history: Optional[Dict] = None) -> SentimentAnalysis:
        """
        Perform comprehensive sentiment analysis on customer message.
        
        Args:
            message: Customer message text
            customer_history: Optional customer history for context
            
        Returns:
            Detailed sentiment analysis result
        """
        message_lower = message.lower()
        
        # Detect emotions and calculate scores
        emotion_scores = self._calculate_emotion_scores(message_lower)
        
        # Determine primary emotion
        primary_emotion = max(emotion_scores.items(), key=lambda x: x[1])[0]
        
        # Calculate intensity
        intensity = self._calculate_intensity(message_lower, emotion_scores[primary_emotion])
        
        # Detect threats and escalation needs
        threat_indicators = self._detect_threats(message_lower)
        escalation_level, escalation_reason = self._determine_escalation(
            primary_emotion, intensity, threat_indicators, customer_history
        )
        
        # Determine empathy level needed
        empathy_level = self._calculate_empathy_level(primary_emotion, intensity)
        
        # Suggest tone adaptation
        tone_adaptation = self._suggest_tone_adaptation(primary_emotion, intensity)
        
        # Extract detected keywords
        detected_keywords = self._extract_keywords(message_lower, primary_emotion)
        
        # Calculate confidence
        confidence = self._calculate_confidence(emotion_scores, message)
        
        result = SentimentAnalysis(
            primary_emotion=primary_emotion,
            intensity=intensity,
            confidence=confidence,
            escalation_level=escalation_level,
            escalation_reason=escalation_reason,
            empathy_level_needed=empathy_level,
            tone_adaptation=tone_adaptation,
            detected_keywords=detected_keywords,
            threat_indicators=threat_indicators
        )
        
        self.logger.info(
            "Sentiment analysis completed",
            primary_emotion=primary_emotion.value,
            intensity=intensity,
            escalation_level=escalation_level.value,
            confidence=confidence
        )
        
        return result
    
    def _calculate_emotion_scores(self, message: str) -> Dict[EmotionalState, float]:
        """Calculate scores for each emotional state."""
        scores = {emotion: 0.0 for emotion in EmotionalState}
        
        for emotion, patterns in self.emotion_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, message, re.IGNORECASE)
                scores[emotion] += len(matches) * 0.3
        
        # Check for profanity (increases negative emotions)
        profanity_count = sum(len(re.findall(pattern, message, re.IGNORECASE)) 
                             for pattern in self.profanity_patterns)
        if profanity_count > 0:
            scores[EmotionalState.ANGRY] += profanity_count * 0.4
            scores[EmotionalState.FRUSTRATED] += profanity_count * 0.2
        
        # Default to neutral if no strong emotions detected
        if all(score < 0.1 for score in scores.values()):
            scores[EmotionalState.NEUTRAL] = 0.5
        
        return scores
    
    def _calculate_intensity(self, message: str, base_score: float) -> float:
        """Calculate emotional intensity with modifiers."""
        intensity = min(base_score, 1.0)
        
        # Apply intensity modifiers
        for modifier, multiplier in self.intensity_modifiers.items():
            if modifier in message:
                intensity = min(intensity * multiplier, 1.0)
        
        # Check for caps (indicates shouting)
        caps_ratio = sum(1 for c in message if c.isupper()) / max(len(message), 1)
        if caps_ratio > 0.3:
            intensity = min(intensity * 1.3, 1.0)
        
        # Check for exclamation marks
        exclamation_count = message.count('!')
        if exclamation_count > 0:
            intensity = min(intensity + (exclamation_count * 0.1), 1.0)
        
        return intensity
    
    def _detect_threats(self, message: str) -> List[str]:
        """Detect threat indicators in the message."""
        threats = []
        
        for pattern in self.threat_indicators:
            matches = re.findall(pattern, message, re.IGNORECASE)
            threats.extend(matches)
        
        return threats
    
    def _determine_escalation(
        self, 
        emotion: EmotionalState, 
        intensity: float, 
        threats: List[str],
        customer_history: Optional[Dict] = None
    ) -> Tuple[EscalationLevel, Optional[str]]:
        """Determine if escalation is needed."""
        
        # Immediate escalation for threats
        if threats:
            return EscalationLevel.URGENT_ESCALATE, f"Threat indicators detected: {', '.join(threats)}"
        
        # High intensity negative emotions
        if emotion in [EmotionalState.ANGRY, EmotionalState.THREATENING] and intensity > 0.7:
            return EscalationLevel.ESCALATE, f"High intensity {emotion.value} customer"
        
        # Frustrated customers with history of escalations
        if (emotion == EmotionalState.FRUSTRATED and intensity > 0.6 and 
            customer_history and customer_history.get('escalation_history', 0) > 0):
            return EscalationLevel.ESCALATE, "Frustrated customer with escalation history"
        
        # Monitor negative emotions
        if emotion in [EmotionalState.ANGRY, EmotionalState.FRUSTRATED, EmotionalState.DISAPPOINTED]:
            return EscalationLevel.MONITOR, f"Negative emotion detected: {emotion.value}"
        
        return EscalationLevel.NONE, None
    
    def _calculate_empathy_level(self, emotion: EmotionalState, intensity: float) -> float:
        """Calculate required empathy level for response."""
        base_empathy = {
            EmotionalState.ANGRY: 0.9,
            EmotionalState.FRUSTRATED: 0.8,
            EmotionalState.THREATENING: 0.9,
            EmotionalState.DISAPPOINTED: 0.7,
            EmotionalState.NEUTRAL: 0.5,
            EmotionalState.SATISFIED: 0.3,
            EmotionalState.HAPPY: 0.2,
            EmotionalState.GRATEFUL: 0.2
        }
        
        return min(base_empathy[emotion] * (1 + intensity * 0.5), 1.0)

    def _suggest_tone_adaptation(self, emotion: EmotionalState, intensity: float) -> str:
        """Suggest tone adaptation for response."""
        if emotion in [EmotionalState.ANGRY, EmotionalState.THREATENING]:
            return "apologetic_de_escalation" if intensity > 0.6 else "empathetic_solution_focused"
        elif emotion == EmotionalState.FRUSTRATED:
            return "understanding_action_oriented"
        elif emotion == EmotionalState.DISAPPOINTED:
            return "apologetic_reassuring"
        elif emotion in [EmotionalState.HAPPY, EmotionalState.GRATEFUL]:
            return "positive_reinforcing"
        elif emotion == EmotionalState.SATISFIED:
            return "professional_helpful"
        else:
            return "neutral_professional"

    def _extract_keywords(self, message: str, emotion: EmotionalState) -> List[str]:
        """Extract relevant keywords for the detected emotion."""
        keywords = []

        if emotion in self.emotion_patterns:
            for pattern in self.emotion_patterns[emotion]:
                matches = re.findall(pattern, message, re.IGNORECASE)
                keywords.extend(matches)

        return list(set(keywords))  # Remove duplicates

    def _calculate_confidence(self, emotion_scores: Dict[EmotionalState, float], message: str) -> float:
        """Calculate confidence in the sentiment analysis."""
        max_score = max(emotion_scores.values())
        second_max = sorted(emotion_scores.values(), reverse=True)[1] if len(emotion_scores) > 1 else 0

        # Higher confidence when there's a clear winner
        score_difference = max_score - second_max

        # Adjust for message length (longer messages generally more reliable)
        length_factor = min(len(message.split()) / 10, 1.0)

        confidence = min((score_difference + 0.3) * length_factor + 0.4, 1.0)

        return confidence
