"""FastAPI application for the Smart Customer Support Orchestrator."""

import asyncio
import logging
from contextlib import asynccontextmanager
from datetime import datetime
from typing import List, Dict, Any, Optional

from fastapi import FastAPI, HTTPException, BackgroundTasks, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import structlog
import uvicorn
import time
import random

from ..orchestrator import SupportOrchestrator
from ..models import CustomerMessage, SupportTicket, SystemMetrics
from ..services.voice_service import voice_service
from ..services.customer_database import customer_db
from ..services.conversation_manager import conversation_manager
from ..services.outbound_call_manager import outbound_call_manager, CallPurpose
from ..services.campaign_manager import campaign_manager, CampaignType
from ..services.phone_service import phone_service
from ..config import settings

# Setup logger
logger = structlog.get_logger(__name__)


async def generate_personalized_response(
    transcript: str,
    customer_info: dict,
    customer_name: str,
    conversation_context: dict = None
) -> str:
    """Generate a personalized AI response based on customer context and transcript."""

    # Import the new services
    from ..services.sentiment_analyzer import SentimentAnalyzer
    from ..services.empathetic_response_generator import EmpatheticResponseGenerator
    from ..services.order_history_summarizer import OrderHistorySummarizer
    from ..services.action_link_generator import ActionLinkGenerator

    # Initialize services
    sentiment_analyzer = SentimentAnalyzer()
    empathetic_generator = EmpatheticResponseGenerator()
    order_summarizer = OrderHistorySummarizer()
    action_generator = ActionLinkGenerator()

    # Normalize transcript for better matching
    transcript_lower = transcript.lower().strip()

    # Check if this is a follow-up in an ongoing conversation
    is_follow_up = conversation_context and len(conversation_context.get('recent_turns', [])) > 0
    recent_context = ""

    if is_follow_up:
        recent_turns = conversation_context.get('recent_turns', [])
        if recent_turns:
            last_turn = recent_turns[-1]
            recent_context = f"Previous context: Customer said '{last_turn.get('customer_message', '')}' and I responded '{last_turn.get('ai_response', '')[:100]}...'"

    # Order status queries with intelligent summarization
    if any(word in transcript_lower for word in ['order', 'orders', 'status', 'delivery', 'shipped', 'tracking']):
        context_prefix = f"Continuing our conversation, " if is_follow_up else ""
        if customer_info:
            # Check if we should summarize instead of asking for more info
            should_summarize = order_summarizer.should_summarize_instead_of_asking(customer_info)

            if should_summarize:
                # Provide intelligent order history summary
                order_summary = await order_summarizer.summarize_order_history(customer_info)

                # Get actual order information from database for recent details
                try:
                    order_info = await customer_db.get_customer_orders(customer_info.get('customer_id', ''))
                    recent_orders_detail = ""

                    if order_info and len(order_info) > 0:
                        recent_orders_detail = "\n\n📦 **Recent Order Details:**\n"
                        for order in order_info[:2]:  # Show last 2 orders with details
                            recent_orders_detail += f"• Order #{order.get('order_id', 'N/A')} - {order.get('status', 'Unknown')} - ${order.get('total', 0):.2f}"
                            if order.get('tracking_number'):
                                recent_orders_detail += f" (Tracking: {order['tracking_number']})"
                            recent_orders_detail += "\n"

                    # Generate contextual action links for orders
                    action_links = action_generator.generate_contextual_actions(
                        transcript, customer_info, max_actions=3
                    )
                    action_links_text = action_generator.format_actions_for_response(action_links)

                    return f"{context_prefix}Hi {customer_name}! {order_summary.summary_text}{recent_orders_detail}{action_links_text}\n\nWhat specific order information can I help you with?"

                except Exception as e:
                    logger.error(f"Error fetching order info: {e}")
                    return f"{context_prefix}Hi {customer_name}! {order_summary.summary_text}\n\nI'd be happy to help you with any specific order questions. What would you like to know?"
            else:
                # For customers with limited history, ask for more info
                return f"{context_prefix}I'd be happy to help you check your order status, {customer_name}. As a {customer_info.get('customer_type', 'valued')} customer, you have priority support. Could you provide me with your order number?"
        else:
            return f"{context_prefix}I can help you check your order status. Could you please provide your order number or email address?"

    # Account/spending queries with intelligent summarization
    elif any(word in transcript_lower for word in ['account', 'spending', 'spent', 'total', 'money', 'balance', 'owe', 'bill', 'billing']):
        context_prefix = f"Continuing our conversation, " if is_follow_up else ""
        if customer_info:
            try:
                # Generate comprehensive order and account summary
                order_summary = await order_summarizer.summarize_order_history(customer_info)

                # Get comprehensive account information
                total_spent = customer_info.get('total_spent', 0)
                total_orders = customer_info.get('total_orders', 0)
                customer_type = customer_info.get('customer_type', 'valued')
                support_tier = customer_info.get('support_tier', 'standard')

                # Try to get billing info
                billing_info = None
                try:
                    billing_info = await customer_db.get_customer_billing_info(customer_info.get('customer_id', ''))
                except:
                    pass

                account_text = f"{context_prefix}Here's your complete account overview, {customer_name}!\n\n"
                account_text += f"📊 **Account Summary**: {order_summary.summary_text}\n\n"
                account_text += f"👤 **Account Details:**\n"
                account_text += f"• Customer Type: {customer_type.title()}\n"
                account_text += f"• Support Tier: {support_tier.title()}\n"
                account_text += f"• Loyalty Tier: {order_summary.loyalty_tier}\n"
                account_text += f"• Average Order Value: ${order_summary.average_order_value:.2f}\n"

                if billing_info:
                    balance = billing_info.get('current_balance', 0)
                    last_payment = billing_info.get('last_payment_date', 'N/A')
                    account_text += f"💳 Current Balance: ${balance:.2f}\n"
                    account_text += f"📅 Last Payment: {last_payment}\n"

                    if balance > 0:
                        account_text += f"\n⚠️ You have an outstanding balance of ${balance:.2f}."
                    else:
                        account_text += f"\n✅ Your account is current with no outstanding balance."
                else:
                    account_text += f"💳 Account Status: Current\n"

                account_text += f"\n\nAs our {customer_type} customer, you have {support_tier} support access. Is there anything specific about your account you'd like to know more about?"
                return account_text

            except Exception as e:
                logger.error(f"Error fetching account info: {e}")
                total_spent = customer_info.get('total_spent', 0)
                total_orders = customer_info.get('total_orders', 0)
                return f"{context_prefix}I can see your account details, {customer_name}! You've spent ${total_spent:.2f} across {total_orders} orders as our {customer_info.get('customer_type', 'valued')} customer. Is there something specific about your account you'd like to know?"
        else:
            return f"{context_prefix}I can help you with your account information. Could you please verify your email address or customer ID?"

    # Refund queries
    elif any(word in transcript_lower for word in ['refund', 'return', 'money back', 'cancel']):
        if customer_info:
            return f"Hi {customer_name}! I understand you're asking about a refund. As a {customer_info.get('customer_type', 'valued')} customer with {customer_info.get('support_tier', 'standard')} support, I'll be happy to help you with this. Could you tell me which order you'd like to return or the reason for the refund?"
        else:
            return f"Hi! I can help you with refund requests. Could you please provide your order number and the reason for the refund?"

    # Help/support queries
    elif any(word in transcript_lower for word in ['help', 'support', 'assist', 'problem', 'issue']):
        if customer_info:
            return f"Hi {customer_name}! I'm here to help you. As a {customer_info.get('customer_type', 'valued')} customer with {customer_info.get('support_tier', 'standard')} support tier, you have access to priority assistance. What can I help you with today?"
        else:
            return f"Hi! I'm Angela from customer support and I'm here to help you. What can I assist you with today?"

    # Greeting responses
    elif any(word in transcript_lower for word in ['hi', 'hello', 'hey', 'good morning', 'good afternoon']):
        if customer_info:
            return f"Hello {customer_name}! It's great to hear from you. I can see you're a {customer_info.get('customer_type', 'valued')} customer with {customer_info.get('total_orders', 0)} orders. How can I assist you today?"
        else:
            return f"Hello! I'm Angela from customer support. How can I help you today?"

    # Enhanced default response with sentiment analysis and empathy
    else:
        # Perform sentiment analysis
        sentiment_analysis = await sentiment_analyzer.analyze_sentiment(transcript, customer_info)

        # Generate empathetic response components
        empathetic_response = await empathetic_generator.generate_empathetic_response(
            sentiment_analysis, transcript, customer_info, conversation_context
        )

        # Check if we should summarize order history instead of asking for more info
        should_summarize = order_summarizer.should_summarize_instead_of_asking(customer_info or {})
        order_summary_text = ""

        if should_summarize and customer_info:
            order_summary = await order_summarizer.summarize_order_history(customer_info)
            order_summary_text = f"\n\n📋 **Account Summary**: {order_summary.summary_text}"

        # Generate contextual action links
        action_links = action_generator.generate_contextual_actions(
            transcript, customer_info, sentiment_analysis, max_actions=3
        )

        # Format action links for response
        action_links_text = action_generator.format_actions_for_response(action_links)

        # Build the complete response
        if is_follow_up and recent_context:
            # This is a follow-up message in an ongoing conversation
            base_response = f"Thank you for that additional information, {customer_name}. {empathetic_response['empathy']}"
            if empathetic_response.get('escalation_note'):
                base_response += f" {empathetic_response['escalation_note']}"
        else:
            # Format the complete empathetic response
            base_response = empathetic_generator.format_complete_response(
                empathetic_response,
                "I'm here to help you with whatever you need."
            )

        # Combine all components
        complete_response = base_response + order_summary_text + action_links_text

        return complete_response


# Global orchestrator instance
orchestrator: SupportOrchestrator = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global orchestrator
    
    # Startup
    logging.basicConfig(level=getattr(logging, settings.log_level))
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    logger = structlog.get_logger()
    logger.info("Starting Smart Customer Support Orchestrator")
    
    # Initialize orchestrator
    orchestrator = SupportOrchestrator()
    
    yield
    
    # Shutdown
    if orchestrator:
        await orchestrator.shutdown()
    logger.info("Smart Customer Support Orchestrator stopped")


# Create FastAPI app
app = FastAPI(
    title="Smart Customer Support Orchestrator",
    description="Multi-Agent AI System for Automated Customer Support",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files (HTML, CSS, JS)
app.mount("/static", StaticFiles(directory="."), name="static")


# Request/Response models
class MessageRequest(BaseModel):
    """Request model for processing customer messages."""
    customer_id: str
    content: str
    channel: str = "web"
    metadata: Dict[str, Any] = {}


class MessageResponse(BaseModel):
    """Response model for processed messages."""
    ticket_id: str
    message_id: str
    final_response: str
    status: str
    processing_time: float
    escalated: bool
    escalation_reason: Optional[str] = None


class BatchMessageRequest(BaseModel):
    """Request model for batch message processing."""
    messages: List[MessageRequest]


class BatchMessageResponse(BaseModel):
    """Response model for batch processing."""
    processed_count: int
    successful_count: int
    failed_count: int
    tickets: List[MessageResponse]


# API Routes
@app.get("/")
async def root():
    """Serve the main HTML interface."""
    return FileResponse("index.html")

@app.get("/voice.html")
async def voice_interface():
    """Serve the voice interface."""
    return FileResponse("voice.html")

@app.get("/test_voice_system.html")
async def test_interface():
    """Serve the test interface."""
    return FileResponse("test_voice_system.html")

@app.get("/api")
async def api_info():
    """API information endpoint."""
    return {
        "name": "Smart Customer Support Orchestrator",
        "version": "1.0.0",
        "description": "Multi-Agent AI System for Automated Customer Support",
        "status": "running",
        "timestamp": datetime.utcnow().isoformat()
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    health_status = await orchestrator.health_check()
    
    if health_status["status"] != "healthy":
        raise HTTPException(status_code=503, detail=health_status)
    
    return health_status


@app.post("/process-message")
async def process_message(request: MessageRequest):
    """Process a single customer message using enhanced natural conversation flow with automatic language detection."""
    try:
        start_time = time.time()

        # Import enhanced services
        from ..services.enhanced_response_generator import EnhancedResponseGenerator
        from ..services.conversation_manager import conversation_manager
        from ..services.sentiment_analyzer import SentimentAnalyzer, EscalationLevel
        from ..services.multilingual_service import MultilingualService

        # Initialize multilingual service for automatic language detection
        multilingual_service = MultilingualService()

        # Detect language automatically
        language_detection = await multilingual_service.detect_language(request.content)
        detected_language = language_detection.detected_language
        needs_translation = language_detection.needs_translation

        # Translate to English for processing if needed
        processed_content = request.content
        if needs_translation:
            translation_result = await multilingual_service.translate_text(
                request.content,
                target_language="en",
                source_language=detected_language
            )
            processed_content = translation_result.translated_text

        # Get customer information
        customer_info = await customer_db.get_customer_info(request.customer_id)
        customer_name = customer_info.get("first_name", "Customer") if customer_info else "Customer"

        # Generate conversation ID if not provided
        conversation_id = request.metadata.get("conversation_id") or f"conv_{int(time.time())}_{request.customer_id}"

        # Check if we should use natural conversation flow (use processed content for analysis)
        use_natural_flow = conversation_manager.should_use_natural_flow(conversation_id, processed_content)

        if use_natural_flow:
            # Use enhanced response generator with natural conversation flow
            enhanced_generator = EnhancedResponseGenerator()

            # Get or create natural conversation context
            conversation_context = conversation_manager.get_natural_conversation_context(conversation_id)

            # Generate natural response (using processed English content)
            ai_response, response_metadata = await enhanced_generator.generate_natural_response(
                customer_message=processed_content,
                customer_info=customer_info,
                conversation_context=conversation_context,
                conversation_id=conversation_id
            )

            # Update conversation context
            if not conversation_context:
                await conversation_manager.start_natural_conversation(
                    customer_id=request.customer_id,
                    conversation_id=conversation_id
                )

            # Process natural conversation turn (using processed English content)
            _, updated_context = await conversation_manager.process_natural_conversation_turn(
                conversation_id=conversation_id,
                customer_message=processed_content,
                customer_info=customer_info
            )

            # Extract metadata for response
            brain_state_updates = {
                "emotional_state": response_metadata["turn_analysis"]["sentiment"],
                "emotional_intensity": response_metadata["empathy_applied"],
                "empathy_level": response_metadata["empathy_applied"],
                "conversation_phase": response_metadata["conversation_phase"],
                "transition_readiness": response_metadata["turn_analysis"]["transition_readiness"],
                "business_intent_detected": response_metadata["business_intent_detected"]
            }

            action_links = response_metadata.get("action_links", [])

        else:
            # Use traditional response generation for direct business requests (using processed English content)
            ai_response = await generate_personalized_response(
                transcript=processed_content,
                customer_info=customer_info,
                customer_name=customer_name,
                conversation_context=None
            )

            # Perform sentiment analysis for escalation detection (using processed English content)
            sentiment_analyzer = SentimentAnalyzer()
            sentiment_analysis = await sentiment_analyzer.analyze_sentiment(processed_content, customer_info)

            brain_state_updates = {
                "emotional_state": sentiment_analysis.primary_emotion.value,
                "emotional_intensity": sentiment_analysis.intensity,
                "empathy_level": sentiment_analysis.empathy_level_needed,
                "confidence_level": sentiment_analysis.confidence,
                "tone_adaptation": sentiment_analysis.tone_adaptation
            }

            # Generate contextual action links (using processed English content)
            from ..services.action_link_generator import ActionLinkGenerator
            action_generator = ActionLinkGenerator()
            action_links = action_generator.generate_contextual_actions(
                processed_content, customer_info, sentiment_analysis, max_actions=3
            )

            # Determine escalation status
            escalation_needed = sentiment_analysis.escalation_level in [EscalationLevel.ESCALATE, EscalationLevel.URGENT_ESCALATE]

        processing_time = time.time() - start_time

        # Generate bilingual responses if needed
        final_response_eng = ai_response
        final_response_other = None

        if needs_translation and detected_language != "en":
            # Translate the English response back to the original language
            response_translation = await multilingual_service.translate_text(
                ai_response,
                target_language=detected_language,
                source_language="en"
            )
            final_response_other = response_translation.translated_text

        # Generate contextual follow-up questions
        follow_up_questions = []
        if use_natural_flow and updated_context:
            # Use natural conversation follow-ups
            if updated_context.current_phase.value == "business_focus":
                follow_up_questions = [
                    "Is there anything else I can help you with regarding this?",
                    "Would you like me to provide more details?",
                    "Do you have any other questions about your account?"
                ]
            elif updated_context.current_phase.value == "casual_chat":
                follow_up_questions = [
                    "How has your day been going?",
                    "Is there anything I can help you with today?",
                    "What brings you here today?"
                ]
            else:
                follow_up_questions = [
                    "Is there anything else I can assist you with?",
                    "How can I help you further?",
                    "What else would you like to know?"
                ]
        else:
            # Use sentiment-based follow-ups for traditional flow
            if 'sentiment_analysis' in locals():
                if sentiment_analysis.primary_emotion.value in ["angry", "frustrated"]:
                    follow_up_questions = [
                        "Is there anything specific I can do to resolve this immediately?",
                        "Would you like me to escalate this to a supervisor?",
                        "Can I provide you with a direct contact for faster resolution?"
                    ]
                elif sentiment_analysis.primary_emotion.value in ["happy", "grateful"]:
                    follow_up_questions = [
                        "Is there anything else I can help you with today?",
                        "Would you like to leave feedback about your experience?",
                        "Can I assist you with any other questions?"
                    ]
                else:
                    follow_up_questions = [
                        "Is there anything else I can help you with?",
                        "Do you need more details about any specific item?",
                        "Would you like me to check anything else for you?"
                    ]
            else:
                follow_up_questions = [
                    "Is there anything else I can help you with?",
                    "How can I assist you further?",
                    "What else would you like to know?"
                ]

        # Determine escalation status
        escalation_needed = False
        if use_natural_flow:
            escalation_needed = response_metadata.get("turn_analysis", {}).get("sentiment") == "negative"
        elif 'sentiment_analysis' in locals():
            escalation_needed = sentiment_analysis.escalation_level in [EscalationLevel.ESCALATE, EscalationLevel.URGENT_ESCALATE]

        # Calculate quality score
        quality_score = 0.9
        if use_natural_flow:
            quality_score = min(0.95, 0.8 + (response_metadata.get("empathy_applied", 0.5) * 0.15))
        elif 'sentiment_analysis' in locals():
            quality_score = min(0.95, 0.7 + (sentiment_analysis.confidence * 0.25))

        # Get escalation reason
        escalation_reason = None
        if escalation_needed:
            if use_natural_flow:
                escalation_reason = "Negative sentiment detected in natural conversation"
            elif 'sentiment_analysis' in locals():
                escalation_reason = sentiment_analysis.escalation_reason

        # Determine collective intelligence activation
        collective_intelligence = False
        if use_natural_flow:
            collective_intelligence = response_metadata.get("turn_analysis", {}).get("sentiment") == "negative"
        elif 'sentiment_analysis' in locals():
            collective_intelligence = sentiment_analysis.escalation_level == EscalationLevel.URGENT_ESCALATE

        # Return enhanced response with bilingual support
        response_data = {
            "workflow_id": f"workflow_{int(time.time())}",
            "processing_time": processing_time,
            "agents_involved": ["intake", "knowledge", "resolution", "quality", "natural_conversation"],
            "brain_state_updates": brain_state_updates,
            "quality_score": quality_score,
            "escalation_needed": escalation_needed,
            "escalation_reason": escalation_reason,
            "swarm_activated": escalation_needed,
            "collective_intelligence": collective_intelligence,
            "follow_up_questions": follow_up_questions,
            "action_links": [
                {
                    "label": getattr(link, 'label', 'Action'),
                    "url": getattr(link, 'url', '#'),
                    "description": getattr(link, 'description', ''),
                    "icon": getattr(link, 'icon', 'link'),
                    "priority": getattr(link, 'priority', 'medium')
                } for link in action_links
            ] if action_links else [],
            "conversation_continues": True,
            "conversation_id": conversation_id,
            "natural_conversation_metadata": response_metadata if use_natural_flow else None
        }

        # Add bilingual response structure
        if detected_language == "en" or not needs_translation:
            # English only response
            response_data["final_response"] = final_response_eng
        else:
            # Bilingual response
            response_data["final_response_eng"] = final_response_eng
            response_data["final_response_other"] = final_response_other

        return response_data

    except Exception as e:
        logger.error(f"Error processing message: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing message: {str(e)}")


@app.post("/process-message-legacy", response_model=MessageResponse)
async def process_message_legacy(request: MessageRequest):
    """Process a single customer message using legacy orchestrator."""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")

    try:
        # Create customer message
        customer_message = CustomerMessage(
            message_id=f"msg_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{request.customer_id}",
            customer_id=request.customer_id,
            content=request.content,
            channel=request.channel,
            metadata=request.metadata
        )

        # Process message
        ticket = await orchestrator.process_customer_message(customer_message)

        # Return response
        return MessageResponse(
            ticket_id=ticket.ticket_id,
            message_id=ticket.customer_message.message_id,
            final_response=ticket.final_response,
            status=ticket.status,
            processing_time=ticket.total_processing_time,
            escalated=ticket.escalated,
            escalation_reason=ticket.escalation_reason
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing message: {str(e)}")


@app.post("/process-batch", response_model=BatchMessageResponse)
async def process_batch_messages(request: BatchMessageRequest):
    """Process multiple customer messages in batch."""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        # Create customer messages
        customer_messages = []
        for i, msg_req in enumerate(request.messages):
            customer_message = CustomerMessage(
                message_id=f"batch_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{i}",
                customer_id=msg_req.customer_id,
                content=msg_req.content,
                channel=msg_req.channel,
                metadata=msg_req.metadata
            )
            customer_messages.append(customer_message)
        
        # Process batch
        tickets = await orchestrator.process_batch_messages(customer_messages)
        
        # Convert to response format
        ticket_responses = []
        for ticket in tickets:
            ticket_responses.append(MessageResponse(
                ticket_id=ticket.ticket_id,
                message_id=ticket.customer_message.message_id,
                final_response=ticket.final_response,
                status=ticket.status,
                processing_time=ticket.total_processing_time,
                escalated=ticket.escalated,
                escalation_reason=ticket.escalation_reason
            ))
        
        return BatchMessageResponse(
            processed_count=len(request.messages),
            successful_count=len(tickets),
            failed_count=len(request.messages) - len(tickets),
            tickets=ticket_responses
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing batch: {str(e)}")


@app.get("/metrics", response_model=SystemMetrics)
async def get_metrics():
    """Get system performance metrics."""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    return await orchestrator.get_system_metrics()


@app.get("/agents/status")
async def get_agent_status():
    """Get status and metrics for all agents."""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")

    return await orchestrator.get_agent_status()


@app.get("/api-keys/status")
async def get_api_key_status():
    """Get API key usage statistics."""
    try:
        from ..utils.api_key_manager import api_key_manager
        return api_key_manager.get_stats()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting API key stats: {str(e)}")


@app.get("/ticket/{ticket_id}")
async def get_ticket(ticket_id: str):
    """Get details for a specific ticket (placeholder - would need database)."""
    # This would typically query a database for ticket details
    # For now, return a placeholder response
    return {
        "ticket_id": ticket_id,
        "status": "This endpoint would return full ticket details from database",
        "note": "Database integration needed for full implementation"
    }


@app.post("/voice/speech-to-text")
async def speech_to_text(audio: UploadFile = File(...)):
    """Convert speech audio to text."""
    try:
        audio_data = await audio.read()
        transcript = await voice_service.speech_to_text(audio_data)

        return {
            "transcript": transcript,
            "status": "success"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Speech-to-text error: {str(e)}")


@app.post("/voice/text-to-speech")
async def text_to_speech(request: dict):
    """Convert text to speech audio."""
    try:
        text = request.get("text", "")
        voice_config = request.get("voice_config", {})

        if not text:
            raise HTTPException(status_code=400, detail="Text is required")

        audio_data = await voice_service.text_to_speech(text, voice_config)

        return Response(
            content=audio_data,
            media_type="audio/mpeg",
            headers={"Content-Disposition": "attachment; filename=speech.mp3"}
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Text-to-speech error: {str(e)}")


@app.post("/voice/process-voice-message")
async def process_voice_message(
    audio: UploadFile = File(...),
    customer_id: str = "CUST_001",
    conversation_id: str = None
):
    """Process voice message end-to-end: speech-to-text, AI processing, text-to-speech."""
    try:
        # Step 1: Convert speech to text
        audio_data = await audio.read()
        transcript = await voice_service.speech_to_text(audio_data)

        if not transcript:
            raise HTTPException(status_code=400, detail="Could not transcribe audio")

        logger.info("Voice message transcribed", transcript=transcript, customer_id=customer_id)

        # Step 2: Get or create conversation
        if not conversation_id:
            conversation_id = await conversation_manager.start_conversation(
                customer_id=customer_id,
                channel="voice",
                initial_message=transcript
            )

        # Get conversation context
        conversation_context = await conversation_manager.get_conversation_context(conversation_id)

        # Step 3: Get customer information for context
        customer_info = await customer_db.get_customer_info(customer_id)
        customer_name = customer_info.get("first_name", "Customer") if customer_info else "Customer"

        # Step 4: Generate AI response using customer and conversation context
        ai_response = await generate_personalized_response(
            transcript,
            customer_info,
            customer_name,
            conversation_context
        )

        # Step 5: Add turn to conversation
        await conversation_manager.add_turn(
            conversation_id=conversation_id,
            customer_message=transcript,
            ai_response=ai_response,
            processing_time=1.0,
            quality_score=0.9,
            follow_up_questions=["Is there anything else I can help you with?"]
        )

        # Step 6: Convert AI response to speech
        speech_audio = await voice_service.text_to_speech(ai_response)

        logger.info("Voice processing completed",
                   transcript_length=len(transcript),
                   response_length=len(ai_response),
                   audio_size=len(speech_audio),
                   conversation_id=conversation_id)

        return {
            "transcript": transcript,
            "ai_response": ai_response,
            "audio_response": speech_audio.hex(),  # Return as hex string
            "customer_id": customer_id,
            "customer_name": customer_name,
            "conversation_id": conversation_id,
            "conversation_continues": True,
            "follow_up_questions": ["Is there anything else I can help you with?"],
            "processing_time": "voice_processing"
        }

    except Exception as e:
        logger.error("Voice processing error", error=str(e), customer_id=customer_id)
        raise HTTPException(status_code=500, detail=f"Voice processing error: {str(e)}")


@app.post("/voice/introduction")
async def voice_introduction(request: dict):
    """Generate Angela's personalized voice introduction with database lookup."""
    try:
        customer_name = request.get("customer_name", "").strip()
        customer_id = request.get("customer_id", "").strip()
        customer_email = request.get("customer_email", "").strip()

        # Try to identify customer from database
        customer_info = None
        identified_name = ""
        final_customer_id = customer_id

        # Priority 1: Search by email (most reliable)
        if customer_email:
            customer_info = await customer_db.search_customer_by_email(customer_email)
            if customer_info:
                identified_name = customer_info.get("first_name", "")
                final_customer_id = customer_info.get("customer_id", customer_id)
                logger.info("Customer found by email", email=customer_email, customer_id=final_customer_id)

        # Priority 2: Search by customer ID if not found by email
        if not customer_info and customer_id:
            customer_info = await customer_db.get_customer_info(customer_id)
            if customer_info:
                identified_name = customer_info.get("first_name", "")
                final_customer_id = customer_info.get("customer_id", customer_id)
                logger.info("Customer found by ID", customer_id=final_customer_id)

        # Priority 3: Search by name in database (less reliable)
        if not customer_info and customer_name:
            # Try to find customer by matching first name in mock data
            for cust_id, cust_data in customer_db._mock_customers.items():
                if cust_data.get("first_name", "").lower() == customer_name.lower():
                    customer_info = cust_data
                    identified_name = customer_info.get("first_name", "")
                    final_customer_id = cust_id
                    logger.info("Customer found by name match", name=customer_name, customer_id=final_customer_id)
                    break

        # Generate personalized introduction based on what we found
        if customer_info and identified_name:
            # We found the customer in our database
            intro_text = f"Hi! I'm Angela from customer support. Am I speaking to {identified_name}?"
            customer_identified = True
        elif customer_name:
            # We have a name but couldn't find them in database
            intro_text = f"Hi! I'm Angela from customer support. Am I speaking to {customer_name}? I'll need to verify your details."
            customer_identified = False
        elif customer_email:
            # We have email but couldn't find them
            intro_text = f"Hi! I'm Angela from customer support. I see you provided the email {customer_email}. Let me help you today."
            customer_identified = False
        else:
            # No identifying information provided
            intro_text = "Hello! I'm Angela from customer support. Could you please tell me your name so I can assist you better?"
            customer_identified = False

        # Convert to speech with high-quality voice
        voice_config = {
            "voice_name": "en-US-Journey-F",  # High-quality natural voice
            "speed": 0.9  # Slightly slower for clarity and warmth
        }

        audio_data = await voice_service.text_to_speech(intro_text, voice_config)

        # Prepare customer details for frontend
        customer_details = {}
        if customer_info:
            customer_details = {
                "customer_id": final_customer_id,
                "name": identified_name,
                "email": customer_info.get("email", ""),
                "customer_type": customer_info.get("customer_type", ""),
                "support_tier": customer_info.get("support_tier", ""),
                "total_orders": customer_info.get("total_orders", 0),
                "total_spent": customer_info.get("total_spent", 0.0)
            }

        return Response(
            content=audio_data,
            media_type="audio/mpeg",
            headers={
                "Content-Disposition": "attachment; filename=angela_intro.mp3",
                "X-Angela-Message": intro_text,
                "X-Customer-Identified": str(customer_identified),
                "X-Customer-ID": final_customer_id or "unknown",
                "X-Customer-Name": identified_name or customer_name or "",
                "X-Customer-Email": customer_info.get("email", "") if customer_info else customer_email,
                "X-Customer-Details": str(customer_details) if customer_details else "{}"
            }
        )

    except Exception as e:
        logger.error("Voice introduction error", error=str(e))
        raise HTTPException(status_code=500, detail=f"Voice introduction error: {str(e)}")


@app.post("/voice/customer-lookup")
async def voice_customer_lookup(request: dict):
    """Look up customer information for voice interface."""
    try:
        customer_email = request.get("customer_email", "").strip()
        customer_name = request.get("customer_name", "").strip()
        customer_id = request.get("customer_id", "").strip()

        customer_info = None

        # Search by email first (most reliable)
        if customer_email:
            customer_info = await customer_db.search_customer_by_email(customer_email)

        # Search by customer ID if not found by email
        if not customer_info and customer_id:
            customer_info = await customer_db.get_customer_info(customer_id)

        # Search by name if still not found
        if not customer_info and customer_name:
            for cust_id, cust_data in customer_db._mock_customers.items():
                if cust_data.get("first_name", "").lower() == customer_name.lower():
                    customer_info = cust_data
                    break

        if customer_info:
            return {
                "found": True,
                "customer": {
                    "customer_id": customer_info.get("customer_id"),
                    "name": customer_info.get("first_name", ""),
                    "email": customer_info.get("email", ""),
                    "customer_type": customer_info.get("customer_type", ""),
                    "support_tier": customer_info.get("support_tier", ""),
                    "total_orders": customer_info.get("total_orders", 0),
                    "total_spent": customer_info.get("total_spent", 0.0),
                    "last_contact": customer_info.get("last_contact", ""),
                    "preferred_channel": customer_info.get("preferred_channel", "")
                }
            }
        else:
            return {
                "found": False,
                "message": "Customer not found in our database"
            }

    except Exception as e:
        logger.error("Customer lookup error", error=str(e))
        raise HTTPException(status_code=500, detail=f"Customer lookup error: {str(e)}")


@app.post("/conversation/start")
async def start_conversation(request: dict):
    """Start a new conversation."""
    try:
        customer_id = request.get("customer_id", "CUST_001")
        channel = request.get("channel", "voice")
        initial_message = request.get("initial_message")

        conversation_id = await conversation_manager.start_conversation(
            customer_id=customer_id,
            channel=channel,
            initial_message=initial_message
        )

        return {
            "conversation_id": conversation_id,
            "status": "started",
            "customer_id": customer_id,
            "channel": channel
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting conversation: {str(e)}")


@app.get("/conversation/{conversation_id}")
async def get_conversation(conversation_id: str):
    """Get conversation details."""
    try:
        conversation = await conversation_manager.get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")

        return conversation.dict()

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting conversation: {str(e)}")


@app.post("/conversation/{conversation_id}/end")
async def end_conversation(conversation_id: str, request: dict = None):
    """End a conversation."""
    try:
        resolved = request.get("resolved", True) if request else True
        success = await conversation_manager.end_conversation(conversation_id, resolved)

        if not success:
            raise HTTPException(status_code=404, detail="Conversation not found")

        return {
            "conversation_id": conversation_id,
            "status": "ended",
            "resolved": resolved
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error ending conversation: {str(e)}")


@app.post("/voice/account-query")
async def process_account_query(request: MessageRequest):
    """Direct account query processing for voice demo."""
    try:
        from ..agents.account_agent import AccountAgent
        from ..models import AgentContext

        # Create customer message
        customer_message = CustomerMessage(
            message_id=f"voice_account_{int(time.time())}",
            customer_id=request.customer_id,
            content=request.content,
            channel="voice"
        )

        # Create context
        context = AgentContext(
            message_id=customer_message.message_id,
            customer_message=customer_message
        )

        # Process with account agent
        account_agent = AccountAgent()
        result_context = await account_agent.process(context)

        # Get account response
        if hasattr(result_context, 'account_info') and result_context.account_info:
            account_info = result_context.account_info
            if account_info.get('found'):
                final_response = account_info.get('message', 'Account information retrieved.')
                agents_involved = ["account"]
            else:
                final_response = account_info.get('message', 'Account information not found.')
                agents_involved = ["account"]
        else:
            # Fallback to general response
            final_response = "Thank you for contacting us. I've carefully reviewed your request and I'm here to provide you with the best possible assistance."
            agents_involved = ["intake", "knowledge", "resolution", "quality"]

        return ProcessingResponse(
            workflow_id=f"voice_account_{int(time.time())}_{random.randint(1000, 9999)}",
            processing_time=0.5,
            agents_involved=agents_involved,
            final_response=final_response,
            brain_state_updates={
                "emotional_analysis": {
                    "sentiment": "neutral",
                    "intensity": 0.1,
                    "empathy_applied": 0.7
                },
                "swarm_activated": False,
                "collective_intelligence": False,
                "consciousness_evolution": {
                    "agent_alpha": 0.01,
                    "agent_beta": 0.01,
                    "agent_gamma": 0.01,
                    "agent_delta": 0.01
                }
            },
            quality_score=0.95,
            escalation_needed=False,
            swarm_activated=False,
            collective_intelligence=False,
            follow_up_questions=["Is there anything else I can help you with?"],
            conversation_continues=True,
            conversation_id=f"voice_conv_{int(time.time())}"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Account query error: {str(e)}")


# Demo endpoints for hackathon
@app.post("/demo/angry-customer")
async def demo_angry_customer():
    """Demo endpoint: Process an angry customer scenario."""
    demo_message = MessageRequest(
        customer_id="demo_customer_001",
        content="I am absolutely furious! My order #12345 arrived completely damaged and your return process is a nightmare! I've been a loyal customer for 5 years and this is unacceptable! I want a full refund immediately and compensation for this terrible experience!",
        channel="email",
        metadata={"demo_scenario": "angry_customer", "order_id": "12345"}
    )
    
    return await process_message(demo_message)


@app.post("/demo/technical-issue")
async def demo_technical_issue():
    """Demo endpoint: Process a technical support scenario."""
    demo_message = MessageRequest(
        customer_id="demo_customer_002", 
        content="Hi, I'm having trouble with your mobile app. It keeps crashing when I try to view my order history. I've tried restarting the app and my phone but the problem persists. Can you help me troubleshoot this issue?",
        channel="chat",
        metadata={"demo_scenario": "technical_issue", "app_version": "2.1.3"}
    )
    
    return await process_message(demo_message)


@app.post("/demo/billing-question")
async def demo_billing_question():
    """Demo endpoint: Process a billing inquiry scenario."""
    demo_message = MessageRequest(
        customer_id="demo_customer_003",
        content="Hello, I noticed a charge of $29.99 on my credit card statement from your company, but I don't remember making this purchase. Could you please help me understand what this charge is for? My account <NAME_EMAIL>.",
        channel="web",
        metadata={"demo_scenario": "billing_question", "charge_amount": 29.99}
    )

    return await process_message(demo_message)


# Outbound Call Management Endpoints
@app.post("/outbound/schedule-call")
async def schedule_outbound_call(request: dict):
    """Schedule an outbound call to a customer."""
    try:
        customer_id = request.get("customer_id")
        purpose = request.get("purpose", "follow_up")
        scheduled_time = request.get("scheduled_time")
        priority = request.get("priority", 5)
        metadata = request.get("metadata", {})

        if not customer_id:
            raise HTTPException(status_code=400, detail="customer_id is required")

        # Parse scheduled time if provided
        if scheduled_time:
            from datetime import datetime
            scheduled_time = datetime.fromisoformat(scheduled_time.replace("Z", "+00:00"))

        # Convert purpose string to enum
        try:
            call_purpose = CallPurpose(purpose)
        except ValueError:
            call_purpose = CallPurpose.FOLLOW_UP

        call_id = await outbound_call_manager.schedule_call(
            customer_id=customer_id,
            purpose=call_purpose,
            scheduled_time=scheduled_time,
            priority=priority,
            metadata=metadata
        )

        return {
            "success": True,
            "call_id": call_id,
            "message": "Call scheduled successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error scheduling call: {str(e)}")


@app.get("/outbound/queue")
async def get_call_queue():
    """Get current outbound call queue."""
    try:
        queue = await outbound_call_manager.get_call_queue()
        return {
            "success": True,
            "queue": queue
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting call queue: {str(e)}")


@app.get("/outbound/active")
async def get_active_calls():
    """Get currently active outbound calls."""
    try:
        active_calls = await outbound_call_manager.get_active_calls()
        return {
            "success": True,
            "active_calls": active_calls
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting active calls: {str(e)}")


@app.get("/outbound/call/{call_id}")
async def get_call_status(call_id: str):
    """Get status of a specific outbound call."""
    try:
        call_status = await outbound_call_manager.get_call_status(call_id)
        if not call_status:
            raise HTTPException(status_code=404, detail="Call not found")

        return {
            "success": True,
            "call": call_status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting call status: {str(e)}")


@app.post("/outbound/call/{call_id}/cancel")
async def cancel_call(call_id: str):
    """Cancel a scheduled outbound call."""
    try:
        success = await outbound_call_manager.cancel_call(call_id)
        if not success:
            raise HTTPException(status_code=404, detail="Call not found or cannot be cancelled")

        return {
            "success": True,
            "message": "Call cancelled successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error cancelling call: {str(e)}")


# Campaign Management Endpoints
@app.post("/campaigns")
async def create_campaign(request: dict):
    """Create a new outbound call campaign."""
    try:
        name = request.get("name")
        campaign_type = request.get("campaign_type", "follow_up")
        purpose = request.get("purpose", "follow_up")
        target_criteria = request.get("target_criteria", {})
        start_time = request.get("start_time")
        end_time = request.get("end_time")
        max_calls_per_hour = request.get("max_calls_per_hour", 10)
        business_hours_only = request.get("business_hours_only", True)
        metadata = request.get("metadata", {})

        if not name:
            raise HTTPException(status_code=400, detail="Campaign name is required")

        if not start_time:
            raise HTTPException(status_code=400, detail="Start time is required")

        # Parse times
        from datetime import datetime
        start_time = datetime.fromisoformat(start_time.replace("Z", "+00:00"))
        if end_time:
            end_time = datetime.fromisoformat(end_time.replace("Z", "+00:00"))

        # Convert enums
        try:
            campaign_type_enum = CampaignType(campaign_type)
        except ValueError:
            campaign_type_enum = CampaignType.FOLLOW_UP

        try:
            purpose_enum = CallPurpose(purpose)
        except ValueError:
            purpose_enum = CallPurpose.FOLLOW_UP

        campaign_id = await campaign_manager.create_campaign(
            name=name,
            campaign_type=campaign_type_enum,
            purpose=purpose_enum,
            target_criteria=target_criteria,
            start_time=start_time,
            end_time=end_time,
            max_calls_per_hour=max_calls_per_hour,
            business_hours_only=business_hours_only,
            metadata=metadata
        )

        return {
            "success": True,
            "campaign_id": campaign_id,
            "message": "Campaign created successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating campaign: {str(e)}")


@app.get("/campaigns")
async def list_campaigns(status: Optional[str] = None):
    """List campaigns, optionally filtered by status."""
    try:
        from ..services.campaign_manager import CampaignStatus

        status_filter = None
        if status:
            try:
                status_filter = CampaignStatus(status)
            except ValueError:
                pass

        campaigns = await campaign_manager.list_campaigns(status_filter)
        return {
            "success": True,
            "campaigns": campaigns
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing campaigns: {str(e)}")


@app.get("/campaigns/{campaign_id}")
async def get_campaign(campaign_id: str):
    """Get campaign details."""
    try:
        campaign = await campaign_manager.get_campaign(campaign_id)
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")

        return {
            "success": True,
            "campaign": campaign
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting campaign: {str(e)}")


@app.post("/campaigns/{campaign_id}/start")
async def start_campaign(campaign_id: str):
    """Start a campaign."""
    try:
        success = await campaign_manager.start_campaign(campaign_id)
        if not success:
            raise HTTPException(status_code=400, detail="Campaign cannot be started")

        return {
            "success": True,
            "message": "Campaign started successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting campaign: {str(e)}")


@app.post("/campaigns/{campaign_id}/pause")
async def pause_campaign(campaign_id: str):
    """Pause a campaign."""
    try:
        success = await campaign_manager.pause_campaign(campaign_id)
        if not success:
            raise HTTPException(status_code=400, detail="Campaign cannot be paused")

        return {
            "success": True,
            "message": "Campaign paused successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error pausing campaign: {str(e)}")


@app.post("/campaigns/{campaign_id}/resume")
async def resume_campaign(campaign_id: str):
    """Resume a paused campaign."""
    try:
        success = await campaign_manager.resume_campaign(campaign_id)
        if not success:
            raise HTTPException(status_code=400, detail="Campaign cannot be resumed")

        return {
            "success": True,
            "message": "Campaign resumed successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error resuming campaign: {str(e)}")


@app.post("/campaigns/{campaign_id}/cancel")
async def cancel_campaign(campaign_id: str):
    """Cancel a campaign."""
    try:
        success = await campaign_manager.cancel_campaign(campaign_id)
        if not success:
            raise HTTPException(status_code=404, detail="Campaign not found")

        return {
            "success": True,
            "message": "Campaign cancelled successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error cancelling campaign: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(
        "src.api.main:app",
        host=settings.api_host,
        port=settings.api_port,
        workers=1,  # Use 1 worker for development
        log_level=settings.log_level.lower(),
        reload=settings.development_mode
    )
