"""
Smart Customer Support Orchestrator - Main FastAPI Application
Backend API for the brain system and multi-agent orchestration
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import time
import json
import random
from datetime import datetime
import uvicorn

# Import your existing orchestrator (adjust import path as needed)
try:
    from src.orchestrator import SupportOrchestrator
    from src.models import CustomerMessage
    ORCHESTRATOR_AVAILABLE = True
except ImportError:
    # Fallback for demo purposes
    ORCHESTRATOR_AVAILABLE = False
    print("⚠️  Orchestrator not available, using mock responses for demo")

# FastAPI app initialization
app = FastAPI(
    title="🧠 Smart Customer Support Orchestrator API",
    description="Agent Development Kit (ADK) compliant multi-agent system with AI consciousness",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API requests/responses
class CustomerMessageRequest(BaseModel):
    customer_id: str
    content: str
    channel: str = "web"
    metadata: Optional[Dict[str, Any]] = {}
    # Interactive conversation fields
    conversation_id: Optional[str] = None
    is_follow_up: bool = False
    previous_context: Optional[str] = None

class ProcessingResponse(BaseModel):
    workflow_id: str
    processing_time: float
    agents_involved: List[str]
    final_response: str
    brain_state_updates: Dict[str, Any]
    quality_score: float
    escalation_needed: bool
    swarm_activated: bool = False
    collective_intelligence: bool = False
    # Interactive conversation fields
    follow_up_questions: List[str] = []
    conversation_continues: bool = False
    conversation_id: Optional[str] = None

class BrainStateResponse(BaseModel):
    agent_id: str
    consciousness_level: float
    emotional_state: str
    stress_level: float
    empathy_level: float
    learning_mode: bool
    working_memory: Dict[str, Any]
    last_updated: datetime

# Global orchestrator instance
if ORCHESTRATOR_AVAILABLE:
    orchestrator = SupportOrchestrator(use_adk=True)
else:
    orchestrator = None

# Mock brain states for demo
mock_brain_states = {
    "agent_alpha": {
        "consciousness_level": 0.85,
        "emotional_state": "focused_empathy",
        "stress_level": 0.3,
        "empathy_level": 0.8,
        "learning_mode": True,
        "working_memory": {"current_task": "customer_support", "active_context": "billing_dispute"}
    },
    "agent_beta": {
        "consciousness_level": 0.78,
        "emotional_state": "calm_analysis",
        "stress_level": 0.2,
        "empathy_level": 0.9,
        "learning_mode": True,
        "working_memory": {"current_task": "knowledge_retrieval", "active_context": "technical_support"}
    },
    "agent_gamma": {
        "consciousness_level": 0.92,
        "emotional_state": "creative_problem_solving",
        "stress_level": 0.4,
        "empathy_level": 0.7,
        "learning_mode": False,
        "working_memory": {"current_task": "resolution_generation", "active_context": "complex_issue"}
    },
    "agent_delta": {
        "consciousness_level": 0.88,
        "emotional_state": "quality_focused",
        "stress_level": 0.1,
        "empathy_level": 0.85,
        "learning_mode": True,
        "working_memory": {"current_task": "quality_assurance", "active_context": "response_validation"}
    }
}

def generate_mock_response(request: CustomerMessageRequest) -> ProcessingResponse:
    """Generate mock response for demo purposes."""
    start_time = time.time()
    
    # Simulate processing time
    time.sleep(random.uniform(1.0, 3.0))
    processing_time = time.time() - start_time
    
    # Analyze message complexity
    message_length = len(request.content)
    complexity_score = min(1.0, message_length / 200)
    
    # Determine if swarm intelligence should activate
    swarm_activated = complexity_score > 0.7 or "urgent" in request.content.lower() or "complex" in request.content.lower()
    
    # Determine if collective intelligence is needed
    collective_intelligence = "refund" in request.content.lower() and "policy" in request.content.lower()
    
    # Generate contextual responses with follow-up questions
    content = request.content.lower()
    is_follow_up = request.is_follow_up

    if "money" in content or "refund" in content:
        response_text = "I understand you're concerned about your money. Let me help you with that refund request. Can you please provide your order number so I can look into this immediately?"
        follow_ups = ["What was your order number?", "When did you place the order?", "What payment method did you use?"]
        emotional_state = "concerned"
        empathy_applied = 0.9
    elif "idiot" in content or "stupid" in content:
        response_text = "I apologize if you've had a frustrating experience. I'm here to help resolve your issue. Could you please tell me more about what's happening so I can assist you better?"
        follow_ups = ["What specific issue are you experiencing?", "How can I make this right for you?", "Would you like me to escalate this to a supervisor?"]
        emotional_state = "angry"
        empathy_applied = 0.9
    elif is_follow_up:
        response_text = "Thank you for that additional information. Based on what you've told me, I can help you further. Is there anything else you'd like me to clarify or assist with?"
        follow_ups = ["Is there anything else I can help with?", "Would you like me to send you a confirmation email?", "How would you rate your experience today?"]
        emotional_state = "helpful"
        empathy_applied = 0.8
    elif "angry" in content or "frustrated" in content:
        response_text = "I sincerely apologize for the frustration you're experiencing. I understand how important this is to you, and I'm here to help resolve this issue immediately."
        follow_ups = ["Can you tell me more about what happened?", "What would be the best way to resolve this?", "Would you like me to escalate this?"]
        emotional_state = "angry"
        empathy_applied = 0.9
    elif "technical" in content or "error" in content:
        response_text = "I understand you're experiencing a technical issue. Let me connect you with our technical specialist who can provide detailed assistance."
        follow_ups = ["What error message are you seeing?", "When did this issue start?", "Have you tried restarting?"]
        emotional_state = "concerned"
        empathy_applied = 0.7
    elif "billing" in content or "charge" in content:
        response_text = "I see you have a billing inquiry. I'll review your account details and ensure any discrepancies are resolved quickly."
        follow_ups = ["What specific charge are you questioning?", "Can you provide your account number?", "When did you notice this charge?"]
        emotional_state = "neutral"
        empathy_applied = 0.6
    else:
        response_text = "Thank you for contacting us. I've carefully reviewed your request and I'm here to provide you with the best possible assistance."
        follow_ups = ["Can you provide more details?", "When did this issue start?", "Have you tried any troubleshooting steps?"]
        emotional_state = "neutral"
        empathy_applied = 0.7
    
    # Brain state updates
    brain_updates = {
        "emotional_analysis": {
            "sentiment": "negative" if any(word in request.content.lower() for word in ["angry", "frustrated", "terrible"]) else "neutral",
            "intensity": complexity_score,
            "empathy_applied": empathy_applied
        },
        "swarm_activated": swarm_activated,
        "collective_intelligence": collective_intelligence,
        "consciousness_evolution": {
            "agent_alpha": random.uniform(0.01, 0.03),
            "agent_beta": random.uniform(0.005, 0.02),
            "agent_gamma": random.uniform(0.01, 0.025),
            "agent_delta": random.uniform(0.005, 0.015)
        }
    }
    
    return ProcessingResponse(
        workflow_id=f"workflow_{int(time.time())}_{random.randint(1000, 9999)}",
        processing_time=processing_time,
        agents_involved=["intake", "knowledge", "resolution", "quality"],
        final_response=response_text,
        brain_state_updates=brain_updates,
        quality_score=random.uniform(0.85, 0.95),
        escalation_needed=swarm_activated,
        swarm_activated=swarm_activated,
        collective_intelligence=collective_intelligence,
        follow_up_questions=follow_ups,
        conversation_continues=True,
        conversation_id=request.conversation_id or f"conv_{int(time.time())}"
    )

# API Routes

@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint with API information."""
    return """
    <html>
        <head>
            <title>🧠 Smart Customer Support Orchestrator API</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1 { color: #1f77b4; text-align: center; }
                .feature { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .endpoint { background: #e3f2fd; padding: 10px; margin: 5px 0; border-radius: 3px; font-family: monospace; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🧠 Smart Customer Support Orchestrator API</h1>
                <p><strong>Agent Development Kit (ADK) Hackathon Submission</strong></p>
                
                <h2>🚀 Features</h2>
                <div class="feature">
                    <strong>🧠 AI Consciousness:</strong> Individual agent brains with consciousness evolution
                </div>
                <div class="feature">
                    <strong>🌊 Swarm Intelligence:</strong> Dynamic agent spawning for complex problems
                </div>
                <div class="feature">
                    <strong>🤝 Collective Intelligence:</strong> Multi-agent consensus building
                </div>
                <div class="feature">
                    <strong>💭 Emotional Intelligence:</strong> Advanced empathy with contagion resistance
                </div>
                
                <h2>📡 API Endpoints</h2>
                <div class="endpoint">POST /process-message - Process customer support requests</div>
                <div class="endpoint">GET /brain-states - Get current agent brain states</div>
                <div class="endpoint">GET /brain-states/{agent_id} - Get specific agent brain state</div>
                <div class="endpoint">GET /health - API health check</div>
                <div class="endpoint">GET /docs - Interactive API documentation</div>
                
                <h2>🎯 Frontend</h2>
                <p>Access the Streamlit frontend at: <a href="http://localhost:8501">http://localhost:8501</a></p>
                
                <h2>📚 Documentation</h2>
                <p>Interactive API docs: <a href="/docs">/docs</a></p>
                <p>ReDoc documentation: <a href="/redoc">/redoc</a></p>
                
                <p style="text-align: center; margin-top: 40px; color: #666;">
                    Built with ❤️ for the Agent Development Kit Hackathon | #adkhackathon
                </p>
            </div>
        </body>
    </html>
    """

@app.post("/process-message", response_model=ProcessingResponse)
async def process_customer_message(request: CustomerMessageRequest):
    """Process a customer support message through the multi-agent system."""
    try:
        if False:  # Temporarily disable real orchestrator to test interactive features
            # Use real orchestrator
            customer_message = CustomerMessage(
                message_id=f"msg_{int(time.time())}",
                customer_id=request.customer_id,
                content=request.content,
                channel=request.channel,
                metadata=request.metadata
            )
            
            ticket = await orchestrator.process_customer_message(customer_message)

            # Extract result data from ticket
            agents_involved = ["intake", "knowledge", "resolution", "quality"]

            # Check if account agent was used (for voice/account requests)
            if hasattr(ticket, 'adk_metadata') and ticket.adk_metadata:
                adk_agents = ticket.adk_metadata.get('agents_used', [])
                if 'account' in adk_agents:
                    agents_involved.insert(1, "account")  # Insert after intake

            result = {
                "workflow_id": f"workflow_{int(time.time())}_{random.randint(1000, 9999)}",
                "processing_time": ticket.total_processing_time,
                "agents_involved": agents_involved,
                "final_response": ticket.final_response,
                "brain_state_updates": {
                    "emotional_analysis": {
                        "sentiment": ticket.intake_result.sentiment.value if ticket.intake_result else "neutral",
                        "intensity": random.uniform(0.1, 0.9),
                        "empathy_applied": 0.7
                    },
                    "swarm_activated": False,
                    "collective_intelligence": False,
                    "consciousness_evolution": {
                        "agent_alpha": random.uniform(0.01, 0.03),
                        "agent_beta": random.uniform(0.01, 0.03),
                        "agent_gamma": random.uniform(0.01, 0.03),
                        "agent_delta": random.uniform(0.01, 0.03)
                    }
                },
                "quality_score": ticket.quality_result.quality_score if ticket.quality_result else 0.9,
                "escalation_needed": ticket.escalated,
                "swarm_activated": False,
                "collective_intelligence": False,
                "follow_up_questions": ["Can you provide more details?", "Is there anything else I can help with?"],
                "conversation_continues": True,
                "conversation_id": request.conversation_id or f"conv_{int(time.time())}"
            }
            
            # Convert result to response format
            return ProcessingResponse(
                workflow_id=result.get("workflow_id", f"workflow_{int(time.time())}"),
                processing_time=result.get("processing_time", 2.0),
                agents_involved=result.get("agents_involved", ["intake", "knowledge", "resolution", "quality"]),
                final_response=result.get("final_response", "Thank you for your message. We're here to help!"),
                brain_state_updates=result.get("brain_state_updates", {}),
                quality_score=result.get("quality_score", 0.9),
                escalation_needed=result.get("escalation_needed", False),
                swarm_activated=result.get("swarm_activated", False),
                collective_intelligence=result.get("collective_intelligence", False),
                follow_up_questions=result.get("follow_up_questions", ["Can you provide more details?", "Is there anything else I can help with?"]),
                conversation_continues=result.get("conversation_continues", True),
                conversation_id=result.get("conversation_id", request.conversation_id or f"conv_{int(time.time())}")
            )
        else:
            # Use mock response for demo
            return generate_mock_response(request)
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Processing error: {str(e)}")

@app.get("/brain-states")
async def get_all_brain_states():
    """Get current brain states for all agents."""
    try:
        # Add some random variation for demo
        current_states = {}
        for agent_id, state in mock_brain_states.items():
            current_states[agent_id] = {
                **state,
                "consciousness_level": min(1.0, state["consciousness_level"] + random.uniform(-0.02, 0.02)),
                "stress_level": max(0.0, min(1.0, state["stress_level"] + random.uniform(-0.05, 0.05))),
                "last_updated": datetime.now()
            }
        
        return current_states
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving brain states: {str(e)}")

@app.get("/brain-states/{agent_id}", response_model=BrainStateResponse)
async def get_agent_brain_state(agent_id: str):
    """Get brain state for a specific agent."""
    if agent_id not in mock_brain_states:
        raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")
    
    try:
        state = mock_brain_states[agent_id]
        return BrainStateResponse(
            agent_id=agent_id,
            consciousness_level=state["consciousness_level"],
            emotional_state=state["emotional_state"],
            stress_level=state["stress_level"],
            empathy_level=state["empathy_level"],
            learning_mode=state["learning_mode"],
            working_memory=state["working_memory"],
            last_updated=datetime.now()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving brain state: {str(e)}")

@app.get("/health")
async def health_check():
    """API health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "orchestrator_available": ORCHESTRATOR_AVAILABLE,
        "brain_system": "active",
        "swarm_intelligence": "ready",
        "collective_intelligence": "standby",
        "api_version": "1.0.0"
    }

@app.post("/test-account-agent")
async def test_account_agent(request: CustomerMessageRequest):
    """Direct test endpoint for account agent with BigQuery integration."""
    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

        from src.agents.account_agent import AccountAgent
        from src.models import AgentContext, CustomerMessage

        # Create customer message
        customer_message = CustomerMessage(
            message_id=f"account_test_{int(time.time())}",
            customer_id=request.customer_id,
            content=request.content,
            channel=request.channel,
            metadata=request.metadata
        )

        # Create context
        context = AgentContext(
            message_id=customer_message.message_id,
            customer_message=customer_message
        )

        # Process with account agent
        account_agent = AccountAgent()
        result_context = await account_agent.process(context)

        # Extract response
        if hasattr(result_context, 'account_info') and result_context.account_info:
            account_info = result_context.account_info
            if account_info.get('found'):
                final_response = account_info.get('message', 'Account information retrieved.')
                agents_involved = ["account"]
            else:
                final_response = account_info.get('message', 'Account information not found.')
                agents_involved = ["account"]
        else:
            final_response = "I'm sorry, I couldn't process your account request right now."
            agents_involved = ["account"]

        return ProcessingResponse(
            workflow_id=f"account_test_{int(time.time())}_{random.randint(1000, 9999)}",
            processing_time=0.5,
            agents_involved=agents_involved,
            final_response=final_response,
            brain_state_updates={
                "emotional_analysis": {"sentiment": "neutral", "intensity": 0.1, "empathy_applied": 0.7},
                "swarm_activated": False,
                "collective_intelligence": False,
                "consciousness_evolution": {"agent_alpha": 0.01, "agent_beta": 0.01, "agent_gamma": 0.01, "agent_delta": 0.01}
            },
            quality_score=0.95,
            escalation_needed=False,
            swarm_activated=False,
            collective_intelligence=False,
            follow_up_questions=["Is there anything else I can help you with?"],
            conversation_continues=True,
            conversation_id=f"account_test_{int(time.time())}"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Account agent test error: {str(e)}")

@app.post("/voice/text-to-speech")
async def text_to_speech(request: dict):
    """Convert text to high-quality speech using Google Cloud or OpenAI."""
    try:
        text = request.get("text", "")
        voice_config = request.get("voice_config", {})

        if not text:
            raise HTTPException(status_code=400, detail="Text is required")

        # Try to use voice service
        try:
            from src.services.voice_service import voice_service
            audio_data = await voice_service.text_to_speech(text, voice_config)

            from fastapi.responses import Response
            return Response(
                content=audio_data,
                media_type="audio/mpeg",
                headers={
                    "Content-Disposition": "attachment; filename=speech.mp3",
                    "Cache-Control": "no-cache"
                }
            )
        except Exception as voice_error:
            # If voice service fails, return error for fallback
            raise HTTPException(status_code=503, detail=f"Voice synthesis unavailable: {str(voice_error)}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Text-to-speech error: {str(e)}")

@app.get("/demo/scenarios")
async def get_demo_scenarios():
    """Get predefined demo scenarios for testing."""
    scenarios = [
        {
            "name": "Angry Customer - Billing Issue",
            "customer_id": "CUST_ANGRY_001",
            "content": "I am absolutely furious! You charged me twice for the same service and your support team has been completely useless. I want a full refund immediately!",
            "channel": "phone",
            "expected_features": ["emotional_intelligence", "de_escalation", "empathy"]
        },
        {
            "name": "Complex Technical Issue",
            "customer_id": "CUST_TECH_001", 
            "content": "My API integration is failing with error code 500 when trying to authenticate. I've checked the documentation and my credentials are correct. This is blocking our production deployment and affecting multiple customers.",
            "channel": "api",
            "expected_features": ["swarm_intelligence", "technical_expertise", "escalation"]
        },
        {
            "name": "Policy Exception Request",
            "customer_id": "CUST_POLICY_001",
            "content": "I want a full refund for a service I used for 11 months out of a 12-month contract. I'm claiming it never worked properly despite no previous complaints. This should be covered under your satisfaction guarantee.",
            "channel": "email",
            "expected_features": ["collective_intelligence", "policy_analysis", "consensus_building"]
        },
        {
            "name": "Happy Customer Feedback",
            "customer_id": "CUST_HAPPY_001",
            "content": "I just wanted to say thank you so much for the excellent service! Your team resolved my issue quickly and professionally. This is why I love being your customer!",
            "channel": "web",
            "expected_features": ["positive_reinforcement", "satisfaction_tracking"]
        }
    ]
    return {"scenarios": scenarios}

@app.post("/demo/{scenario_name}")
async def run_demo_scenario(scenario_name: str):
    """Run a predefined demo scenario."""
    scenarios = await get_demo_scenarios()
    scenario = next((s for s in scenarios["scenarios"] if s["name"].lower().replace(" ", "_").replace("-", "_") == scenario_name.lower()), None)
    
    if not scenario:
        raise HTTPException(status_code=404, detail=f"Demo scenario '{scenario_name}' not found")
    
    request = CustomerMessageRequest(
        customer_id=scenario["customer_id"],
        content=scenario["content"],
        channel=scenario["channel"],
        metadata={"demo_scenario": scenario_name, "expected_features": scenario["expected_features"]}
    )
    
    return await process_customer_message(request)

# Run the application
if __name__ == "__main__":
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
