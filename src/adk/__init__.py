"""
Google Cloud Agent Development Kit (ADK) Integration Package
Advanced agent framework for enterprise customer support
"""

from .agent_framework import (
    ADKAgent,
    ADKOrchestrator, 
    ADKFactory,
    ADKMessage,
    ADKAgentConfig,
    AgentType,
    AgentCapability
)

from .innovative_agents import (
    EmotionalIntelligenceAgent,
    PredictiveAnalyticsAgent,
    MultiModalAgent,
    CollaborativeAgent,
    AdaptiveLearningAgent
)

from .google_cloud_integration import (
    CloudServicesManager,
    VertexAIManager,
    BigQueryManager,
    CloudFunctionsManager,
    WorkflowsManager
)

__all__ = [
    # Core ADK Framework
    "ADKAgent",
    "ADKOrchestrator",
    "ADKFactory", 
    "ADKMessage",
    "ADKAgentConfig",
    "AgentType",
    "AgentCapability",
    
    # Innovative Agents
    "EmotionalIntelligenceAgent",
    "PredictiveAnalyticsAgent", 
    "MultiModalAgent",
    "CollaborativeAgent",
    "AdaptiveLearningAgent",
    
    # Google Cloud Integration
    "CloudServicesManager",
    "VertexAIManager",
    "BigQueryManager",
    "CloudFunctionsManager",
    "WorkflowsManager"
]
