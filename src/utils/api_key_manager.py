"""API Key Manager for handling multiple Google API keys with load balancing and rate limiting."""

import time
import random
import asyncio
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import structlog

from ..config import settings


class APIKeyManager:
    """Manages multiple Google API keys with intelligent load balancing."""
    
    def __init__(self):
        self.logger = structlog.get_logger().bind(component="api_key_manager")
        self.api_keys = settings.get_google_api_keys()
        
        # Track usage for each key
        self.key_stats = {}
        for key in self.api_keys:
            self.key_stats[key] = {
                "requests_count": 0,
                "last_used": None,
                "rate_limited_until": None,
                "errors": 0,
                "success_count": 0
            }
        
        self.current_key_index = 0
        self.logger.info(f"Initialized with {len(self.api_keys)} API keys")
    
    def get_next_key(self) -> Optional[str]:
        """Get the next available API key using round-robin with rate limit awareness."""
        if not self.api_keys:
            return None
        
        # Filter out rate-limited keys
        available_keys = []
        now = datetime.utcnow()
        
        for key in self.api_keys:
            stats = self.key_stats[key]
            rate_limited_until = stats.get("rate_limited_until")
            
            if rate_limited_until is None or now > rate_limited_until:
                available_keys.append(key)
        
        if not available_keys:
            # All keys are rate limited, return the one that expires soonest
            soonest_key = min(
                self.api_keys,
                key=lambda k: self.key_stats[k].get("rate_limited_until", datetime.min)
            )
            self.logger.warning("All API keys rate limited, using soonest to expire", key_suffix=soonest_key[-8:])
            return soonest_key
        
        # Use round-robin among available keys
        if self.current_key_index >= len(available_keys):
            self.current_key_index = 0
        
        selected_key = available_keys[self.current_key_index]
        self.current_key_index = (self.current_key_index + 1) % len(available_keys)
        
        # Update usage stats
        self.key_stats[selected_key]["requests_count"] += 1
        self.key_stats[selected_key]["last_used"] = now
        
        self.logger.debug("Selected API key", key_suffix=selected_key[-8:], available_keys=len(available_keys))
        return selected_key
    
    def mark_rate_limited(self, api_key: str, retry_after_seconds: int = 60):
        """Mark an API key as rate limited."""
        if api_key in self.key_stats:
            rate_limited_until = datetime.utcnow() + timedelta(seconds=retry_after_seconds)
            self.key_stats[api_key]["rate_limited_until"] = rate_limited_until
            self.key_stats[api_key]["errors"] += 1
            
            self.logger.warning(
                "API key marked as rate limited",
                key_suffix=api_key[-8:],
                retry_after_seconds=retry_after_seconds
            )
    
    def mark_success(self, api_key: str):
        """Mark a successful API call."""
        if api_key in self.key_stats:
            self.key_stats[api_key]["success_count"] += 1
    
    def mark_error(self, api_key: str):
        """Mark an API error (non-rate-limit)."""
        if api_key in self.key_stats:
            self.key_stats[api_key]["errors"] += 1
    
    def get_stats(self) -> Dict:
        """Get usage statistics for all API keys."""
        stats = {
            "total_keys": len(self.api_keys),
            "available_keys": 0,
            "rate_limited_keys": 0,
            "key_details": {}
        }
        
        now = datetime.utcnow()
        
        for i, key in enumerate(self.api_keys):
            key_stats = self.key_stats[key]
            rate_limited_until = key_stats.get("rate_limited_until")
            is_rate_limited = rate_limited_until and now < rate_limited_until
            
            if is_rate_limited:
                stats["rate_limited_keys"] += 1
            else:
                stats["available_keys"] += 1
            
            stats["key_details"][f"key_{i+1}"] = {
                "key_suffix": key[-8:],
                "requests_count": key_stats["requests_count"],
                "success_count": key_stats["success_count"],
                "errors": key_stats["errors"],
                "last_used": key_stats["last_used"].isoformat() if key_stats["last_used"] else None,
                "rate_limited": is_rate_limited,
                "rate_limited_until": rate_limited_until.isoformat() if rate_limited_until else None
            }
        
        return stats
    
    def get_best_key(self) -> Optional[str]:
        """Get the API key with the best performance (lowest error rate)."""
        if not self.api_keys:
            return None
        
        # Filter available keys and sort by success rate
        now = datetime.utcnow()
        available_keys = []
        
        for key in self.api_keys:
            stats = self.key_stats[key]
            rate_limited_until = stats.get("rate_limited_until")
            
            if rate_limited_until is None or now > rate_limited_until:
                total_requests = stats["requests_count"]
                success_rate = stats["success_count"] / max(total_requests, 1)
                available_keys.append((key, success_rate))
        
        if not available_keys:
            return self.get_next_key()  # Fallback to round-robin
        
        # Sort by success rate (descending)
        available_keys.sort(key=lambda x: x[1], reverse=True)
        best_key = available_keys[0][0]
        
        # Update usage stats
        self.key_stats[best_key]["requests_count"] += 1
        self.key_stats[best_key]["last_used"] = now
        
        return best_key


# Global instance
api_key_manager = APIKeyManager()
