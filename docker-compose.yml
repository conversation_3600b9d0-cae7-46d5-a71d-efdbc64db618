version: '3.8'

services:
  orchestrator:
    build: .
    ports:
      - "8080:8080"
    environment:
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}
      - GOOGLE_CLOUD_REGION=${GOOGLE_CLOUD_REGION:-us-central1}
      - VERTEX_AI_LOCATION=${VERTEX_AI_LOCATION:-us-central1}
      - BIGQUERY_DATASET=${BIGQUERY_DATASET:-customer_support}
      - STORAGE_BUCKET=${STORAGE_BUCKET}
      - STORAGE_BUCKET_DOCUMENTS=${STORAGE_BUCKET_DOCUMENTS}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
    volumes:
      - ~/.config/gcloud:/home/<USER>/.config/gcloud:ro
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - orchestrator
    restart: unless-stopped
    profiles:
      - production

volumes:
  logs:
    driver: local
