# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_REGION=us-central1
VERTEX_AI_LOCATION=us-central1

# Google AI API Keys (get from https://makersuite.google.com/app/apikey)
GOOGLE_API_KEY_1=your-first-api-key
GOOGLE_API_KEY_2=your-second-api-key
GOOGLE_API_KEY_3=your-third-api-key

# BigQuery Configuration (optional)
BIGQUERY_DATASET=customer_support
BIGQUERY_TABLE_KNOWLEDGE=knowledge_base
BIGQUERY_TABLE_TICKETS=support_tickets
BIGQUERY_TABLE_RESPONSES=agent_responses

# Cloud Storage Configuration (optional)
STORAGE_BUCKET=your-support-bucket
STORAGE_BUCKET_DOCUMENTS=your-documents-bucket

# Vertex AI Models
VERTEX_AI_MODEL_INTAKE=gemini-1.5-flash
VERTEX_AI_MODEL_KNOWLEDGE=gemini-1.5-flash
VERTEX_AI_MODEL_RESOLUTION=gemini-1.5-flash
VERTEX_AI_MODEL_QUALITY=gemini-1.5-flash

# Agent Configuration
INTAKE_AGENT_TEMPERATURE=0.3
KNOWLEDGE_AGENT_SEARCH_LIMIT=10
RESOLUTION_AGENT_MAX_LENGTH=500
QUALITY_AGENT_MIN_SCORE=0.85

# API Configuration
API_HOST=0.0.0.0
API_PORT=8080
API_WORKERS=4

# Logging
LOG_LEVEL=INFO
STRUCTURED_LOGGING=true

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Development
DEBUG=false
DEVELOPMENT_MODE=false
