#!/usr/bin/env python3
"""
Database setup script for Smart Customer Support Orchestrator
Creates BigQuery tables and inserts sample data
"""

import os
import sys
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import time

# Your project configuration
PROJECT_ID = "pro-course-433716-v0"
DATASET_ID = "customer_support"
LOCATION = "us-central1"

def create_client():
    """Create BigQuery client."""
    try:
        client = bigquery.Client(project=PROJECT_ID)
        print(f"✅ Connected to BigQuery project: {PROJECT_ID}")
        return client
    except Exception as e:
        print(f"❌ Error connecting to BigQuery: {e}")
        sys.exit(1)

def create_dataset(client):
    """Create the dataset if it doesn't exist."""
    dataset_id = f"{PROJECT_ID}.{DATASET_ID}"
    
    try:
        client.get_dataset(dataset_id)
        print(f"✅ Dataset {dataset_id} already exists")
    except NotFound:
        dataset = bigquery.Dataset(dataset_id)
        dataset.location = LOCATION
        dataset.description = "Customer support data for AI agent system"
        
        dataset = client.create_dataset(dataset, timeout=30)
        print(f"✅ Created dataset {dataset.dataset_id}")

def execute_sql_file(client, filename, description):
    """Execute SQL commands from a file."""
    print(f"\n📄 Executing {description}...")
    
    try:
        with open(filename, 'r') as file:
            sql_content = file.read()
        
        # Split by semicolon and execute each statement
        statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        for i, statement in enumerate(statements):
            if statement:
                try:
                    print(f"   Executing statement {i+1}/{len(statements)}...")
                    job = client.query(statement)
                    job.result()  # Wait for completion
                    print(f"   ✅ Statement {i+1} completed")
                except Exception as e:
                    print(f"   ⚠️  Statement {i+1} error: {e}")
                    # Continue with other statements
        
        print(f"✅ {description} completed")
        
    except FileNotFoundError:
        print(f"❌ File {filename} not found")
    except Exception as e:
        print(f"❌ Error executing {filename}: {e}")

def verify_tables(client):
    """Verify that all tables were created successfully."""
    print(f"\n🔍 Verifying table creation...")
    
    expected_tables = [
        'knowledge_base',
        'support_tickets', 
        'agent_responses',
        'customer_profiles',
        'faqs',
        'system_metrics',
        'escalation_rules',
        'training_data'
    ]
    
    dataset_ref = client.dataset(DATASET_ID)
    
    for table_name in expected_tables:
        try:
            table_ref = dataset_ref.table(table_name)
            table = client.get_table(table_ref)
            
            # Get row count
            query = f"SELECT COUNT(*) as row_count FROM `{PROJECT_ID}.{DATASET_ID}.{table_name}`"
            result = client.query(query).result()
            row_count = list(result)[0].row_count
            
            print(f"   ✅ {table_name}: {len(table.schema)} columns, {row_count} rows")
            
        except NotFound:
            print(f"   ❌ {table_name}: Table not found")
        except Exception as e:
            print(f"   ⚠️  {table_name}: Error checking table - {e}")

def run_sample_queries(client):
    """Run some sample queries to test the setup."""
    print(f"\n🧪 Running sample queries...")
    
    sample_queries = [
        {
            "name": "Total tickets by priority",
            "query": f"""
                SELECT priority, COUNT(*) as count 
                FROM `{PROJECT_ID}.{DATASET_ID}.support_tickets` 
                GROUP BY priority 
                ORDER BY count DESC
            """
        },
        {
            "name": "Knowledge base categories",
            "query": f"""
                SELECT category, COUNT(*) as document_count 
                FROM `{PROJECT_ID}.{DATASET_ID}.knowledge_base` 
                GROUP BY category
            """
        },
        {
            "name": "Customer satisfaction average",
            "query": f"""
                SELECT 
                    ROUND(AVG(customer_satisfaction_score), 2) as avg_satisfaction,
                    COUNT(*) as total_tickets
                FROM `{PROJECT_ID}.{DATASET_ID}.support_tickets`
                WHERE customer_satisfaction_score IS NOT NULL
            """
        }
    ]
    
    for query_info in sample_queries:
        try:
            print(f"   Running: {query_info['name']}")
            results = client.query(query_info['query']).result()
            
            for row in results:
                print(f"     {dict(row)}")
            
            print(f"   ✅ {query_info['name']} completed")
            
        except Exception as e:
            print(f"   ❌ {query_info['name']} failed: {e}")

def main():
    """Main setup function."""
    print("🚀 Smart Customer Support Orchestrator - Database Setup")
    print("=" * 60)
    print(f"Project: {PROJECT_ID}")
    print(f"Dataset: {DATASET_ID}")
    print(f"Location: {LOCATION}")
    print("=" * 60)
    
    # Create BigQuery client
    client = create_client()
    
    # Create dataset
    create_dataset(client)
    
    # Execute table creation
    execute_sql_file(client, 'sql/create_tables.sql', 'Table Creation')
    
    # Insert sample data
    execute_sql_file(client, 'sql/insert_sample_data.sql', 'Sample Data Insertion')
    
    # Verify setup
    verify_tables(client)
    
    # Run sample queries
    run_sample_queries(client)
    
    print(f"\n🎉 Database setup completed successfully!")
    print(f"\nNext steps:")
    print(f"1. Test queries in BigQuery console:")
    print(f"   SELECT * FROM `{PROJECT_ID}.{DATASET_ID}.support_tickets` LIMIT 10")
    print(f"2. Run the application:")
    print(f"   python main.py --mode demo")
    print(f"3. Start the API server:")
    print(f"   python main.py --mode api")
    
    print(f"\n📊 Useful queries are available in: sql/useful_queries.sql")

if __name__ == "__main__":
    main()
