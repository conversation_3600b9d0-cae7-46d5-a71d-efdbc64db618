# Frontend Requirements for AI Customer Support System
# Streamlit-based frontend application

# Core Streamlit framework
streamlit>=1.28.0

# HTTP requests for API communication
requests>=2.31.0

# JSON handling (usually included with Python, but explicit for clarity)
# json - built-in module

# Time and datetime utilities (built-in modules)
# time - built-in module
# datetime - built-in module

# Optional: Enhanced UI components
streamlit-option-menu>=0.3.6

# Optional: Charts and visualization (if needed for analytics)
plotly>=5.17.0
pandas>=2.1.0

# Optional: Additional Streamlit components
# streamlit-extras>=0.3.0

# Optional: Session state management (built into modern Streamlit)
# streamlit-session-state - not needed for Streamlit 1.28+

# Development and debugging
# streamlit-debug>=0.1.0  <-- removed (no package exists on PyPI)
